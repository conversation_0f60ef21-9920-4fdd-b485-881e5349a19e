package com.example.captain.dto;

import lombok.Data;

/**
 * 产品查询参数
 */
@Data
public class ProductQueryParam {
    /**
     * 页码
     */
    private Integer page = 1;

    /**
     * 每页记录数
     */
    private Integer size = 10;

    /**
     * 事件ID
     */
    private Long eventId;

    /**
     * 父商品ASIN
     */
    private String parentAsin;

    /**
     * 是否为主商品过滤条件
     */
    private Boolean isMainProduct;
}
