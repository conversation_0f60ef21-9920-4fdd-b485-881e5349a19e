package com.example.captain.controller;

import com.example.captain.service.ProductService;
import com.example.common.model.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 产品回调控制器
 */
@RestController
@RequestMapping("/captain/callback")
@Slf4j
public class ProductCallbackController {

    private final ProductService productService;

    @Autowired
    public ProductCallbackController(ProductService productService) {
        this.productService = productService;
    }

    /**
     * 处理产品数据回调
     *
     * @param eventId 事件ID
     * @param data 产品数据
     * @return 处理结果
     */
    @PostMapping("/{eventId}/product")
    public ApiResponse<Map<String, Object>> handleProductCallback(
            @PathVariable(name = "eventId") Long eventId,
            @RequestHeader(value = "Authorization", required = false) String authorization,
            @RequestBody Map<String, Object> data) {

        log.info("收到产品数据回调: eventId={}", eventId);

        // 验证JWT令牌
        if (authorization == null || !authorization.startsWith("Bearer ")) {
            log.warn("无效的JWT令牌");
            return ApiResponse.error(401, "无效的JWT令牌");
        }

        // 获取产品数据列表
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> productInfoList = (List<Map<String, Object>>) data.get("products");

        if (productInfoList == null || productInfoList.isEmpty()) {
            log.warn("产品数据为空");
            return ApiResponse.error(400, "产品数据为空");
        }

        // 保存产品数据
        int count = productService.saveProducts(eventId, productInfoList);

        log.info("成功保存{}个产品", count);

        return ApiResponse.success(Map.of(
            "count", count,
            "message", "产品数据处理成功"
        ));
    }
}
