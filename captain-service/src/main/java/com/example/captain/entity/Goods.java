package com.example.captain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Amazon待发布商品实体类
 * 用于存储准备发布到Amazon的商品数据
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "goods", autoResultMap = true)
public class Goods {

    /**
     * 商品ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 关联的事件ID
     */
    @TableField("event_id")
    private Long eventId;

    /**
     * 关联的Amazon商品ASIN
     */
    @TableField("amazon_asin")
    private String amazonAsin;

    /**
     * 关联的1688货源ID
     */
    @TableField("supply_id")
    private Long supplyId;

    /**
     * 产品类型
     */
    @TableField("feed_product_type")
    private String feedProductType;

    /**
     * 卖家SKU
     */
    @TableField("item_sku")
    private String itemSku;

    /**
     * 品牌名称
     */
    @TableField("brand_name")
    private String brandName;

    /**
     * 外部产品ID(ASIN)
     */
    @TableField("external_product_id")
    private String externalProductId;

    /**
     * 外部产品ID类型
     */
    @TableField("external_product_id_type")
    private String externalProductIdType;

    /**
     * 商品名称
     */
    @TableField("item_name")
    private String itemName;

    /**
     * 制造商
     */
    @TableField("manufacturer")
    private String manufacturer;

    /**
     * 推荐浏览节点
     */
    @TableField("recommended_browse_nodes")
    private String recommendedBrowseNodes;

    /**
     * 最大订购数量
     */
    @TableField("max_order_quantity")
    private Integer maxOrderQuantity;

    /**
     * 原产国
     */
    @TableField("country_of_origin")
    private String countryOfOrigin;

    /**
     * 标准价格
     */
    @TableField("standard_price")
    private BigDecimal standardPrice;

    /**
     * 库存数量
     */
    @TableField("quantity")
    private Integer quantity;

    /**
     * 主图片URL
     */
    @TableField("main_image_url")
    private String mainImageUrl;

    /**
     * 其他图片URL1
     */
    @TableField("other_image_url1")
    private String otherImageUrl1;

    /**
     * 其他图片URL2
     */
    @TableField("other_image_url2")
    private String otherImageUrl2;

    /**
     * 其他图片URL3
     */
    @TableField("other_image_url3")
    private String otherImageUrl3;

    /**
     * 其他图片URL4
     */
    @TableField("other_image_url4")
    private String otherImageUrl4;

    /**
     * 其他图片URL5
     */
    @TableField("other_image_url5")
    private String otherImageUrl5;

    /**
     * 其他图片URL6
     */
    @TableField("other_image_url6")
    private String otherImageUrl6;

    /**
     * 其他图片URL7
     */
    @TableField("other_image_url7")
    private String otherImageUrl7;

    /**
     * 其他图片URL8
     */
    @TableField("other_image_url8")
    private String otherImageUrl8;

    /**
     * 样本图片URL
     */
    @TableField("swatch_image_url")
    private String swatchImageUrl;

    /**
     * 父子关系(parent/child)
     */
    @TableField("parent_child")
    private String parentChild;

    /**
     * 父SKU
     */
    @TableField("parent_sku")
    private String parentSku;

    /**
     * 关系类型
     */
    @TableField("relationship_type")
    private String relationshipType;

    /**
     * 变体主题
     */
    @TableField("variation_theme")
    private String variationTheme;

    /**
     * 更新删除标志
     */
    @TableField("update_delete")
    private String updateDelete;

    /**
     * 产品描述
     */
    @TableField("product_description")
    private String productDescription;

    /**
     * 制造商零件号
     */
    @TableField("part_number")
    private String partNumber;

    /**
     * 关键产品特性1
     */
    @TableField("bullet_point1")
    private String bulletPoint1;

    /**
     * 关键产品特性2
     */
    @TableField("bullet_point2")
    private String bulletPoint2;

    /**
     * 关键产品特性3
     */
    @TableField("bullet_point3")
    private String bulletPoint3;

    /**
     * 关键产品特性4
     */
    @TableField("bullet_point4")
    private String bulletPoint4;

    /**
     * 关键产品特性5
     */
    @TableField("bullet_point5")
    private String bulletPoint5;

    /**
     * 搜索关键词
     */
    @TableField("generic_keywords")
    private String genericKeywords;

    /**
     * 金属类型
     */
    @TableField("metal_type")
    private String metalType;

    /**
     * 部门名称
     */
    @TableField("department_name")
    private String departmentName;

    /**
     * 宝石类型
     */
    @TableField("gem_type")
    private String gemType;

    /**
     * 特殊功能
     */
    @TableField("special_features")
    private String specialFeatures;

    /**
     * 颜色名称
     */
    @TableField("color_name")
    private String colorName;

    /**
     * 颜色映射
     */
    @TableField("color_map")
    private String colorMap;

    /**
     * 尺寸名称
     */
    @TableField("size_name")
    private String sizeName;

    /**
     * 网站运输重量
     */
    @TableField("website_shipping_weight")
    private BigDecimal websiteShippingWeight;

    /**
     * 网站运输重量单位
     */
    @TableField("website_shipping_weight_unit_of_measure")
    private String websiteShippingWeightUnitOfMeasure;

    /**
     * 尺寸映射
     */
    @TableField("size_map")
    private String sizeMap;

    /**
     * 单位数量
     */
    @TableField("unit_count")
    private Integer unitCount;

    /**
     * 单位数量类型
     */
    @TableField("unit_count_type")
    private String unitCountType;

    /**
     * 深度(前后)
     */
    @TableField("depth_front_to_back")
    private BigDecimal depthFrontToBack;

    /**
     * 深度单位
     */
    @TableField("depth_front_to_back_unit_of_measure")
    private String depthFrontToBackUnitOfMeasure;

    /**
     * 宽度(左右)
     */
    @TableField("depth_width_side_to_side")
    private BigDecimal depthWidthSideToSide;

    /**
     * 宽度单位
     */
    @TableField("depth_width_side_to_side_unit_of_measure")
    private String depthWidthSideToSideUnitOfMeasure;

    /**
     * 高度(底部到顶部)
     */
    @TableField("depth_height_floor_to_top")
    private BigDecimal depthHeightFloorToTop;

    /**
     * 高度单位
     */
    @TableField("depth_height_floor_to_top_unit_of_measure")
    private String depthHeightFloorToTopUnitOfMeasure;

    /**
     * 履行中心ID
     */
    @TableField("fulfillment_center_id")
    private String fulfillmentCenterId;

    /**
     * 货币
     */
    @TableField("currency")
    private String currency;

    /**
     * 标价
     */
    @TableField("list_price")
    private BigDecimal listPrice;

    /**
     * 含税标价
     */
    @TableField("list_price_with_tax")
    private BigDecimal listPriceWithTax;

    /**
     * 商品状态
     */
    @TableField("condition_type")
    private String conditionType;

    /**
     * 保修描述
     */
    @TableField("warranty_description")
    private String warrantyDescription;

    /**
     * 是否包含电池
     */
    @TableField("are_batteries_included")
    private String areBatteriesIncluded;

    /**
     * 是否需要电池
     */
    @TableField("batteries_required")
    private String batteriesRequired;

    /**
     * 危险品法规声明
     */
    @TableField("supplier_declared_dg_hz_regulation1")
    private String supplierDeclaredDgHzRegulation1;

    /**
     * 状态：0-待审核，1-已审核，2-已发布，3-已拒绝
     */
    @TableField("status")
    private Integer status;

    /**
     * 审核备注
     */
    @TableField("review_notes")
    private String reviewNotes;

    /**
     * 审核人ID
     */
    @TableField("reviewed_by")
    private Long reviewedBy;

    /**
     * 审核时间
     */
    @TableField("reviewed_at")
    private LocalDateTime reviewedAt;

    /**
     * 数据来源：1-自动生成，2-手工录入
     */
    @TableField("source_type")
    private Integer sourceType;

    /**
     * 生成规则记录
     */
    @TableField("generation_rules")
    private String generationRules;

    /**
     * 创建时间
     */
    @TableField("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField("updated_at")
    private LocalDateTime updatedAt;
}
