package com.example.captain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 1688货源实体类
 * 用于存储从1688抓取的商品数据
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "supplies", autoResultMap = true)
public class Supply {

    /**
     * 货源ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 关联的事件ID
     */
    @TableField("event_id")
    private Long eventId;

    /**
     * 关联的Amazon商品ASIN
     */
    @TableField("amazon_asin")
    private String amazonAsin;

    /**
     * 阿里巴巴商品ID
     */
    @TableField("object_id")
    private String objectId;

    /**
     * 商品标题
     */
    @TableField("title")
    private String title;

    /**
     * 商品价格
     */
    @TableField("price")
    private String price;

    /**
     * 商品详情页URL
     */
    @TableField("detail_url")
    private String detailUrl;

    /**
     * 商品图片URL
     */
    @TableField("image_url")
    private String imageUrl;

    /**
     * 本地保存的图片路径
     */
    @TableField("local_image_path")
    private String localImagePath;

    /**
     * 与Amazon商品的相似度
     */
    @TableField("similarity")
    private BigDecimal similarity;

    /**
     * 商品属性信息
     */
    @TableField("product_attributes")
    private String productAttributes;

    /**
     * 包装信息
     */
    @TableField("packaging_info")
    private String packagingInfo;

    /**
     * 开店年限
     */
    @TableField("tp_year")
    private String tpYear;

    /**
     * 公司名称
     */
    @TableField("company_name")
    private String companyName;

    /**
     * 公司档案URL
     */
    @TableField("tp_credit_url")
    private String tpCreditUrl;

    /**
     * 年销量
     */
    @TableField("sales_360_fuzzify")
    private String sales360Fuzzify;

    /**
     * 几件起批
     */
    @TableField("quantity_begin")
    private String quantityBegin;

    /**
     * 原始JSON数据
     */
    @TableField("raw_data")
    private String rawData;

    /**
     * 创建时间
     */
    @TableField("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField("updated_at")
    private LocalDateTime updatedAt;
}
