# Worker Service 使用指南

本文档介绍如何使用 Docker 容器运行 Worker Service，并通过 VNC 图形界面手动与浏览器交互。

## 前提条件

- Docker 已安装
- 基本的 Linux/命令行知识

## 快速开始

1. 确保您在 `worker-service` 目录下
2. 运行构建和启动脚本：

```bash
chmod +x build-and-run.sh
./build-and-run.sh
```

### 使用简化版 Dockerfile

如果您在使用标准 Dockerfile 时遇到 VNC 服务器启动问题，可以尝试使用简化版的 Dockerfile：

```bash
chmod +x build-and-run-simple.sh
./build-and-run-simple.sh
```

简化版使用 x11vnc 而不是 TigerVNC，通常更容易在 Docker 环境中设置。

### 使用独立版 Dockerfile（推荐）

如果您遇到 Maven 构建错误（如父 POM 解析问题），可以使用独立版的 Dockerfile，它使用简化的 pom.xml 文件，不依赖于父 POM：

```bash
chmod +x build-and-run-standalone.sh
./build-and-run-standalone.sh
```

独立版包含所有必要的依赖项，并且不需要父 POM 文件，是最可靠的运行方式。

### 使用开发模式（挂载卷）

如果您想在宿主机上编辑文件，同时在容器中编译和运行，可以使用开发模式：

```bash
chmod +x build-and-run-dev.sh
./build-and-run-dev.sh
```

开发模式将当前目录挂载到容器的 `/app` 目录，这样您可以在宿主机上编辑文件，而在容器中实时看到更改并编译运行。

#### 开发模式使用说明

1. 在宿主机上编辑源代码文件
2. 在容器中（通过 VNC 界面）打开终端，运行以下命令重新编译和运行：
   ```bash
   cd /app
   mvn clean compile exec:java -Dexec.mainClass="com.example.worker.service.AmazonService"
   ```
3. 如果您想保存会话，可以运行：
   ```bash
   /scripts/save-session.sh
   ```

**注意**：
- 容器启动时会自动备份原始的 `pom.xml` 文件（如果存在）为 `pom.xml.original`
- 然后会使用 `pom.simple.xml` 文件（如果存在）或者创建一个新的简化版 `pom.xml` 文件
- 这样可以确保项目能够成功编译，而不依赖于父 POM 文件
- 如果您想恢复原始的 pom.xml 文件，可以在容器中运行：
  ```bash
  cd /app
  mv pom.xml.original pom.xml
  ```

## 通过 VNC 访问容器

构建和运行脚本执行后，您可以通过以下两种方式访问容器中的图形界面：

1. **使用 VNC 客户端**：
   - 连接到 `localhost:5900`
   - 密码：`password`

2. **使用浏览器**：
   - 访问 `http://localhost:8080/vnc.html`
   - 点击 "Connect"
   - 输入密码 `password`

## 手动登录并保存会话文件

一旦连接到 VNC，您将看到一个图形界面。在这个界面中：

1. 应用程序会自动启动，并尝试打开亚马逊网站
2. 如果需要登录，您可以手动输入凭据并登录
3. 登录后，会话文件将自动保存在 `/app/src/main/resources/amazon_session.json`

### 使用保存会话脚本

我们提供了一个专门用于保存会话的脚本，您可以在 VNC 界面中打开终端，然后运行：

```bash
/app/save-session.sh
```

这将启动一个新的浏览器窗口，导航到亚马逊网站，并等待您手动登录。登录后，脚本会自动保存会话状态。

### 手动运行保存会话命令

如果您想直接使用 Maven 命令保存会话，可以在 VNC 界面中打开终端，然后运行：

```bash
cd /app
mvn exec:java -Dexec.mainClass="com.example.worker.service.AmazonService" -Dexec.args="save-session"
```

### 保存会话的工作原理

当您运行保存会话命令时：

1. 会打开一个新的浏览器窗口，导航到亚马逊网站
2. 您有 30 分钟的时间手动登录
3. 登录完成后，会话状态会自动保存到 `src/main/resources/amazon_session.json`
4. 这个文件包含了 cookies 和 localStorage 数据，可以用于后续的自动化操作

## 修改会话文件路径

如果您想修改会话文件的保存路径，可以编辑 `AmazonService.java` 文件中的以下行：

```java
String sessionFilePath = "src/main/resources/amazon_session.json";
```

## 故障排除

### VNC 连接问题

1. **无法连接到 VNC（Connection refused）**：
   - 如果您看到 `Failed to connect to localhost:5900: [Errno 111] Connection refused` 错误，这表明 VNC 服务器没有正确启动
   - 尝试在容器内运行备用的 VNC 启动脚本：
     ```bash
     /app/start-vnc-alternative.sh
     ```
   - 检查 VNC 服务器是否正在运行：
     ```bash
     netstat -tuln | grep 5900
     ```
   - 如果没有看到端口 5900 被监听，尝试手动启动 VNC 服务器：
     ```bash
     vncserver :0 -geometry 1920x1080 -depth 24 -localhost no
     ```

2. **VNC 连接超时**：
   - 确保端口 5900 和 8080 没有被其他应用程序占用
   - 检查防火墙设置
   - 尝试使用 VNC 客户端直接连接到容器的 IP 地址和端口 5900

3. **VNC 显示黑屏**：
   - 这可能是因为窗口管理器没有启动
   - 在 VNC 终端中运行：
     ```bash
     fluxbox &
     ```

### 其他问题

1. **浏览器无法启动**：
   - 在 VNC 终端中运行 `playwright install chromium` 确保浏览器已安装
   - 检查 Playwright 是否正确安装：
     ```bash
     mvn exec:java -Dexec.mainClass="com.microsoft.playwright.CLI" -Dexec.args="--help"
     ```

2. **会话文件未保存**：
   - 检查容器中的权限问题
   - 尝试手动保存会话文件
   - 确保目录存在：
     ```bash
     mkdir -p /app/src/main/resources
     ```

3. **容器启动后立即退出**：
   - 检查 Docker 日志以查看错误信息：
     ```bash
     docker logs $(docker ps -lq)
     ```
   - 尝试以交互式方式运行容器：
     ```bash
     docker run --rm -it --privileged -p 5900:5900 -p 8080:8080 worker-service-vnc /bin/bash
     ```

## 自定义 VNC 设置

如果您想自定义 VNC 设置，可以编辑 Dockerfile 中的相关部分：

```dockerfile
# 设置VNC密码
RUN mkdir -p /root/.vnc
RUN echo "password" | vncpasswd -f > /root/.vnc/passwd
RUN chmod 600 /root/.vnc/passwd

# 设置VNC启动脚本
RUN echo '#!/bin/bash\nxrdb $HOME/.Xresources\nstartfluxbox &' > /root/.vnc/xstartup
RUN chmod +x /root/.vnc/xstartup
```

### 关于 Fluxbox 警告信息

启动时可能会看到类似以下的警告信息：

```
Failed to read: session.screen0.toolbar.layer
Setting default value
Failed to read: session.screen0.toolbar.onhead
Setting default value
...
```

这些是 Fluxbox 窗口管理器的正常警告，表示它找不到某些配置项，因此使用默认值。这些警告不会影响 VNC 服务器的功能，可以安全地忽略。

如果您想消除这些警告，可以创建一个 Fluxbox 配置文件：

```bash
mkdir -p $HOME/.fluxbox
touch $HOME/.fluxbox/init
```

## 注意事项

- 容器中的 VNC 密码设置为 "password"，生产环境中应更改为更安全的密码
- 容器停止后，所有未保存到挂载卷的数据都将丢失
