package com.example.captain.controller;

import com.example.captain.entity.Goods;
import com.example.captain.mapper.GoodsMapper;
import com.example.captain.service.ExcelExportService;
import com.example.common.model.ApiResponse;
import io.micrometer.observation.annotation.Observed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * Excel导出控制器
 * 基于Goods表提供Amazon商品数据的Excel导出API
 */
@Slf4j
@RestController
@RequestMapping("/captain/excel")
@RequiredArgsConstructor
@Observed(name = "captain.controller", contextualName = "excel-export-controller")
public class ExcelExportController {

    private final ExcelExportService excelExportService;
    private final GoodsMapper goodsMapper;

    /**
     * 根据亚马逊主商品ASIN导出Excel文件
     * @param mainAsin 主商品ASIN
     * @return Excel文件下载响应
     */
    @GetMapping("/export/asin/{mainAsin}")
    public ResponseEntity<Resource> exportGoodsByMainAsin(@PathVariable("mainAsin") String mainAsin) {
        try {
            log.info("开始导出主商品ASIN为{}的Excel文件", mainAsin);

            // 1. 查询goods表，主商品排在前面，子商品排在后面
            List<Goods> goodsList = goodsMapper.findGoodsByMainAsin(mainAsin);
            if (goodsList.isEmpty()) {
                log.warn("未找到主商品ASIN为{}的商品数据", mainAsin);
                return ResponseEntity.notFound().build();
            }

            log.info("找到{}个相关商品，主商品ASIN: {}", goodsList.size(), mainAsin);

            // 2. 生成Excel文件
            String filePath = excelExportService.exportGoodsToExcel(goodsList, null);

            // 3. 准备文件下载响应
            File file = new File(filePath);
            Resource resource = new FileSystemResource(file);

            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String filename = String.format("amazon_goods_%s_%s.xls", mainAsin, timestamp);
            String encodedFilename = URLEncoder.encode(filename, StandardCharsets.UTF_8);

            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION,
                           "attachment; filename=\"" + filename + "\"; filename*=UTF-8''" + encodedFilename)
                    .header(HttpHeaders.CONTENT_TYPE, "application/vnd.ms-excel")
                    .contentLength(file.length())
                    .body(resource);

        } catch (Exception e) {
            log.error("导出主商品ASIN为{}的Excel文件失败: {}", mainAsin, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 下载空的Excel模板
     * @return Excel模板文件下载响应
     */
    @GetMapping("/template")
    public ResponseEntity<Resource> downloadTemplate() {
        try {
            log.info("开始下载Excel模板文件");

            // 1. 生成空模板文件
            String filePath = excelExportService.generateEmptyTemplate(null);

            // 2. 准备文件下载响应
            File file = new File(filePath);
            Resource resource = new FileSystemResource(file);

            String filename = "amazon_product_template.xls";
            String encodedFilename = URLEncoder.encode(filename, StandardCharsets.UTF_8);

            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION,
                           "attachment; filename=\"" + filename + "\"; filename*=UTF-8''" + encodedFilename)
                    .header(HttpHeaders.CONTENT_TYPE, "application/vnd.ms-excel")
                    .contentLength(file.length())
                    .body(resource);

        } catch (Exception e) {
            log.error("下载Excel模板文件失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 验证商品数据是否适合导出
     * @param mainAsin 主商品ASIN
     * @return 验证结果
     */
    @GetMapping("/validate/asin/{mainAsin}")
    public ResponseEntity<ApiResponse<Map<String, Object>>> validateGoodsForExport(@PathVariable("mainAsin") String mainAsin) {
        try {
            log.info("开始验证主商品ASIN为{}的商品数据", mainAsin);

            // 1. 获取商品数据
            List<Goods> goodsList = goodsMapper.findGoodsByMainAsin(mainAsin);

            // 2. 验证商品数据
            List<String> validationErrors = excelExportService.validateGoodsForExport(goodsList);

            // 3. 构建响应
            Map<String, Object> result = Map.of(
                "totalGoods", goodsList.size(),
                "validGoods", goodsList.size() - validationErrors.size(),
                "invalidGoods", validationErrors.size(),
                "errors", validationErrors,
                "canExport", validationErrors.isEmpty()
            );

            return ResponseEntity.ok(ApiResponse.success(result));

        } catch (Exception e) {
            log.error("验证主商品ASIN为{}的商品数据失败: {}", mainAsin, e.getMessage(), e);
            return ResponseEntity.ok(ApiResponse.error(500, "验证失败: " + e.getMessage()));
        }
    }
}
