package com.example.captain.util;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * ASIN压缩工具类测试
 */
public class AsinCompressorTest {

    @Test
    public void testCompress() {
        String[] testAsins = {
            "B09TEST113",
            "B07PXGQC1Q",
            "B01DFKC2SO",
            "B00X4WHP5E",
            "B000P0ZUVY"
        };
        
        System.out.println("ASIN压缩测试：");
        System.out.println("原始ASIN\t\t压缩后\t\t压缩率");
        System.out.println("----------------------------------------");
        
        for (String asin : testAsins) {
            String compressed = AsinCompressor.compress(asin);
            double ratio = (1.0 - (double)compressed.length() / asin.length()) * 100;
            System.out.printf("%s\t%s\t\t%.1f%%\n", asin, compressed, ratio);
            
            // 确保压缩后的字符串长度小于原始ASIN
            assertTrue(compressed.length() < asin.length(), 
                    "压缩后的长度应小于原始ASIN长度: " + asin + " -> " + compressed);
            
            // 确保压缩后的字符串只包含数字和字母
            assertTrue(compressed.matches("[0-9A-Za-z]+"), 
                    "压缩后的字符串应只包含数字和字母: " + compressed);
        }
    }
    
    @Test
    public void testCompressEdgeCases() {
        // 测试空字符串
        assertEquals("", AsinCompressor.compress(""));
        
        // 测试null
        assertEquals("", AsinCompressor.compress(null));
        
        // 测试短ASIN
        String shortAsin = "B123";
        String compressed = AsinCompressor.compress(shortAsin);
        assertTrue(compressed.length() < shortAsin.length() || compressed.equals(shortAsin),
                "短ASIN压缩: " + shortAsin + " -> " + compressed);
        
        // 测试非标准ASIN（不以B开头）
        String nonStandardAsin = "X12345678";
        compressed = AsinCompressor.compress(nonStandardAsin);
        assertTrue(compressed.length() < nonStandardAsin.length(),
                "非标准ASIN压缩: " + nonStandardAsin + " -> " + compressed);
    }
    
    @Test
    public void testDecompress() {
        // 由于压缩过程可能丢失信息，解压缩可能无法完全还原原始ASIN
        // 这里只测试解压缩功能是否正常工作
        
        String[] testAsins = {
            "B09TEST113",
            "B07PXGQC1Q",
            "B01DFKC2SO"
        };
        
        for (String asin : testAsins) {
            String compressed = AsinCompressor.compress(asin);
            String decompressed = AsinCompressor.decompress(compressed);
            
            System.out.println("原始ASIN: " + asin);
            System.out.println("压缩后: " + compressed);
            System.out.println("解压缩: " + decompressed);
            System.out.println();
            
            // 确保解压缩后的字符串是有效的ASIN格式
            assertTrue(decompressed.length() == 10, 
                    "解压缩后的ASIN长度应为10: " + decompressed);
            assertTrue(decompressed.startsWith("B"), 
                    "解压缩后的ASIN应以B开头: " + decompressed);
        }
    }
}
