package com.example.captain.util;

import com.example.captain.dto.AmazonProductExcelData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Excel模板工具类测试
 */
@SpringBootTest
class ExcelTemplateUtilTest {

    private ExcelTemplateUtil excelTemplateUtil;

    @BeforeEach
    void setUp() {
        excelTemplateUtil = new ExcelTemplateUtil();
    }

    @Test
    void testLoadTemplate() {
        // 测试模板加载
        boolean result = excelTemplateUtil.loadTemplate();
        assertTrue(result, "模板应该加载成功");
    }

    @Test
    void testWriteProductData() {
        // 准备测试数据
        AmazonProductExcelData testData = createTestProductData();
        
        // 加载模板
        assertTrue(excelTemplateUtil.loadTemplate(), "模板加载失败");
        
        // 写入数据
        assertDoesNotThrow(() -> {
            excelTemplateUtil.writeProductData(testData);
        }, "写入产品数据不应该抛出异常");
    }

    @Test
    void testGenerateExcelFile() {
        // 准备测试数据
        List<AmazonProductExcelData> testDataList = Arrays.asList(
            createTestProductData(),
            createTestProductData2()
        );
        
        // 加载模板
        assertTrue(excelTemplateUtil.loadTemplate(), "模板加载失败");
        
        // 写入数据
        excelTemplateUtil.writeProductsData(testDataList);
        
        // 生成文件
        String filePath = assertDoesNotThrow(() -> {
            return excelTemplateUtil.generateExcelFile(null);
        }, "生成Excel文件不应该抛出异常");
        
        assertNotNull(filePath, "文件路径不应该为空");
        assertTrue(filePath.endsWith(".xls"), "文件应该是.xls格式");
        
        // 清理资源
        excelTemplateUtil.close();
    }

    /**
     * 创建测试产品数据
     */
    private AmazonProductExcelData createTestProductData() {
        return AmazonProductExcelData.builder()
                .feedProductType("PRODUCT")
                .itemSku("TEST_SKU_001")
                .brandName("Test Brand")
                .externalProductId("B09TEST001")
                .externalProductIdType("ASIN")
                .itemName("Test Product 1")
                .manufacturer("Test Manufacturer")
                .recommendedBrowseNodes("123456")
                .maxOrderQuantity(10)
                .countryOfOrigin("CN")
                .standardPrice(new BigDecimal("29.99"))
                .quantity(100)
                .mainImageUrl("https://example.com/image1.jpg")
                .otherImageUrls(Arrays.asList(
                    "https://example.com/image2.jpg",
                    "https://example.com/image3.jpg"
                ))
                .parentChild("parent")
                .updateDelete("Update")
                .productDescription("This is a test product description")
                .bulletPoints(Arrays.asList(
                    "Feature 1",
                    "Feature 2",
                    "Feature 3"
                ))
                .genericKeywords("test, product, sample")
                .departmentName("Electronics")
                .currency("USD")
                .listPrice(new BigDecimal("39.99"))
                .conditionType("New")
                .build();
    }

    /**
     * 创建第二个测试产品数据
     */
    private AmazonProductExcelData createTestProductData2() {
        return AmazonProductExcelData.builder()
                .feedProductType("PRODUCT")
                .itemSku("TEST_SKU_002")
                .brandName("Test Brand 2")
                .externalProductId("B09TEST002")
                .externalProductIdType("ASIN")
                .itemName("Test Product 2")
                .manufacturer("Test Manufacturer 2")
                .recommendedBrowseNodes("789012")
                .maxOrderQuantity(5)
                .countryOfOrigin("US")
                .standardPrice(new BigDecimal("49.99"))
                .quantity(50)
                .mainImageUrl("https://example.com/image4.jpg")
                .parentChild("child")
                .parentSku("TEST_SKU_001")
                .updateDelete("Update")
                .productDescription("This is another test product description")
                .bulletPoints(Arrays.asList(
                    "Feature A",
                    "Feature B"
                ))
                .genericKeywords("test, product, sample, variant")
                .departmentName("Home")
                .currency("USD")
                .listPrice(new BigDecimal("59.99"))
                .conditionType("New")
                .build();
    }
}
