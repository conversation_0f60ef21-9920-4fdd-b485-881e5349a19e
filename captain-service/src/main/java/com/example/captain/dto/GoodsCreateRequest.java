package com.example.captain.dto;

import com.example.captain.enums.AmazonSite;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 创建Amazon待发布商品请求
 */
@Data
public class GoodsCreateRequest {

    /**
     * 关联的事件ID
     */
    @NotNull(message = "事件ID不能为空")
    private Long eventId;

    /**
     * 主商品ASIN
     */
    @NotBlank(message = "Amazon商品ASIN不能为空")
    private String amazonAsin;

    /**
     * 1688货源object_id
     */
    @NotBlank(message = "1688货源object_id不能为空")
    private String supplyObjectId;

    /**
     * 店铺ID
     */
    @NotNull(message = "店铺ID不能为空")
    private Long storeId;

    /**
     * Amazon站点
     */
    @NotNull(message = "Amazon站点不能为空")
    private AmazonSite amazonSite;
}
