# 使用官方的Playwright Java镜像作为基础镜像
FROM mcr.microsoft.com/playwright/java:v1.42.0-jammy

# 预先设置时区和非交互式安装，避免交互式选择
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=Asia/Shanghai

# 安装必要的软件包，包括完整的中文支持和更好的终端
RUN apt-get update && apt-get install -y \
    locales \
    fonts-noto-cjk \
    fonts-noto-color-emoji \
    fonts-wqy-microhei \
    fonts-wqy-zenhei \
    fonts-arphic-ukai \
    fonts-arphic-uming \
    language-pack-zh-hans \
    xfonts-intl-chinese \
    xfonts-wqy \
    x11vnc \
    xvfb \
    fluxbox \
    novnc \
    websockify \
    x11-xserver-utils \
    lxterminal \
    rxvt-unicode \
    net-tools \
    curl \
    maven \
    tzdata \
    procps \
    vim \
    nano \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 配置时区
RUN ln -fs /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    dpkg-reconfigure -f noninteractive tzdata

# 设置语言环境为中文
RUN locale-gen zh_CN.UTF-8
ENV LANG zh_CN.UTF-8
ENV LANGUAGE zh_CN:zh
ENV LC_ALL zh_CN.UTF-8

# 设置Java编码
ENV JAVA_TOOL_OPTIONS="-Dfile.encoding=UTF-8 -Dsun.jnu.encoding=UTF-8 -Dstdout.encoding=UTF-8 -Dstderr.encoding=UTF-8 -Dconsole.encoding=UTF-8"

# 更新字体缓存
RUN fc-cache -fv

# 创建中文测试文件
RUN echo "这是一个中文测试文件，用于测试中文显示是否正常。" > /tmp/chinese_test.txt

# 设置终端支持中文
RUN echo "export LANG=zh_CN.UTF-8" >> /etc/bash.bashrc
RUN echo "export LANGUAGE=zh_CN:zh" >> /etc/bash.bashrc
RUN echo "export LC_ALL=zh_CN.UTF-8" >> /etc/bash.bashrc
RUN echo "export TERM=xterm-256color" >> /etc/bash.bashrc
RUN echo "stty echo" >> /etc/bash.bashrc
RUN echo "stty icanon" >> /etc/bash.bashrc
RUN echo "printf \"\033%%G\"" >> /etc/bash.bashrc

# 修改noVNC配置以支持中文
RUN find /usr/share/novnc -name "app.js" -exec sed -i 's/"DejaVu Sans Mono", "Liberation Mono", "monospace"/"WenQuanYi Micro Hei Mono", "Noto Sans Mono CJK SC", "DejaVu Sans Mono", "Liberation Mono", "monospace"/g' {} \; || true
RUN find /usr/share/novnc -name "*.html" -exec sed -i 's/<head>/<head>\n<meta charset="UTF-8">/g' {} \; || true

# 创建脚本目录
RUN mkdir -p /scripts

# 创建中文终端启动脚本
RUN echo '#!/bin/bash\n\
export LANG=zh_CN.UTF-8\n\
export LANGUAGE=zh_CN:zh\n\
export LC_ALL=zh_CN.UTF-8\n\
export TERM=xterm-256color\n\
exec lxterminal --title="中文终端"' > /scripts/start-chinese-terminal.sh

RUN chmod +x /scripts/start-chinese-terminal.sh

# 创建fluxbox菜单配置
RUN mkdir -p /root/.fluxbox
RUN echo '[begin] (Fluxbox)\n\
  [submenu] (应用程序)\n\
    [exec] (中文终端) {/scripts/start-chinese-terminal.sh}\n\
    [exec] (xterm) {xterm}\n\
  [end]\n\
  [submenu] (配置)\n\
    [config] (配置)\n\
  [end]\n\
  [submenu] (系统)\n\
    [restart] (重启)\n\
    [exit] (退出)\n\
  [end]\n\
[end]' > /root/.fluxbox/menu

# 设置工作目录
WORKDIR /app

# 创建启动脚本
RUN echo '#!/bin/bash\n\
# 设置显示分辨率\n\
export DISPLAY_WIDTH=${DISPLAY_WIDTH:-1920}\n\
export DISPLAY_HEIGHT=${DISPLAY_HEIGHT:-1080}\n\
export VNC_PASSWORD=${VNC_PASSWORD:-password}\n\
\n\
# 设置中文环境\n\
export LANG=zh_CN.UTF-8\n\
export LANGUAGE=zh_CN:zh\n\
export LC_ALL=zh_CN.UTF-8\n\
export TERM=xterm-256color\n\
export JAVA_TOOL_OPTIONS="-Dfile.encoding=UTF-8 -Dsun.jnu.encoding=UTF-8 -Dstdout.encoding=UTF-8 -Dstderr.encoding=UTF-8 -Dconsole.encoding=UTF-8"\n\
\n\
# 确保没有旧的进程在运行\n\
pkill -f Xvfb || true\n\
pkill -f x11vnc || true\n\
pkill -f fluxbox || true\n\
pkill -f websockify || true\n\
\n\
# 清理锁文件\n\
rm -f /tmp/.X0-lock || true\n\
rm -f /tmp/.X11-unix/X0 || true\n\
\n\
# 启动虚拟显示服务器\n\
echo "启动Xvfb..."\n\
Xvfb :0 -screen 0 ${DISPLAY_WIDTH}x${DISPLAY_HEIGHT}x24 &\n\
\n\
# 等待Xvfb启动\n\
sleep 2\n\
\n\
# 设置DISPLAY环境变量\n\
export DISPLAY=:0\n\
\n\
# 启动窗口管理器\n\
echo "启动Fluxbox..."\n\
fluxbox &\n\
\n\
# 启动VNC服务器\n\
echo "启动VNC服务器..."\n\
x11vnc -display :0 -forever -shared -passwd ${VNC_PASSWORD} &\n\
\n\
# 启动noVNC\n\
echo "启动noVNC..."\n\
websockify --web /usr/share/novnc 8080 localhost:5900 &\n\
\n\
# 等待服务启动\n\
sleep 2\n\
\n\
# 检查VNC服务器是否在运行\n\
if netstat -tuln | grep -q ":5900"; then\n\
  echo "VNC服务器已成功启动在端口5900"\n\
else\n\
  echo "警告：VNC服务器似乎没有在端口5900上运行"\n\
fi\n\
\n\
echo "VNC服务器已启动，可通过浏览器访问 http://localhost:7070/vnc.html"\n\
echo "VNC密码: ${VNC_PASSWORD}"\n\
\n\
# 自动启动中文终端\n\
echo "启动中文终端..."\n\
/scripts/start-chinese-terminal.sh &\n\
\n\
# 测试中文显示\n\
echo "========== 中文显示测试 =========="\n\
echo "如果您能看到这段中文，说明终端编码配置正确。"\n\
echo "=================================="\n\
\n\
# 编译并运行应用程序\n\
cd /app\n\
# echo "正在编译并运行应用程序..."\n\
# mvn clean compile exec:java -Dexec.mainClass="com.example.worker.service.AmazonService" -Dfile.encoding=UTF-8 -Dsun.jnu.encoding=UTF-8\n\
\n\
# 保持容器运行\n\
echo "容器运行中..."\n\
tail -f /dev/null' > /scripts/start.sh

RUN chmod +x /scripts/start.sh

# 暴露VNC和noVNC端口
EXPOSE 5900 8080

# 设置容器启动命令
CMD ["/scripts/start.sh"]
