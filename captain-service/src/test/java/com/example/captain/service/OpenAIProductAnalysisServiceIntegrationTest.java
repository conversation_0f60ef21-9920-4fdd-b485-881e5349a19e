package com.example.captain.service;

import com.example.captain.model.ProductDimensionsAndWeight;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

/**
 * OpenAI商品分析服务集成测试
 * 此测试需要配置有效的OpenAI API密钥才能运行
 *
 * 运行前请在application.yml中配置您的OpenAI API密钥：
 * openai:
 *   api-key: sk-your-actual-api-key-here
 */
@SpringBootTest
@Slf4j
@TestPropertySource(properties = {
    "openai.model=gpt-3.5-turbo",
    "openai.timeout=30"
})
public class OpenAIProductAnalysisServiceIntegrationTest {

    private OpenAIProductAnalysisService openAIService;
    private ObjectMapper objectMapper;

    @BeforeEach
    public void setUp() {
        objectMapper = new ObjectMapper();
        // 注意：这里使用占位符，实际测试时请在application.yml中配置真实的API密钥
        String apiKey = "sk-your-actual-api-key-here"; // 请替换为真实的API密钥
        String model = "gpt-3.5-turbo";
        openAIService = new OpenAIProductAnalysisService(apiKey, objectMapper, model);
    }

    @Test
    public void testExtractProductDimensions_BluetoothHeadphones() {
        log.info("=== 测试案例1: 蓝牙耳机 ===");

        // 输入数据
        String title = "Wireless Bluetooth Headphones - Over Ear Headphones with Microphone, 40H Playtime, Deep Bass, Soft Earmuffs, Foldable, Wired and Wireless Modes for PC/Cell Phones/TV/Travel";
        String feature = "【40H Playtime & Fast Charging】: Wireless headphones can be used for 40 hours in wireless mode (at 50% volume). Weight: 0.5 kg. Dimensions: 20cm x 15cm x 8cm. 【Premium Sound Quality】: Advanced 40mm drivers deliver rich, detailed sound with deep bass.";
        String description = "Premium wireless Bluetooth headphones with superior sound quality. Package dimensions: 20 x 15 x 8 centimeters. Shipping weight: 500 grams. Perfect for music lovers and professionals. The headphones feature comfortable over-ear design with soft padding.";

        printInputData(title, feature, description);

        // 执行提取
        ProductDimensionsAndWeight result = openAIService.extractDimensionsAndWeight(title, feature, description);

        // 输出结果
        printExtractionResult(result);
    }

    @Test
    public void testExtractProductDimensions_LaptopStand() {
        log.info("=== 测试案例2: 笔记本电脑支架 ===");

        // 输入数据
        String title = "Adjustable Laptop Stand for Desk, Aluminum Laptop Riser with Heat-Vent, Ergonomic Computer Stand for MacBook Air Pro, Dell, HP, Lenovo More 10-17.3 Laptops";
        String feature = "【Ergonomic Design】: 6 adjustable height levels. 【Sturdy & Stable】: Made of premium aluminum alloy. Weight: 1.2kg. Size: 28cm(L) x 22cm(W) x 6cm(H). 【Heat Dissipation】: Open design for better airflow.";
        String description = "This laptop stand is made of high-quality aluminum alloy with excellent heat dissipation. Product dimensions: 28 x 22 x 6 cm. Net weight: 1200g. Supports laptops from 10 to 17.3 inches. Perfect for office and home use.";

        printInputData(title, feature, description);

        // 执行提取
        ProductDimensionsAndWeight result = openAIService.extractDimensionsAndWeight(title, feature, description);

        // 输出结果
        printExtractionResult(result);
    }

    @Test
    public void testExtractProductDimensions_WirelessCharger() {
        log.info("=== 测试案例3: 无线充电器 ===");

        // 输入数据
        String title = "Wireless Charger, 15W Max Fast Wireless Charging Pad Compatible with iPhone 14/13/12/11 Series, Samsung Galaxy S23/S22/S21, AirPods Pro";
        String feature = "【15W Fast Charging】: Supports up to 15W fast charging for compatible devices. 【Compact Design】: Ultra-slim profile at just 8mm thick. Dimensions: 10cm diameter x 0.8cm height. Weight: 150 grams. 【Safety Features】: Over-current, over-voltage, and over-temperature protection.";
        String description = "Sleek and compact wireless charging pad with 15W fast charging capability. Product specifications: Diameter 10cm, Height 0.8cm, Weight 150g. Compatible with Qi-enabled devices. LED indicator shows charging status.";

        printInputData(title, feature, description);

        // 执行提取
        ProductDimensionsAndWeight result = openAIService.extractDimensionsAndWeight(title, feature, description);

        // 输出结果
        printExtractionResult(result);
    }

    @Test
    public void testExtractProductDimensions_GamingMouse() {
        log.info("=== 测试案例4: 游戏鼠标 ===");

        // 输入数据
        String title = "Gaming Mouse, RGB Wired Gaming Mouse with 12000 DPI, 7 Programmable Buttons, Ergonomic Design for PC Laptop Computer Games";
        String feature = "【High Precision】: Up to 12000 DPI for precise tracking. 【Ergonomic Design】: Comfortable grip for extended gaming sessions. Product size: 12.5cm x 6.8cm x 4.2cm. Net weight: 95g. 【RGB Lighting】: Customizable RGB backlighting with multiple effects.";
        String description = "Professional gaming mouse with high-precision sensor and customizable RGB lighting. Dimensions: 125mm x 68mm x 42mm. Weight: 95 grams. Features 7 programmable buttons and ergonomic design for comfortable gaming.";

        printInputData(title, feature, description);

        // 执行提取
        ProductDimensionsAndWeight result = openAIService.extractDimensionsAndWeight(title, feature, description);

        // 输出结果
        printExtractionResult(result);
    }

    @Test
    public void testExtractProductDimensions_IncompleteData() {
        log.info("=== 测试案例5: 不完整数据 ===");

        // 输入数据（缺少具体的重量和尺寸信息）
        String title = "Smartphone Case, Clear Protective Case with Shock Absorption";
        String feature = "【Crystal Clear】: Transparent design shows off your phone's original beauty. 【Drop Protection】: Military-grade protection against drops and impacts.";
        String description = "High-quality transparent phone case with excellent protection. Made from premium TPU material. Easy installation and precise cutouts for all ports.";

        printInputData(title, feature, description);

        // 执行提取
        ProductDimensionsAndWeight result = openAIService.extractDimensionsAndWeight(title, feature, description);

        // 输出结果
        printExtractionResult(result);
    }

    /**
     * 打印输入数据
     */
    private void printInputData(String title, String feature, String description) {
        System.out.println("\n📝 输入数据:");
        System.out.println("标题 (Title): " + title);
        System.out.println("特性 (Feature): " + feature);
        System.out.println("描述 (Description): " + description);
        System.out.println("\n🔄 正在调用OpenAI API进行信息提取...\n");
    }

    /**
     * 打印提取结果
     */
    private void printExtractionResult(ProductDimensionsAndWeight result) {
        System.out.println("📊 提取结果:");
        System.out.println("==================================================");

        if (result != null && result.hasValidData()) {
            // 重量信息
            if (result.getShippingWeight() != null) {
                System.out.println("🏋️ 运输重量: " + result.getShippingWeight() + " " +
                    (result.getShippingWeightUnitOfMeasure() != null ? result.getShippingWeightUnitOfMeasure() : ""));
            } else {
                System.out.println("🏋️ 运输重量: 未提取到");
            }

            // 尺寸信息
            System.out.println("📏 包装尺寸:");
            if (result.getItemDepthFrontToBack() != null) {
                System.out.println("   长度(深度): " + result.getItemDepthFrontToBack() + " " +
                    (result.getItemDepthUnit() != null ? result.getItemDepthUnit() : ""));
            } else {
                System.out.println("   长度(深度): 未提取到");
            }

            if (result.getItemWidthSideToSide() != null) {
                System.out.println("   宽度: " + result.getItemWidthSideToSide() + " " +
                    (result.getItemWidthUnit() != null ? result.getItemWidthUnit() : ""));
            } else {
                System.out.println("   宽度: 未提取到");
            }

            if (result.getItemHeightFloorToTop() != null) {
                System.out.println("   高度: " + result.getItemHeightFloorToTop() + " " +
                    (result.getItemHeightUnit() != null ? result.getItemHeightUnit() : ""));
            } else {
                System.out.println("   高度: 未提取到");
            }

            System.out.println("\n✅ 提取成功！");
        } else {
            System.out.println("❌ 未能提取到有效的重量和尺寸信息");
            if (result == null) {
                System.out.println("   原因: OpenAI API调用失败或返回空结果");
            } else {
                System.out.println("   原因: 提取的数据无效或为空");
            }
        }

        System.out.println("==================================================");
        System.out.println();
    }
}
