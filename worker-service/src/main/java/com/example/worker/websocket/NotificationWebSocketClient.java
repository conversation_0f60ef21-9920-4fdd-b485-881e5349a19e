package com.example.worker.websocket;

import com.example.worker.config.AppConfig;
import com.example.worker.model.NotificationDto;
import com.example.worker.model.WebSocketMessage;
import com.example.worker.service.AuthService;
import com.example.worker.service.AmazonService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.handshake.ServerHandshake;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * 通知WebSocket客户端
 */
@Slf4j
public class NotificationWebSocketClient extends WebSocketClient {

    private final ObjectMapper objectMapper;
    private final AppConfig appConfig;
    private final AmazonService amazonService;
    private final AuthService authService;

    /**
     * 构造函数
     *
     * @param serverUri WebSocket服务器URI
     * @param objectMapper ObjectMapper实例
     * @param appConfig 应用配置
     * @param amazonService Amazon服务
     * @param authService 认证服务
     */
    public NotificationWebSocketClient(URI serverUri, ObjectMapper objectMapper, AppConfig appConfig,
                                      AmazonService amazonService, AuthService authService) {
        super(serverUri);
        this.objectMapper = objectMapper;
        this.appConfig = appConfig;
        this.amazonService = amazonService;
        this.authService = authService;
    }

    /**
     * 创建WebSocket客户端
     *
     * @param wsUrl WebSocket URL
     * @param token JWT令牌
     * @param objectMapper ObjectMapper实例
     * @param appConfig 应用配置
     * @param amazonService Amazon服务
     * @param authService 认证服务
     * @return WebSocket客户端
     */
    public static NotificationWebSocketClient create(String wsUrl, String token, ObjectMapper objectMapper,
                                                   AppConfig appConfig, AmazonService amazonService,
                                                   AuthService authService) {
        try {
            // 构建WebSocket URL，添加token参数
            URI serverUri = new URI(wsUrl + "?token=" + token);
            return new NotificationWebSocketClient(serverUri, objectMapper, appConfig, amazonService, authService);
        } catch (URISyntaxException e) {
            log.error("创建WebSocket客户端时发生错误", e);
            throw new RuntimeException("创建WebSocket客户端失败", e);
        }
    }

    @Override
    public void onOpen(ServerHandshake handshakedata) {
        log.info("WebSocket连接已打开，状态码: {}", handshakedata.getHttpStatus());

        // 订阅主题
        subscribeToTopic(appConfig.getTopic());
    }

    @Override
    public void onMessage(String message) {
        try {
            log.info("收到WebSocket消息: {}", message);

            // 解析消息
            WebSocketMessage webSocketMessage = objectMapper.readValue(message, WebSocketMessage.class);

            // 处理消息
            handleMessage(webSocketMessage);
        } catch (JsonProcessingException e) {
            log.error("解析WebSocket消息时发生错误", e);
        }
    }

    @Override
    public void onClose(int code, String reason, boolean remote) {
        log.info("WebSocket连接已关闭，代码: {}, 原因: {}, 远程关闭: {}", code, reason, remote);
    }

    @Override
    public void onError(Exception ex) {
        log.error("WebSocket连接发生错误", ex);
    }

    /**
     * 处理消息
     *
     * @param message WebSocket消息
     */
    private void handleMessage(WebSocketMessage message) {
        String type = message.getType();

        switch (type) {
            case "connected":
                log.info("WebSocket连接已建立");
                break;
            case "subscribed":
                handleSubscribedMessage(message);
                break;
            case "notification":
                handleNotificationMessage(message);
                break;
            case "error":
                handleErrorMessage(message);
                break;
            default:
                log.warn("未知的消息类型: {}", type);
        }
    }

    /**
     * 处理订阅确认消息
     *
     * @param message WebSocket消息
     */
    private void handleSubscribedMessage(WebSocketMessage message) {
        try {
            JsonNode data = objectMapper.valueToTree(message.getData());
            String topic = data.get("topic").asText();
            String status = data.get("status").asText();

            log.info("主题 {} 订阅状态: {}", topic, status);
        } catch (Exception e) {
            log.error("处理订阅确认消息时发生错误", e);
        }
    }

    /**
     * 处理通知消息
     *
     * @param message WebSocket消息
     */
    private void handleNotificationMessage(WebSocketMessage message) {
        try {
            // 将消息数据转换为NotificationDto
            NotificationDto notification = objectMapper.convertValue(message.getData(), NotificationDto.class);

            log.info("收到通知: {}", notification);

            // 根据通知类型处理消息
            String notificationType = notification.getType();
            Map<String, String> data = notification.getData();
            long eventId = notification.getId();

            if (notificationType != null && data != null) {
                switch (notificationType) {
                    case "fetch":
                        handleFetchNotification(eventId,data);
                        break;
                    default:
                        log.info("未处理的通知类型: {}", notificationType);
                }
            } else {
                log.warn("通知类型或数据为空");
            }
        } catch (Exception e) {
            log.error("处理通知消息时发生错误", e);
        }
    }

    /**
     * 处理获取网页内容的通知
     * 调用AmazonService的processFetchNotification方法处理fetch通知
     *
     * @param eventId 事件ID
     * @param data 通知数据
     */
    private void handleFetchNotification(long eventId, Map<String, String> data) {
        String url = data.get("url");
        if (url != null && !url.isEmpty()) {
            log.info("处理fetch通知，事件ID: {}, URL: {}", eventId, url);
            try {
                // 使用AmazonService处理fetch通知
                Map<String, Object> result = amazonService.processFetchNotification(eventId, url);

                if (result.containsKey("error")) {
                    // 处理失败，发送错误回调
                    String errorMsg = (String) result.get("error");
                    log.error("处理fetch通知时出错: {}", errorMsg);

                    // 构造错误结果
                    Map<String, String> callbackResult = new HashMap<>();
                    callbackResult.put("code", "-2"); // 通用错误码
                    callbackResult.put("msg", errorMsg);

                    // 发送回调
                    sendCallback(eventId, callbackResult);
                } else {
                    // 处理成功，发送成功回调
                    int pageCount = (int) result.get("page_count");
                    int productCount = (int) result.get("product_count");
                    log.info("成功处理fetch通知，共处理 {} 页，获取 {} 个商品信息", pageCount, productCount);

                    // 产品数据已经通过callbackProductInfo方法回传，这里只需要发送处理完成的回调
                    int callbackCount = (int) result.get("callback_count");
                    log.info("产品数据已回传 {} 次", callbackCount);

                    // 构造成功结果
                    Map<String, String> callbackResult = new HashMap<>();
                    callbackResult.put("code", "-1"); // 成功码
                    callbackResult.put("pageCount", String.valueOf(pageCount));
                    callbackResult.put("productCount", String.valueOf(productCount));
                    callbackResult.put("callbackCount", String.valueOf(callbackCount));

                    // 发送回调
                    sendCallback(eventId, callbackResult);
                }
            } catch (Exception e) {
                // 处理异常，发送异常回调
                log.error("处理fetch通知时发生异常", e);

                // 构造异常结果
                Map<String, String> callbackResult = new HashMap<>();
                callbackResult.put("code", "-3"); // 异常错误码
                callbackResult.put("msg", "处理fetch通知时发生异常: " + e.getMessage());

                // 发送回调
                sendCallback(eventId, callbackResult);
            }
        } else {
            // URL为空，发送参数错误回调
            log.warn("通知中不包含URL信息");

            // 构造参数错误结果
            Map<String, String> callbackResult = new HashMap<>();
            callbackResult.put("code", "-4"); // 参数错误码
            callbackResult.put("msg", "通知中不包含URL信息");

            // 发送回调
            sendCallback(eventId, callbackResult);
        }
    }

    /**
     * 发送回调请求
     *
     * @param eventId 事件ID
     * @param result 回调结果
     * @return 是否发送成功
     */
    private boolean sendCallback(long eventId, Map<String, String> result) {
        try {
            // 获取JWT令牌
            String token = authService.getToken();
            if (token == null) {
                log.error("获取JWT令牌失败，无法发送回调");
                return false;
            }

            // 构建请求URL - 使用captain-service的EventController路径
            String callbackUrl = appConfig.getGateway().getUrl() + "/api/captain/callback/" + eventId;
            log.info("发送回调请求到: {}", callbackUrl);

            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("result", result);
            String jsonBody = objectMapper.writeValueAsString(requestBody);

            // 构建HttpClient
            HttpClient httpClient = HttpClient.newBuilder()
                    .connectTimeout(Duration.ofSeconds(10))
                    .build();

            // 构建请求
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(callbackUrl))
                    .header("Content-Type", "application/json")
                    .header("Authorization", "Bearer " + token)
                    .POST(HttpRequest.BodyPublishers.ofString(jsonBody))
                    .build();

            // 发送请求
            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());

            // 检查响应
            if (response.statusCode() == 200) {
                JsonNode jsonNode = objectMapper.readTree(response.body());
                if (jsonNode.has("code") && jsonNode.get("code").asInt() == 0) {
                    log.info("回调请求发送成功，事件ID: {}", eventId);
                    return true;
                } else {
                    log.warn("回调请求发送失败，事件ID: {}, 响应: {}", eventId, response.body());
                    return false;
                }
            } else {
                log.warn("回调请求发送失败，事件ID: {}, 状态码: {}, 响应: {}",
                        eventId, response.statusCode(), response.body());
                return false;
            }
        } catch (Exception e) {
            log.error("发送回调请求时发生错误", e);
            return false;
        }
    }

    /**
     * 处理错误消息
     *
     * @param message WebSocket消息
     */
    private void handleErrorMessage(WebSocketMessage message) {
        try {
            JsonNode data = objectMapper.valueToTree(message.getData());
            String errorMessage = data.get("message").asText();

            log.error("收到错误消息: {}", errorMessage);
        } catch (Exception e) {
            log.error("处理错误消息时发生错误", e);
        }
    }

    /**
     * 订阅主题
     *
     * @param topic 主题名称
     */
    public void subscribeToTopic(String topic) {
        try {
            log.info("订阅主题: {}", topic);

            // 构建订阅消息
            WebSocketMessage message = new WebSocketMessage();
            message.setType("subscribe");

            Map<String, String> data = new HashMap<>();
            data.put("topic", topic);
            message.setData(data);

            // 发送订阅消息
            String payload = objectMapper.writeValueAsString(message);
            send(payload);

            log.info("已发送订阅请求");
        } catch (JsonProcessingException e) {
            log.error("发送订阅请求时发生错误", e);
        }
    }
}
