package com.example.captain.dto;

import lombok.Data;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;

/**
 * 店铺状态更新请求对象
 */
@Data
public class StoreStatusUpdateRequest {

    /**
     * 状态：1-正常，0-禁用
     */
    @NotNull(message = "状态不能为空")
    @Min(value = 0, message = "状态值必须为0或1")
    @Max(value = 1, message = "状态值必须为0或1")
    private Integer status;
}
