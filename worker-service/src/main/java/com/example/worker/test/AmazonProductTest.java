package com.example.worker.test;

import com.example.worker.client.AmazonClient;
import com.example.worker.config.AppConfig;
import com.example.worker.service.AmazonService;
import com.example.worker.service.AuthService;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.Map;

/**
 * Amazon商品测试类
 * 用于测试获取Amazon商品详情和查找Alibaba相似商品
 */
public class AmazonProductTest {

    /**
     * 主方法 - 测试获取Amazon商品详情和查找Alibaba相似商品
     *
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        System.out.println("=== AmazonProductTest.main() 开始执行 ===");

        // 初始化并禁用OpenCV的关闭钩子
        try {
            com.example.worker.imagesimilarity.OpenCVManager.initialize();
            com.example.worker.imagesimilarity.OpenCVManager.disableShutdownHook();
            System.out.println("OpenCV已初始化，并禁用了关闭钩子");
        } catch (Exception e) {
            System.err.println("初始化OpenCV时出错: " + e.getMessage());
        }

        // 商品链接
        String productLink = "https://www.amazon.co.uk/Machine-Blender-Smoothie-Commercial-Cocktail/dp/B0D8SBT51G/ref=sr_1_1?dib=eyJ2IjoiMSJ9.Il8Nt-qC6En2JKQxazA4wvAgcVFnq3rpAaajOYabwfNqz-th9DHKriqTACQd_fzJk7nxZ3gzoDsf_y3i9DbpQiC03IsP_bsUCSrL-v2hLAL3tQEFdBS24IC4p_4x2Yn2nG7hQEYzBBLzqLzTgiP5HYmFYPvlm1qV2jidcnbqst2hO15QsMtv5Y2-Nq8xQebk_k3aD7gDA0YkubRA8DGRpRWcKq0lDBqU-LYKkWByWXk.Z0PWkGEVXFrWWr3YjaMQOL5qLiSqN-8GTsIYYoeHddk&dib_tag=se&keywords=Smoothie+Maker&m=A3QHFOPJ0W0NUM&qid=1747744820&s=merchant-items&sr=1-1";

        try {
            // 创建必要的依赖对象
            AppConfig appConfig = new AppConfig();
            AppConfig.Gateway gateway = new AppConfig.Gateway();
            gateway.setUrl("http://**********:8080");
            appConfig.setGateway(gateway);

            AppConfig.Auth auth = new AppConfig.Auth();
            auth.setUsername("admin");
            auth.setPassword("Test@123");
            appConfig.setAuth(auth);

            ObjectMapper objectMapper = new ObjectMapper();
            AuthService authService = new AuthService();

            // 使用反射设置authService的依赖
            try {
                java.lang.reflect.Field appConfigField = AuthService.class.getDeclaredField("appConfig");
                appConfigField.setAccessible(true);
                appConfigField.set(authService, appConfig);

                java.lang.reflect.Field objectMapperField = AuthService.class.getDeclaredField("objectMapper");
                objectMapperField.setAccessible(true);
                objectMapperField.set(authService, objectMapper);
            } catch (Exception e) {
                System.err.println("设置AuthService依赖时出错: " + e.getMessage());
                e.printStackTrace();
                return;
            }

            // 创建AmazonService实例
            try (AmazonService amazonService = new AmazonService(appConfig, objectMapper, authService)) {
                System.out.println("初始化AmazonService成功");

                // 确保AmazonClient已初始化会话
                if (amazonService.initSession()) {
                    System.out.println("Amazon会话初始化成功");

                    // 获取商品详情
                    System.out.println("\n开始获取Amazon商品详情...");
                    System.out.println("商品链接: " + productLink);

                    Map<String, Object> productInfo = amazonService.getAmazonProductInfo(productLink);

                    // 打印商品详情
                    if (productInfo.containsKey("error")) {
                        System.err.println("获取商品详情失败: " + productInfo.get("error"));
                    } else {
                        System.out.println("\n获取商品详情成功!");
                        System.out.println("ASIN: " + productInfo.get("ASIN"));
                        System.out.println("标题: " + productInfo.get("title"));
                        System.out.println("价格: " + productInfo.get("price"));
                        System.out.println("国家: " + productInfo.get("country"));
                        System.out.println("推荐浏览节点: " + productInfo.get("recommended_browse_nodes"));
                        System.out.println("产品类型: " + productInfo.get("Product_type"));

                        // 输出商品短描述
                        System.out.println("\n商品特点:");
                        System.out.println(productInfo.get("feature"));

                        // 输出商品长描述
                        System.out.println("\n商品描述:");
                        System.out.println(productInfo.get("description"));

                        // 输出变体商品信息
                        if (productInfo.containsKey("productVariants") && productInfo.get("productVariants") instanceof java.util.List) {
                            @SuppressWarnings("unchecked")
                            java.util.List<Map<String, Object>> variants = (java.util.List<Map<String, Object>>) productInfo.get("productVariants");

                            System.out.println("\n变体商品信息:");
                            System.out.println("变体数量: " + variants.size());

                            for (int i = 0; i < variants.size(); i++) {
                                Map<String, Object> variant = variants.get(i);
                                System.out.println("\n变体 " + (i + 1) + ":");
                                System.out.println("ASIN: " + variant.get("asin"));
                                System.out.println("链接: " + variant.get("link"));
                                System.out.println("SKU: " + variant.get("product_sku"));

                                // 输出变体属性
                                for (Map.Entry<String, Object> entry : variant.entrySet()) {
                                    if (!entry.getKey().equals("asin") && !entry.getKey().equals("link") &&
                                        !entry.getKey().equals("product_sku") && !entry.getKey().equals("parentAsin")) {
                                        System.out.println(entry.getKey() + ": " + entry.getValue());
                                    }
                                }
                            }
                        }

                        // 打印图片信息
                        System.out.println("\n商品图片信息:");
                        if (productInfo.containsKey("mainImage")) {
                            System.out.println("主图: " + productInfo.get("mainImage"));
                        }

                        if (productInfo.containsKey("images") && productInfo.get("images") instanceof java.util.List) {
                            java.util.List<?> images = (java.util.List<?>) productInfo.get("images");
                            System.out.println("图片数量: " + images.size());
                            for (int i = 0; i < Math.min(3, images.size()); i++) {
                                System.out.println("图片 " + (i + 1) + ": " + images.get(i));
                            }
                            if (images.size() > 3) {
                                System.out.println("... 更多图片省略 ...");
                            }
                        }

                        // 调用独立函数处理Alibaba相似商品
                        System.out.println("\n开始查找Alibaba相似商品...");
                        // 设置要从API抓取的商品数量为5
                        int maxProductsToExtract = 5;
                        // 设置相似度排序后返回的最大商品数量为3
                        int maxSimilarProductsToReturn = 3;
                        System.out.println("设置从API抓取的商品数量为: " + maxProductsToExtract);
                        System.out.println("设置相似度排序后返回的最大商品数量为: " + maxSimilarProductsToReturn);
                        // 使用null作为事件ID，因为这是测试环境，不需要回写数据到captain-service
                        amazonService.findSimilarAlibabaProducts(null, productInfo, maxProductsToExtract, maxSimilarProductsToReturn);

                        // 打印Alibaba相似商品信息
                        if (productInfo.containsKey("alibabaProducts") && productInfo.get("alibabaProducts") instanceof java.util.List) {
                            @SuppressWarnings("unchecked")
                            java.util.List<Map<String, Object>> alibabaProducts = (java.util.List<Map<String, Object>>) productInfo.get("alibabaProducts");

                            System.out.println("\nAlibaba相似商品信息:");
                            System.out.println("找到 " + alibabaProducts.size() + " 个最相似的商品");

                            for (int i = 0; i < alibabaProducts.size(); i++) {
                                Map<String, Object> product = alibabaProducts.get(i);
                                System.out.println("\n商品 " + (i + 1) + ":");
                                System.out.println("ID: " + product.get("objectId"));
                                System.out.println("标题: " + product.get("title"));
                                System.out.println("价格: " + product.get("price"));
                                System.out.println("相似度: " + product.get("similarity"));
                                System.out.println("图片URL: " + product.get("imageUrl"));
                                System.out.println("本地图片路径: " + product.get("localImagePath"));
                                System.out.println("详情URL: " + product.get("detailUrl"));
                                System.out.println("开店年限: " + product.get("tpYear"));
                                System.out.println("公司名称: " + product.get("companyName"));
                                System.out.println("公司档案: " + product.get("tpCreditUrl"));
                                System.out.println("年销量: " + product.get("sales360Fuzzify"));
                                System.out.println("几件起批: " + product.get("quantityBegin"));

                                // 输出商品属性信息
                                if (product.containsKey("productAttributes")) {
                                    System.out.println("\n商品属性信息:");
                                    System.out.println(product.get("productAttributes"));
                                }

                                // 输出包装信息
                                if (product.containsKey("packagingInfo")) {
                                    System.out.println("\n包装信息:");
                                    System.out.println(product.get("packagingInfo"));
                                }

                            }
                        } else {
                            System.out.println("未找到相似度超过80%的Alibaba商品");
                        }
                    }
                } else {
                    System.err.println("初始化Amazon会话失败，无法继续测试");
                }
            }
        } catch (Exception e) {
            System.err.println("测试过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }

        System.out.println("\nAmazonProductTest测试完成");

        // 手动释放OpenCV资源
        try {
            // 确保在程序结束前释放OpenCV资源
            com.example.worker.imagesimilarity.OpenCVManager.release();
            System.out.println("OpenCV资源已释放");
        } catch (Exception e) {
            System.err.println("释放OpenCV资源时出错: " + e.getMessage());
        }
    }
}
