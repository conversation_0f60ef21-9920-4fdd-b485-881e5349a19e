package com.example.captain.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.example.captain.dto.ProductDTO;
import com.example.captain.dto.ProductQueryParam;
import com.example.captain.entity.Product;

import java.util.List;
import java.util.Map;

/**
 * 产品服务接口
 */
public interface ProductService {

    /**
     * 保存产品数据
     *
     * @param eventId 事件ID
     * @param productInfoList 产品信息列表
     * @return 保存的产品数量
     */
    int saveProducts(Long eventId, List<Map<String, Object>> productInfoList);

    /**
     * 获取事件相关的所有产品
     *
     * @param eventId 事件ID
     * @return 产品列表
     */
    List<Product> getProductsByEventId(Long eventId);

    /**
     * 分页查询产品列表
     *
     * @param param 查询参数
     * @return 分页结果
     */
    IPage<ProductDTO> getProductPage(ProductQueryParam param);

    /**
     * 根据ID获取产品详情
     *
     * @param id 产品ID
     * @return 产品详情
     */
    ProductDTO getProductDTOById(Long id);

    /**
     * 根据ASIN获取产品详情
     *
     * @param asin 产品ASIN
     * @return 产品详情
     */
    ProductDTO getProductDTOByAsin(String asin);

    /**
     * 根据ID获取产品实体
     *
     * @param id 产品ID
     * @return 产品实体
     */
    Product getProductById(Long id);

    /**
     * 根据ID列表获取产品列表
     *
     * @param ids 产品ID列表
     * @return 产品列表
     */
    List<Product> getProductsByIds(List<Long> ids);
}
