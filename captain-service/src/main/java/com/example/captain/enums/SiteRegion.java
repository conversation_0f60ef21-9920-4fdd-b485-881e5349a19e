package com.example.captain.enums;

import lombok.Getter;

/**
 * Amazon站点区域枚举
 */
@Getter
public enum SiteRegion {
    
    NORTH_AMERICA("北美站", "North America"),
    EUROPE("欧洲站", "Europe"),
    ASIA_PACIFIC("亚太站", "Asia Pacific");
    
    private final String chineseName;
    private final String englishName;
    
    SiteRegion(String chineseName, String englishName) {
        this.chineseName = chineseName;
        this.englishName = englishName;
    }
    
    /**
     * 根据中文名称获取枚举
     */
    public static SiteRegion fromChineseName(String chineseName) {
        for (SiteRegion region : values()) {
            if (region.getChineseName().equals(chineseName)) {
                return region;
            }
        }
        throw new IllegalArgumentException("未知的站点区域: " + chineseName);
    }
}
