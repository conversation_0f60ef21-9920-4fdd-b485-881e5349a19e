server:
  port: 8080

spring:
  application:
    name: gateway-service
  # 允许 Bean 定义覆盖，解决可能的冲突
  main:
    allow-bean-definition-overriding: true
  cloud:
    gateway:
      discovery:
        locator:
          enabled: true
          lower-case-service-id: true
      # WebSocket 配置
      websocket:
        time-to-live: 1800  # 30 minutes
      # 全局超时配置
      httpclient:
        connect-timeout: 5000  # 5 seconds
        response-timeout: 10s  # 10 seconds
      # 默认过滤器
      default-filters:
        - name: RequestSize
          args:
            maxSize: 5MB
      # 路由配置
      routes:
        # 用户服务路由
        - id: user-service
          uri: lb://user-service
          predicates:
            - Path=/api/user/**
          filters:
            - RewritePath=/api/user/(?<segment>.*), /user/${segment}
            - RewritePath=/api/user, /user
            - name: Retry
              args:
                retries: 3
                statuses: INTERNAL_SERVER_ERROR,BAD_GATEWAY,SERVICE_UNAVAILABLE
                methods: GET,POST
                backoff:
                  firstBackoff: 100ms
                  maxBackoff: 1000ms
                  factor: 2
                  basedOnPreviousValue: true
            - name: CircuitBreaker
              args:
                name: user-serviceCircuitBreaker
                fallbackUri: forward:/fallback/user-service

        # WebSocket 路由
        - id: websocket-route
          uri: lb:ws://websocket-service
          predicates:
            - Path=/ws/**

        # WebSocket 事件 API 路由
        - id: websocket-api
          uri: lb://websocket-service
          predicates:
            - Path=/api/ws/**
          filters:
            - RewritePath=/api/ws/(?<segment>.*), /ws/${segment}
            - RewritePath=/api/ws, /ws
            - name: Retry
              args:
                retries: 3
                statuses: INTERNAL_SERVER_ERROR,BAD_GATEWAY,SERVICE_UNAVAILABLE
                methods: GET,POST
                backoff:
                  firstBackoff: 100ms
                  maxBackoff: 1000ms
                  factor: 2
                  basedOnPreviousValue: true
            - name: CircuitBreaker
              args:
                name: websocket-serviceCircuitBreaker
                fallbackUri: forward:/fallback/websocket-service

        # 认证服务路由
        - id: auth-service
          uri: forward:/auth
          predicates:
            - Path=/auth/**

        # 降级处理路由
        - id: fallback
          uri: forward:/fallback
          predicates:
            - Path=/fallback/**

        # Hello服务路由
        - id: hello-service
          uri: lb://hello-service
          predicates:
            - Path=/api/hello/**
          filters:
            - RewritePath=/api/hello/(?<segment>.*), /hello/${segment}
            - RewritePath=/api/hello, /hello
            - name: Retry
              args:
                retries: 3
                statuses: INTERNAL_SERVER_ERROR,BAD_GATEWAY,SERVICE_UNAVAILABLE
                methods: GET,POST
                backoff:
                  firstBackoff: 100ms
                  maxBackoff: 1000ms
                  factor: 2
                  basedOnPreviousValue: true
            - name: CircuitBreaker
              args:
                name: hello-serviceCircuitBreaker
                fallbackUri: forward:/fallback/hello-service

        # World服务路由
        - id: world-service
          uri: lb://world-service
          predicates:
            - Path=/api/world/**
          filters:
            - RewritePath=/api/world/(?<segment>.*), /world/${segment}
            - RewritePath=/api/world, /world
            - name: Retry
              args:
                retries: 3
                statuses: INTERNAL_SERVER_ERROR,BAD_GATEWAY,SERVICE_UNAVAILABLE
                methods: GET,POST
                backoff:
                  firstBackoff: 100ms
                  maxBackoff: 1000ms
                  factor: 2
                  basedOnPreviousValue: true
            - name: CircuitBreaker
              args:
                name: world-serviceCircuitBreaker
                fallbackUri: forward:/fallback/world-service

        # Todo服务路由
        - id: todo-service
          uri: lb://todo-service
          predicates:
            - Path=/api/todo/**
          filters:
            - RewritePath=/api/todo/(?<segment>.*), /todo/${segment}
            - RewritePath=/api/todo, /todo
            - name: Retry
              args:
                retries: 3
                statuses: INTERNAL_SERVER_ERROR,BAD_GATEWAY,SERVICE_UNAVAILABLE
                methods: GET,POST
                backoff:
                  firstBackoff: 100ms
                  maxBackoff: 1000ms
                  factor: 2
                  basedOnPreviousValue: true
            - name: CircuitBreaker
              args:
                name: todo-serviceCircuitBreaker
                fallbackUri: forward:/fallback/todo-service

        # Captain服务路由
        - id: captain-service
          uri: lb://captain-service
          predicates:
            - Path=/api/captain/**
          filters:
            - RewritePath=/api/captain/(?<segment>.*), /captain/${segment}
            - RewritePath=/api/captain, /captain
            - name: Retry
              args:
                retries: 3
                statuses: INTERNAL_SERVER_ERROR,BAD_GATEWAY,SERVICE_UNAVAILABLE
                methods: GET,POST
                backoff:
                  firstBackoff: 100ms
                  maxBackoff: 1000ms
                  factor: 2
                  basedOnPreviousValue: true
            - name: CircuitBreaker
              args:
                name: captain-serviceCircuitBreaker
                fallbackUri: forward:/fallback/captain-service


  # 熔断器配置
  circuitbreaker:
    resilience4j:
      circuitbreaker:
        default:
          slidingWindowSize: 10
          failureRateThreshold: 50
          waitDurationInOpenState: 10000
          permittedNumberOfCallsInHalfOpenState: 5
      timelimiter:
        default:
          timeoutDuration: 10000

eureka:
  client:
    serviceUrl:
      defaultZone: http://localhost:8761/eureka/
    # 启用健康检查
    healthcheck:
      enabled: true
    # 增加重试机制
    registry-fetch-interval-seconds: 5
    initial-instance-info-replication-interval-seconds: 10
    instance-info-replication-interval-seconds: 10
    eureka-service-url-poll-interval-seconds: 5
  instance:
    # 增加心跳机制
    lease-renewal-interval-in-seconds: 5
    lease-expiration-duration-in-seconds: 15
    prefer-ip-address: true
    instance-id: ${spring.application.name}:${server.port}

# Spring Security configuration
spring.security:
  oauth2:
    resourceserver:
      jwt:
        jwk-set-uri: http://localhost:8080/.well-known/jwks.json

# JWT configuration
jwt:
  expiration: 3600000  # 1 hour in milliseconds
  secret: ${JWT_SECRET:ThisIsAVerySecureSecretKeyForJwtSigningPleaseChangeInProduction}

# Springdoc OpenAPI configuration
springdoc:
  swagger-ui:
    path: /swagger-ui.html
    # 配置API文档聚合
    urls:
      - name: Gateway API
        url: /v3/api-docs
      - name: 认证API
        url: /v3/api-docs/认证API
      - name: 健康检查API
        url: /v3/api-docs/健康检查API
      - name: API文档
        url: /v3/api-docs/API文档
      - name: User Service API
        url: /user-service/v3/api-docs
        displayName: "User Service API"
      - name: WebSocket Service API
        url: /websocket-service/v3/api-docs
    # 禁用默认的Swagger Petstore URL
    disable-swagger-default-url: true
    # 默认展开所有操作
    docExpansion: none
    # 默认显示的API组
    urls-primary-name: User Service API
    # 启用深度链接
    deep-linking: true
    # 显示请求时长
    display-request-duration: true
    # 过滤器
    filter: true
    # 允许跨域
    csrf.enabled: false
    # 显示权限信息
    oauth:
      clientId: swagger-ui
    # 添加自定义配置
    config-url: /swagger-config.json
    # 添加自定义按钮
    custom-buttons:
      - name: "权限文档"
        url: "/api-permissions.html"
  api-docs:
    path: /v3/api-docs
    # 启用美化输出
    writer-with-default-pretty-printer: true
    # 显示所有组
    groups.enabled: true
    # 启用扩展
    enabled: true
    # 显示所有端点
    show-actuator: true
  # 禁用缓存，确保实时更新
  cache:
    disabled: true
  # 扫描包
  packages-to-scan: com.example.gateway
  # 添加权限信息
  model-converters:
    deprecating-converter:
      enabled: true
  # 显示所有模型
  show-actuator: true

management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: always
  # 详细的Micrometer Tracing配置
  tracing:
    enabled: true
    sampling:
      probability: 1.0  # 100% sampling for development
    propagation:
      type: B3_MULTI  # 使用B3多头格式，兼容性更好
    brave:
      enabled: true
      span-joining-enabled: true
      propagation:
        enabled: true
        type: B3_MULTI
  # 启用观察
  observations:
    enabled: true
    http:
      client:
        requests:
          name: "http.client.requests"
      server:
        requests:
          name: "http.server.requests"

logging:
  level:
    # 默认日志级别
    root: INFO
    # 应用日志
    com.example.gateway: INFO
    com.example.gateway.filter.AuthorizationFilter: INFO
    com.example.gateway.filter.MdcContextFilter: INFO
    com.example.gateway.service.UserPermissionService: INFO
    com.example.gateway.util.MdcContextOperator: INFO
    # 追踪相关日志
    io.micrometer.tracing: INFO
    io.micrometer.observation: INFO
    brave: INFO
    # 降低 Eureka 客户端日志级别
    com.netflix.discovery: WARN
    com.netflix.eureka: WARN
    org.springframework.cloud.netflix.eureka: WARN
    # 降低负载均衡器日志级别
    org.springframework.cloud.loadbalancer: WARN
    # 降低其他框架日志级别
    org.springframework.web.reactive.function.client: INFO
    org.springframework.http.server.reactive: INFO
    org.springframework.cloud.gateway: INFO
    # 开启调试日志的类
    com.example.gateway.controller: DEBUG
    com.example.gateway.util: DEBUG
  pattern:
    # 添加traceId和spanId到日志格式，并突出重要信息
    console: "%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(%-5level) %clr([%X{traceId:-}/%X{spanId:-}]){blue} %clr([%thread]){magenta} %clr(%-40.40logger{39}){cyan} : %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level [%X{traceId:-}/%X{spanId:-}] [%thread] %-40.40logger{39} : %msg%n"
  file:
    name: /home/<USER>/workspace/microservices-demo/logs/gateway.log
  # 添加日志分组配置
  group:
    tracing: io.micrometer.tracing, io.micrometer.observation, brave
    netflix: com.netflix.discovery, com.netflix.eureka, org.springframework.cloud.netflix
    web: org.springframework.web, org.springframework.http

# 分布式追踪配置已合并到上面的management部分

# 路由配置已移至spring.cloud.gateway.routes
