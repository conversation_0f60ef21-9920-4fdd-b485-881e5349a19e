package com.example.captain.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 1688货源数据传输对象
 */
@Data
public class SupplyDTO {
    /**
     * 货源ID
     */
    private Long id;

    /**
     * 关联的事件ID
     */
    private Long eventId;

    /**
     * 关联的Amazon商品ASIN
     */
    private String amazonAsin;

    /**
     * 阿里巴巴商品ID
     */
    private String objectId;

    /**
     * 商品标题
     */
    private String title;

    /**
     * 商品价格
     */
    private String price;

    /**
     * 商品详情页URL
     */
    private String detailUrl;

    /**
     * 商品图片URL
     */
    private String imageUrl;

    /**
     * 本地保存的图片路径
     */
    private String localImagePath;

    /**
     * 与Amazon商品的相似度
     */
    private BigDecimal similarity;

    /**
     * 商品属性信息
     */
    private String productAttributes;

    /**
     * 包装信息
     */
    private String packagingInfo;

    /**
     * 开店年限
     */
    private String tpYear;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 公司档案URL
     */
    private String tpCreditUrl;

    /**
     * 年销量
     */
    private String sales360Fuzzify;

    /**
     * 几件起批
     */
    private String quantityBegin;

    /**
     * 原始JSON数据
     */
    private String rawData;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
