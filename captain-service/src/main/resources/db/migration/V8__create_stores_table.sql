-- 创建店铺信息表
CREATE TABLE stores (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '店铺ID',
    store_name VARCHAR(100) NOT NULL COMMENT '店铺名称',
    store_url VARCHAR(500) COMMENT '店铺链接',
    username VARCHAR(50) NOT NULL COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码（加密存储）',
    north_america_brand VARCHAR(100) COMMENT '北美站品牌',
    europe_brand VARCHAR(100) COMMENT '欧洲站品牌',
    ean_country_code VARCHAR(3) COMMENT 'EAN国家代码',
    ean_manufacturer_code VARCHAR(10) COMMENT 'EAN厂商代码',
    ean_product_code_current VARCHAR(20) COMMENT 'EAN产品代码当前值',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '是否删除：1-已删除，0-未删除',
    UNIQUE KEY uk_stores_store_name (store_name),
    INDEX idx_stores_status (status),
    INDEX idx_stores_deleted (deleted)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='店铺信息表';
