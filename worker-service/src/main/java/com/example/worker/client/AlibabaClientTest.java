package com.example.worker.client;

/**
 * AlibabaClient 测试类
 * 用于测试阿里巴巴客户端的图片搜索功能
 */
public class AlibabaClientTest {

    /**
     * 主方法，用于测试 AlibabaClient
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        System.out.println("开始测试 AlibabaClient...");

        // 解析命令行参数，获取要提取的商品数量
        int maxProductsToExtract = 5; // 默认值
        if (args.length > 0) {
            try {
                maxProductsToExtract = Integer.parseInt(args[0]);
                System.out.println("设置提取商品数量为: " + maxProductsToExtract);
            } catch (NumberFormatException e) {
                System.err.println("无效的商品数量参数，使用默认值5");
            }
        }

        // 创建 AlibabaClient 实例，设置最大提取商品数量
        try (AlibabaClient client = new AlibabaClient(maxProductsToExtract)) {
            // 要搜索的图片URL
            String imageUrl = "https://m.media-amazon.com/images/I/61RjwGw-dkL.__AC_SX300_SY300_QL70_ML2_.jpg";

            System.out.println("使用图片URL: " + imageUrl);
            System.out.println("将提取最多 " + client.getMaxProductsToExtract() + " 个商品");

            // 打开图片URL并等待用户操作
            client.openUrlAndWait(imageUrl);

            System.out.println("测试完成");
        } catch (Exception e) {
            System.err.println("测试过程中出错: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
