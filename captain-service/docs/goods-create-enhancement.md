# GoodsCreateRequest 增强功能文档

## 概述

本次更新为 `GoodsCreateRequest` 添加了店铺ID和Amazon站点配置功能，实现了基于站点区域的品牌选择、货币设置和汇率价格计算。

## 新增字段

### GoodsCreateRequest.java

```java
/**
 * 店铺ID
 */
@NotNull(message = "店铺ID不能为空")
private Long storeId;

/**
 * Amazon站点
 */
@NotNull(message = "Amazon站点不能为空")
private AmazonSite amazonSite;
```

## 核心功能

### 1. 品牌名称自动选择

根据Amazon站点所属区域，从店铺配置中自动选择对应的品牌名称：

- **北美站**（美国、加拿大、墨西哥）：使用 `store.northAmericaBrand`
- **欧洲站**（英国、德国、法国等）：使用 `store.europeBrand`
- **亚太站**：优先使用北美站品牌，如果没有则使用欧洲站品牌

**实现逻辑：**
```java
private String getBrandNameByRegion(Store store, AmazonSite amazonSite) {
    SiteRegion siteRegion = amazonSite.getSiteRegion();
    
    switch (siteRegion) {
        case NORTH_AMERICA:
            return StringUtils.hasText(store.getNorthAmericaBrand()) 
                ? store.getNorthAmericaBrand() 
                : store.getStoreName();
        case EUROPE:
            return StringUtils.hasText(store.getEuropeBrand()) 
                ? store.getEuropeBrand() 
                : store.getStoreName();
        // ...
    }
}
```

### 2. 货币自动设置

根据Amazon站点的货币代码自动设置商品货币：

| 站点 | 货币代码 | 货币名称 |
|------|----------|----------|
| 美国 | USD | 美元 |
| 加拿大 | CAD | 加拿大元 |
| 墨西哥 | MXN | 墨西哥比索 |
| 英国 | GBP | 英镑 |
| 德国/法国/意大利/西班牙/荷兰 | EUR | 欧元 |
| 瑞典 | SEK | 瑞典克朗 |
| 波兰 | PLN | 波兰兹罗提 |

**实现：**
```java
goods.setCurrency(amazonSite.getCurrencyCode());
```

### 3. 汇率价格计算

根据Amazon站点的汇率自动计算商品价格：

#### 计算逻辑

1. **优先使用1688成本价格计算**：
   ```
   目标货币价格 = (1688成本价格(RMB) / 目标货币汇率) * 定价倍率(2.5)
   ```

2. **Amazon参考价格限制**：
   - 如果有Amazon参考价格，最终价格不超过参考价格的1.2倍
   - Amazon价格转换：`美元价格 * 美元汇率 / 目标货币汇率`

3. **默认价格**：
   - 如果没有成本价格和参考价格，使用预设的默认价格

#### 示例计算

**场景**：德国站点，1688成本价200元人民币
- 欧元汇率：8.1520
- 目标货币成本价：200 ÷ 8.1520 = 24.53 EUR
- 销售价格：24.53 × 2.5 = 61.33 EUR

**代码实现**：
```java
private void setPriceInfoWithExchangeRate(Goods goods, Product product, Supply supply, AmazonSite amazonSite) {
    BigDecimal exchangeRate = amazonSiteConfig.getCurrentExchangeRate(amazonSite.getCountryCode());
    BigDecimal supplyCostRMB = new BigDecimal(supply.getPrice());
    
    // 成本价转换为目标货币
    BigDecimal costInTargetCurrency = supplyCostRMB.divide(exchangeRate, 4, RoundingMode.HALF_UP);
    
    // 定价策略：成本价 * 2.5
    BigDecimal standardPrice = costInTargetCurrency.multiply(new BigDecimal("2.5"));
    
    goods.setStandardPrice(standardPrice.setScale(2, RoundingMode.HALF_UP));
    goods.setListPrice(standardPrice.multiply(new BigDecimal("1.2")).setScale(2, RoundingMode.HALF_UP));
}
```

## 使用示例

### API请求示例

```json
{
  "eventId": 1,
  "amazonAsin": "B08N5WRWNW",
  "supplyObjectId": "123456789",
  "storeId": 1,
  "amazonSite": "DE"
}
```

### 处理流程

1. **验证输入**：检查店铺ID、Amazon站点等必填字段
2. **获取店铺信息**：根据storeId获取店铺配置
3. **品牌选择**：根据站点区域选择对应品牌
4. **货币设置**：使用站点对应的货币代码
5. **价格计算**：根据汇率和成本价格计算销售价格
6. **创建商品**：生成完整的商品信息

### 生成的商品信息

```java
// 德国站点示例
goods.setBrandName("MyBrand EU");           // 欧洲站品牌
goods.setManufacturer("MyBrand EU");        // 制造商
goods.setCurrency("EUR");                   // 欧元
goods.setStandardPrice(new BigDecimal("61.33")); // 计算后的价格
goods.setGenerationRules("基于Amazon商品(B08N5WRWNW)、1688货源(123456789)和店铺(我的店铺)针对德国站点自动生成");
```

## 依赖关系

### 新增依赖

- `StoreService`：获取店铺信息
- `AmazonSiteConfig`：获取站点配置和汇率信息

### 修改的文件

1. `GoodsCreateRequest.java` - 添加店铺ID和Amazon站点字段
2. `GoodsServiceImpl.java` - 实现新的业务逻辑
3. 新增Amazon站点配置相关文件

## 错误处理

- **店铺不存在**：抛出 `BusinessException` 提示店铺不存在
- **汇率获取失败**：使用默认汇率或默认价格
- **价格计算异常**：使用预设的默认价格
- **品牌配置缺失**：使用店铺名称作为品牌

## 扩展性

- **新增站点**：在 `AmazonSite` 枚举中添加新站点
- **定价策略**：可调整价格计算倍率和策略
- **汇率更新**：支持运行时汇率更新
- **品牌管理**：支持更复杂的品牌选择逻辑

## 总结

本次增强实现了：
✅ 店铺信息集成
✅ 基于站点区域的品牌自动选择
✅ 货币自动设置
✅ 汇率价格自动计算
✅ 完整的错误处理和日志记录

这些功能使得商品创建过程更加智能化和自动化，减少了手动配置的工作量，提高了数据的一致性和准确性。
