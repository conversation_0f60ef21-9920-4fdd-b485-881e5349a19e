package com.example.captain.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.captain.entity.Product;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Optional;

/**
 * 产品Mapper接口
 */
@Mapper
public interface ProductMapper extends BaseMapper<Product> {

    /**
     * 根据事件ID查询产品列表
     *
     * @param eventId 事件ID
     * @return 产品列表
     */
    @Select("SELECT * FROM products WHERE event_id = #{eventId}")
    List<Product> findByEventId(@Param("eventId") Long eventId);

    /**
     * 根据事件ID和ASIN查询产品
     *
     * @param eventId 事件ID
     * @param asin ASIN
     * @return 产品
     */
    @Select("SELECT * FROM products WHERE event_id = #{eventId} AND asin = #{asin} LIMIT 1")
    Product findByEventIdAndAsin(@Param("eventId") Long eventId, @Param("asin") String asin);

    /**
     * 根据父ASIN查询子商品列表
     *
     * @param parentAsin 父商品ASIN
     * @return 子商品列表
     */
    @Select("SELECT * FROM products WHERE parent_asin = #{parentAsin}")
    List<Product> findChildProductsByParentAsin(@Param("parentAsin") String parentAsin);
}
