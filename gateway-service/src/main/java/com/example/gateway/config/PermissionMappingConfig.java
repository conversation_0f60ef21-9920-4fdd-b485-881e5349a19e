package com.example.gateway.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

@Configuration
public class PermissionMappingConfig {

    /**
     * 定义API路径与所需权限的映射关系
     * key: 正则表达式匹配的路径
     * value: 所需权限名称
     */
    /**
     * 定义API路径与HTTP方法和所需权限的映射关系
     * key: 路径正则表达式和HTTP方法的组合
     * value: 所需权限名称
     */
    @Bean
    public Map<String, String> apiPermissionMapping() {
        Map<String, String> mapping = new HashMap<>();

        // 用户服务权限映射 - 使用更精确的路径匹配
        mapping.put("^/api/user$:GET", "list_users");                // 获取用户列表
        mapping.put("^/api/user/[0-9]+$:GET", "view_user");           // 获取用户详情
        mapping.put("^/api/user/[0-9]+$:PUT", "update_user");         // 更新用户
        mapping.put("^/api/user/[0-9]+$:DELETE", "delete_user");      // 删除用户
        mapping.put("^/api/user/health$:GET", "health_check");       // 健康检查

        // 添加更多的路径匹配模式，确保所有路径都能被正确匹配
        mapping.put("/api/user:GET", "list_users");                  // 不使用正则表达式的匹配
        mapping.put("/api/user/[0-9]+:GET", "view_user");             // 不使用正则表达式的匹配
        mapping.put("/api/user/[0-9]+:PUT", "update_user");           // 不使用正则表达式的匹配
        mapping.put("/api/user/[0-9]+:DELETE", "delete_user");        // 不使用正则表达式的匹配

        // 角色和权限管理权限映射
        mapping.put("^/api/role.*$:.*", "admin");                   // 角色管理
        mapping.put("^/api/permission.*$:.*", "admin");              // 权限管理

        // Captain服务权限映射
        mapping.put("^/api/captain/.*$:.*", "captain_access");      // Captain服务访问权限（包括回调）

        return mapping;
    }

    /**
     * 定义公开API路径，不需要权限验证
     */
    @Bean
    public Map<Pattern, HttpMethod> publicApiMapping() {
        Map<Pattern, HttpMethod> mapping = new HashMap<>();

        // 登录和注册接口不需要权限验证
        mapping.put(Pattern.compile("^/api/user/login$"), HttpMethod.POST);
        mapping.put(Pattern.compile("^/api/user/register$"), HttpMethod.POST);

        // Swagger文档接口不需要权限验证
        mapping.put(Pattern.compile("^/swagger-ui.*$"), null);
        mapping.put(Pattern.compile("^/v3/api-docs.*$"), null);
        mapping.put(Pattern.compile("^/user-service/v3/api-docs.*$"), null);
        mapping.put(Pattern.compile("^/websocket-service/v3/api-docs.*$"), null);
        mapping.put(Pattern.compile("^/user-service/swagger-ui.*$"), null);
        mapping.put(Pattern.compile("^/websocket-service/swagger-ui.*$"), null);
        mapping.put(Pattern.compile("^/api-permissions.html$"), null);
        mapping.put(Pattern.compile("^/api-docs/.*$"), null);
        mapping.put(Pattern.compile("^/swagger-config.json$"), null);
        mapping.put(Pattern.compile("^/auth/v3/api-docs.*$"), null);
        mapping.put(Pattern.compile("^/auth/swagger-ui.*$"), null);

        // Actuator端点不需要权限验证
        mapping.put(Pattern.compile("^/actuator.*$"), null);

        // 注意：WebSocket 路径和 WebSocket API 路径都需要 JWT 认证，所以不在公开 API 映射中
        // WebSocket 路径由 WebSocketAuthenticationFilter 处理
        // WebSocket API 路径由 AuthorizationFilter 处理

        return mapping;
    }
}
