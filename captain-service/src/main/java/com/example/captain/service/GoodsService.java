package com.example.captain.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.example.captain.dto.GoodsCreateRequest;
import com.example.captain.dto.GoodsDTO;
import com.example.captain.dto.GoodsQueryParam;
import com.example.captain.entity.Goods;

import java.util.List;

/**
 * Amazon待发布商品服务接口
 */
public interface GoodsService {

    /**
     * 根据主商品ASIN + supplies表的object_id创建Amazon商品信息
     *
     * @param request 创建请求
     * @return 创建的商品信息列表（包含主商品和子商品）
     */
    List<GoodsDTO> createGoods(GoodsCreateRequest request);

    /**
     * 分页查询商品列表
     *
     * @param param 查询参数
     * @return 分页结果
     */
    IPage<GoodsDTO> getGoodsPage(GoodsQueryParam param);

    /**
     * 根据ID获取商品详情
     *
     * @param id 商品ID
     * @return 商品详情
     */
    GoodsDTO getGoodsDTOById(Long id);

    /**
     * 根据事件ID获取商品列表
     *
     * @param eventId 事件ID
     * @return 商品列表
     */
    List<Goods> getGoodsByEventId(Long eventId);

    /**
     * 根据Amazon ASIN获取商品列表
     *
     * @param amazonAsin Amazon ASIN
     * @return 商品列表
     */
    List<Goods> getGoodsByAmazonAsin(String amazonAsin);

    /**
     * 根据卖家SKU获取商品
     *
     * @param itemSku 卖家SKU
     * @return 商品信息
     */
    GoodsDTO getGoodsByItemSku(String itemSku);

    /**
     * 更新商品状态
     *
     * @param id 商品ID
     * @param status 新状态
     * @param reviewNotes 审核备注
     * @param reviewedBy 审核人ID
     * @return 是否更新成功
     */
    boolean updateGoodsStatus(Long id, Integer status, String reviewNotes, Long reviewedBy);
}
