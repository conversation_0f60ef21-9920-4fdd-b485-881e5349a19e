package com.example.captain.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.example.captain.dto.ProductDTO;
import com.example.captain.dto.ProductQueryParam;
import com.example.captain.service.ProductService;
import com.example.common.model.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 产品控制器
 */
@RestController
@RequestMapping("/captain/products")
@Slf4j
public class ProductController {

    private final ProductService productService;

    @Autowired
    public ProductController(ProductService productService) {
        this.productService = productService;
    }

    /**
     * 分页查询产品列表
     * 支持按event_id和parent_asin过滤
     * 结果按id倒序排列
     *
     * @param param 查询参数
     * @return 分页结果
     */
    @GetMapping
    public ApiResponse<IPage<ProductDTO>> getProductPage(ProductQueryParam param) {
        log.info("分页查询产品列表: param={}", param);
        IPage<ProductDTO> page = productService.getProductPage(param);
        return ApiResponse.success(page);
    }

    /**
     * 根据ID获取产品详情
     *
     * @param id 产品ID
     * @return 产品详情
     */
    @GetMapping("/{id}")
    public ApiResponse<ProductDTO> getProductById(@PathVariable(name = "id") Long id) {
        log.info("获取产品详情: id={}", id);
        ProductDTO product = productService.getProductDTOById(id);
        return ApiResponse.success(product);
    }

    /**
     * 根据ASIN获取产品详情
     *
     * @param asin 产品ASIN
     * @return 产品详情
     */
    @GetMapping("/asin/{asin}")
    public ApiResponse<ProductDTO> getProductByAsin(@PathVariable(name = "asin") String asin) {
        log.info("获取产品详情: asin={}", asin);
        ProductDTO product = productService.getProductDTOByAsin(asin);
        return ApiResponse.success(product);
    }
}
