package com.example.captain.util;

import com.example.captain.model.ProductDimensionsAndWeight;
import com.example.captain.service.MockOpenAIProductAnalysisService;

/**
 * 纯净的模拟测试运行器
 * 只使用模拟服务，不涉及任何网络连接
 */
public class CleanMockTestRunner {

    public static void main(String[] args) {
        System.out.println("🧪 纯净模拟OpenAI测试 - 验证单位转换");
        System.out.println("============================================================");
        
        // 初始化模拟服务
        MockOpenAIProductAnalysisService service = new MockOpenAIProductAnalysisService();
        
        // 测试案例1: 克转KG
        testWeightConversion_GramsToKG(service);
        
        // 测试案例2: 毫米转CM
        testDimensionConversion_MMToCM(service);
        
        // 测试案例3: 混合单位转换
        testMixedUnitConversion(service);
        
        // 测试案例4: 验证严格单位输出
        testStrictUnitOutput(service);
        
        System.out.println("✅ 所有单位转换测试完成！");
        System.out.println("\n📋 测试总结:");
        System.out.println("- ✅ 重量单位转换: g → KG");
        System.out.println("- ✅ 尺寸单位转换: mm → CM");
        System.out.println("- ✅ 严格单位输出: 所有单位都按要求转换");
        System.out.println("- ✅ 无网络依赖: 完全离线运行");
    }
    
    private static void testWeightConversion_GramsToKG(MockOpenAIProductAnalysisService service) {
        System.out.println("\n🔬 测试案例1: 重量单位转换 (g → KG)");
        System.out.println("----------------------------------------");
        
        String title = "Wireless Charger with 150g Weight";
        String feature = "Compact design. Weight: 150 grams. Fast charging capability.";
        String description = "Lightweight wireless charger weighing only 150g for portability.";
        
        ProductDimensionsAndWeight result = service.extractDimensionsAndWeight(title, feature, description);
        
        System.out.println("📝 输入: 150 grams");
        if (result != null && result.getShippingWeight() != null) {
            System.out.println("📊 输出: " + result.getShippingWeight() + " " + result.getShippingWeightUnitOfMeasure());
            
            // 验证转换是否正确 (150g = 0.15kg)
            if ("KG".equals(result.getShippingWeightUnitOfMeasure()) && 
                result.getShippingWeight().compareTo(new java.math.BigDecimal("0.15")) == 0) {
                System.out.println("✅ 重量转换正确: 150g → 0.15KG");
            } else {
                System.out.println("❌ 重量转换错误");
            }
        } else {
            System.out.println("❌ 未提取到重量信息");
        }
    }
    
    private static void testDimensionConversion_MMToCM(MockOpenAIProductAnalysisService service) {
        System.out.println("\n📏 测试案例2: 尺寸单位转换 (mm → CM)");
        System.out.println("----------------------------------------");
        
        String title = "Gaming Mouse with Precise Dimensions";
        String feature = "High precision gaming mouse. Dimensions: 125mm x 68mm x 42mm.";
        String description = "Professional gaming mouse with exact measurements of 125 x 68 x 42 millimeters.";
        
        ProductDimensionsAndWeight result = service.extractDimensionsAndWeight(title, feature, description);
        
        System.out.println("📝 输入: 125mm x 68mm x 42mm");
        if (result != null && result.hasValidData()) {
            System.out.println("📊 输出: " + 
                result.getItemDepthFrontToBack() + result.getItemDepthUnit() + " × " +
                result.getItemWidthSideToSide() + result.getItemWidthUnit() + " × " +
                result.getItemHeightFloorToTop() + result.getItemHeightUnitOfMeasure());
            
            // 验证转换是否正确 (125mm = 12.5cm, 68mm = 6.8cm, 42mm = 4.2cm)
            boolean lengthCorrect = result.getItemDepthFrontToBack().compareTo(new java.math.BigDecimal("12.5")) == 0;
            boolean widthCorrect = result.getItemWidthSideToSide().compareTo(new java.math.BigDecimal("6.8")) == 0;
            boolean heightCorrect = result.getItemHeightFloorToTop().compareTo(new java.math.BigDecimal("4.2")) == 0;
            boolean unitsCorrect = "CM".equals(result.getItemDepthUnit()) && 
                                 "CM".equals(result.getItemWidthUnit()) && 
                                 "CM".equals(result.getItemHeightUnitOfMeasure());
            
            if (lengthCorrect && widthCorrect && heightCorrect && unitsCorrect) {
                System.out.println("✅ 尺寸转换正确: 125×68×42mm → 12.5×6.8×4.2CM");
            } else {
                System.out.println("❌ 尺寸转换错误");
            }
        } else {
            System.out.println("❌ 未提取到尺寸信息");
        }
    }
    
    private static void testMixedUnitConversion(MockOpenAIProductAnalysisService service) {
        System.out.println("\n🔄 测试案例3: 混合单位转换");
        System.out.println("----------------------------------------");
        
        String title = "Laptop Stand - Adjustable Height";
        String feature = "Ergonomic laptop stand. Weight: 1200g. Size: 280mm(L) x 220mm(W) x 60mm(H).";
        String description = "Premium aluminum stand weighing 1.2kg with dimensions 28cm x 22cm x 6cm.";
        
        ProductDimensionsAndWeight result = service.extractDimensionsAndWeight(title, feature, description);
        
        System.out.println("📝 输入: 1200g, 280mm×220mm×60mm");
        if (result != null && result.hasValidData()) {
            System.out.println("📊 重量输出: " + result.getShippingWeight() + " " + result.getShippingWeightUnitOfMeasure());
            System.out.println("📊 尺寸输出: " + 
                result.getItemDepthFrontToBack() + result.getItemDepthUnit() + " × " +
                result.getItemWidthSideToSide() + result.getItemWidthUnit() + " × " +
                result.getItemHeightFloorToTop() + result.getItemHeightUnitOfMeasure());
            
            // 验证混合转换
            boolean weightCorrect = result.getShippingWeight().compareTo(new java.math.BigDecimal("1.2")) == 0;
            boolean dimensionsCorrect = 
                result.getItemDepthFrontToBack().compareTo(new java.math.BigDecimal("28.0")) == 0 &&
                result.getItemWidthSideToSide().compareTo(new java.math.BigDecimal("22.0")) == 0 &&
                result.getItemHeightFloorToTop().compareTo(new java.math.BigDecimal("6.0")) == 0;
            
            if (weightCorrect && dimensionsCorrect) {
                System.out.println("✅ 混合单位转换正确");
            } else {
                System.out.println("❌ 混合单位转换错误");
            }
        } else {
            System.out.println("❌ 未提取到信息");
        }
    }
    
    private static void testStrictUnitOutput(MockOpenAIProductAnalysisService service) {
        System.out.println("\n🎯 测试案例4: 验证严格单位输出");
        System.out.println("----------------------------------------");
        
        String title = "Bluetooth Speaker - Portable Design";
        String feature = "Portable speaker. Weight: 800 grams. Dimensions: 15cm x 8cm x 5cm.";
        String description = "Compact Bluetooth speaker with 800g weight and 150mm x 80mm x 50mm size.";
        
        ProductDimensionsAndWeight result = service.extractDimensionsAndWeight(title, feature, description);
        
        System.out.println("📝 输入: 800g, 15cm×8cm×5cm");
        if (result != null && result.hasValidData()) {
            // 检查所有单位是否严格按要求输出
            boolean weightUnitCorrect = "KG".equals(result.getShippingWeightUnitOfMeasure());
            boolean depthUnitCorrect = "CM".equals(result.getItemDepthUnit());
            boolean widthUnitCorrect = "CM".equals(result.getItemWidthUnit());
            boolean heightUnitCorrect = "CM".equals(result.getItemHeightUnitOfMeasure());
            
            System.out.println("📊 重量单位: " + result.getShippingWeightUnitOfMeasure() + 
                             (weightUnitCorrect ? " ✅" : " ❌"));
            System.out.println("📊 深度单位: " + result.getItemDepthUnit() + 
                             (depthUnitCorrect ? " ✅" : " ❌"));
            System.out.println("📊 宽度单位: " + result.getItemWidthUnit() + 
                             (widthUnitCorrect ? " ✅" : " ❌"));
            System.out.println("📊 高度单位: " + result.getItemHeightUnitOfMeasure() + 
                             (heightUnitCorrect ? " ✅" : " ❌"));
            
            if (weightUnitCorrect && depthUnitCorrect && widthUnitCorrect && heightUnitCorrect) {
                System.out.println("✅ 所有单位输出严格符合要求");
            } else {
                System.out.println("❌ 单位输出不符合要求");
            }
        } else {
            System.out.println("❌ 未提取到信息");
        }
    }
}
