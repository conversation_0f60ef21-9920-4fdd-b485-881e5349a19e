#!/bin/bash

# 清理测试数据脚本
# 删除由测试脚本创建的测试商品数据
# 
# 使用方法:
# chmod +x script/cleanup-test-data.sh
# ./script/cleanup-test-data.sh

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
GATEWAY_URL="http://localhost:8080"
USERNAME="admin"
PASSWORD="admin123"

echo -e "${BLUE}=========================================${NC}"
echo -e "${BLUE}清理测试数据${NC}"
echo -e "${BLUE}=========================================${NC}"

# 1. 获取JWT令牌
echo -e "\n${BLUE}步骤1: 获取JWT令牌${NC}"
LOGIN_RESPONSE=$(curl -s -X POST \
    "${GATEWAY_URL}/auth/login" \
    -H "Content-Type: application/json" \
    -d "{\"username\":\"${USERNAME}\",\"password\":\"${PASSWORD}\"}")

if [[ $LOGIN_RESPONSE == *"\"token\""* ]]; then
    echo -e "${GREEN}✓ 登录成功${NC}"
    TOKEN=$(echo $LOGIN_RESPONSE | grep -o '"token":"[^"]*' | cut -d'"' -f4)
    echo -e "${GREEN}✓ JWT令牌: ${TOKEN:0:20}...${NC}"
else
    echo -e "${RED}✗ 登录失败: $LOGIN_RESPONSE${NC}"
    exit 1
fi

# 2. 查询所有产品，寻找测试数据
echo -e "\n${BLUE}步骤2: 查询测试数据${NC}"

# 查询产品列表（假设测试数据在事件ID=1中）
PRODUCTS_RESPONSE=$(curl -s -X GET \
    "${GATEWAY_URL}/api/captain/products?eventId=1&page=1&size=100" \
    -H "Authorization: Bearer ${TOKEN}")

if [[ $PRODUCTS_RESPONSE == *"\"code\":0"* ]]; then
    echo -e "${GREEN}✓ 产品列表查询成功${NC}"
    
    # 提取测试数据的ASIN（包含B08FIX、B08NEWFIELDS、B08TEST等）
    TEST_ASINS=$(echo "$PRODUCTS_RESPONSE" | grep -o '"asin":"B08[^"]*' | grep -E '(B08FIX|B08NEWFIELDS|B08TEST|B08NEWTEST)' | cut -d'"' -f4)
    
    if [[ -n "$TEST_ASINS" ]]; then
        echo -e "${YELLOW}发现以下测试数据ASIN:${NC}"
        echo "$TEST_ASINS"
        
        echo -e "\n${YELLOW}⚠️  警告：即将删除上述测试数据${NC}"
        echo -e "${YELLOW}这些数据包含以下特征之一：${NC}"
        echo -e "• ASIN以B08FIX开头（修复验证测试）"
        echo -e "• ASIN为B08NEWFIELDS（新字段测试）"
        echo -e "• ASIN以B08TEST开头（一般测试）"
        echo -e "• ASIN为B08NEWTEST（新测试）"
        
        read -p "确认删除这些测试数据吗？(y/N): " -n 1 -r
        echo
        
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            echo -e "\n${BLUE}步骤3: 删除测试数据${NC}"
            
            # 注意：当前系统可能没有删除API，这里提供SQL删除方案
            echo -e "${YELLOW}注意：当前系统没有产品删除API${NC}"
            echo -e "${YELLOW}请手动执行以下SQL语句来删除测试数据：${NC}"
            echo -e "\n${BLUE}SQL删除语句：${NC}"
            
            for asin in $TEST_ASINS; do
                echo "DELETE FROM products WHERE asin = '$asin';"
            done
            
            echo -e "\n${YELLOW}或者使用通配符删除：${NC}"
            echo "DELETE FROM products WHERE asin LIKE 'B08FIX%';"
            echo "DELETE FROM products WHERE asin LIKE 'B08TEST%';"
            echo "DELETE FROM products WHERE asin IN ('B08NEWFIELDS', 'B08NEWTEST');"
            
            echo -e "\n${BLUE}连接数据库的命令：${NC}"
            echo "mysql -u lihui -p'Lihui@123!' -h localhost captain_service"
            
        else
            echo -e "${YELLOW}取消删除操作${NC}"
        fi
        
    else
        echo -e "${GREEN}✓ 没有发现测试数据${NC}"
    fi
    
else
    echo -e "${RED}✗ 产品列表查询失败: $PRODUCTS_RESPONSE${NC}"
fi

# 3. 显示当前数据库中的产品统计
echo -e "\n${BLUE}步骤4: 显示产品统计信息${NC}"

STATS_RESPONSE=$(curl -s -X GET \
    "${GATEWAY_URL}/api/captain/products?eventId=1&page=1&size=1" \
    -H "Authorization: Bearer ${TOKEN}")

if [[ $STATS_RESPONSE == *"\"total\""* ]]; then
    TOTAL_COUNT=$(echo "$STATS_RESPONSE" | grep -o '"total":[0-9]*' | cut -d':' -f2)
    echo -e "${BLUE}当前事件ID=1中的产品总数: ${TOTAL_COUNT}${NC}"
else
    echo -e "${YELLOW}无法获取产品统计信息${NC}"
fi

echo -e "\n${BLUE}=========================================${NC}"
echo -e "${BLUE}清理脚本执行完成${NC}"
echo -e "${BLUE}=========================================${NC}"

echo -e "\n${YELLOW}提示：${NC}"
echo -e "• 如果需要彻底清理，请执行上面提供的SQL语句"
echo -e "• 建议在测试环境中定期清理测试数据"
echo -e "• 生产环境请谨慎操作"
