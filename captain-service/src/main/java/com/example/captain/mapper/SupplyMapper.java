package com.example.captain.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.captain.entity.Supply;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 1688货源Mapper接口
 */
@Mapper
public interface SupplyMapper extends BaseMapper<Supply> {

    /**
     * 根据事件ID查询货源列表
     *
     * @param eventId 事件ID
     * @return 货源列表
     */
    @Select("SELECT * FROM supplies WHERE event_id = #{eventId}")
    List<Supply> findByEventId(@Param("eventId") Long eventId);

    /**
     * 根据事件ID和Amazon ASIN查询货源列表
     *
     * @param eventId 事件ID
     * @param amazonAsin Amazon ASIN
     * @return 货源列表
     */
    @Select("SELECT * FROM supplies WHERE event_id = #{eventId} AND amazon_asin = #{amazonAsin}")
    List<Supply> findByEventIdAndAmazonAsin(@Param("eventId") Long eventId, @Param("amazonAsin") String amazonAsin);
}
