package com.example.captain.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Amazon待发布商品数据传输对象
 */
@Data
public class GoodsDTO {
    /**
     * 商品ID
     */
    private Long id;

    /**
     * 关联的事件ID
     */
    private Long eventId;

    /**
     * 关联的Amazon商品ASIN
     */
    private String amazonAsin;

    /**
     * 关联的1688货源ID
     */
    private Long supplyId;

    /**
     * 产品类型
     */
    private String feedProductType;

    /**
     * 卖家SKU
     */
    private String itemSku;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 外部产品ID(ASIN)
     */
    private String externalProductId;

    /**
     * 外部产品ID类型
     */
    private String externalProductIdType;

    /**
     * 商品名称
     */
    private String itemName;

    /**
     * 制造商
     */
    private String manufacturer;

    /**
     * 推荐浏览节点
     */
    private String recommendedBrowseNodes;

    /**
     * 最大订购数量
     */
    private Integer maxOrderQuantity;

    /**
     * 原产国
     */
    private String countryOfOrigin;

    /**
     * 标准价格
     */
    private BigDecimal standardPrice;

    /**
     * 库存数量
     */
    private Integer quantity;

    /**
     * 主图片URL
     */
    private String mainImageUrl;

    /**
     * 其他图片URL1
     */
    private String otherImageUrl1;

    /**
     * 其他图片URL2
     */
    private String otherImageUrl2;

    /**
     * 其他图片URL3
     */
    private String otherImageUrl3;

    /**
     * 其他图片URL4
     */
    private String otherImageUrl4;

    /**
     * 其他图片URL5
     */
    private String otherImageUrl5;

    /**
     * 其他图片URL6
     */
    private String otherImageUrl6;

    /**
     * 其他图片URL7
     */
    private String otherImageUrl7;

    /**
     * 其他图片URL8
     */
    private String otherImageUrl8;

    /**
     * 样本图片URL
     */
    private String swatchImageUrl;

    /**
     * 父子关系(parent/child)
     */
    private String parentChild;

    /**
     * 父SKU
     */
    private String parentSku;

    /**
     * 关系类型
     */
    private String relationshipType;

    /**
     * 变体主题
     */
    private String variationTheme;

    /**
     * 更新删除标志
     */
    private String updateDelete;

    /**
     * 产品描述
     */
    private String productDescription;

    /**
     * 制造商零件号
     */
    private String partNumber;

    /**
     * 关键产品特性1
     */
    private String bulletPoint1;

    /**
     * 关键产品特性2
     */
    private String bulletPoint2;

    /**
     * 关键产品特性3
     */
    private String bulletPoint3;

    /**
     * 关键产品特性4
     */
    private String bulletPoint4;

    /**
     * 关键产品特性5
     */
    private String bulletPoint5;

    /**
     * 搜索关键词
     */
    private String genericKeywords;

    /**
     * 金属类型
     */
    private String metalType;

    /**
     * 部门名称
     */
    private String departmentName;

    /**
     * 宝石类型
     */
    private String gemType;

    /**
     * 特殊功能
     */
    private String specialFeatures;

    /**
     * 颜色名称
     */
    private String colorName;

    /**
     * 颜色映射
     */
    private String colorMap;

    /**
     * 尺寸名称
     */
    private String sizeName;

    /**
     * 网站运输重量
     */
    private BigDecimal websiteShippingWeight;

    /**
     * 网站运输重量单位
     */
    private String websiteShippingWeightUnitOfMeasure;

    /**
     * 尺寸映射
     */
    private String sizeMap;

    /**
     * 单位数量
     */
    private Integer unitCount;

    /**
     * 单位数量类型
     */
    private String unitCountType;

    /**
     * 深度(前后)
     */
    private BigDecimal depthFrontToBack;

    /**
     * 深度单位
     */
    private String depthFrontToBackUnitOfMeasure;

    /**
     * 宽度(左右)
     */
    private BigDecimal depthWidthSideToSide;

    /**
     * 宽度单位
     */
    private String depthWidthSideToSideUnitOfMeasure;

    /**
     * 高度(底部到顶部)
     */
    private BigDecimal depthHeightFloorToTop;

    /**
     * 高度单位
     */
    private String depthHeightFloorToTopUnitOfMeasure;

    /**
     * 履行中心ID
     */
    private String fulfillmentCenterId;

    /**
     * 货币
     */
    private String currency;

    /**
     * 标价
     */
    private BigDecimal listPrice;

    /**
     * 含税标价
     */
    private BigDecimal listPriceWithTax;

    /**
     * 商品状态
     */
    private String conditionType;

    /**
     * 保修描述
     */
    private String warrantyDescription;

    /**
     * 是否包含电池
     */
    private String areBatteriesIncluded;

    /**
     * 是否需要电池
     */
    private String batteriesRequired;

    /**
     * 危险品法规声明
     */
    private String supplierDeclaredDgHzRegulation1;

    /**
     * 状态：0-待审核，1-已审核，2-已发布，3-已拒绝
     */
    private Integer status;

    /**
     * 审核备注
     */
    private String reviewNotes;

    /**
     * 审核人ID
     */
    private Long reviewedBy;

    /**
     * 审核时间
     */
    private LocalDateTime reviewedAt;

    /**
     * 数据来源：1-自动生成，2-手工录入
     */
    private Integer sourceType;

    /**
     * 生成规则记录
     */
    private String generationRules;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
