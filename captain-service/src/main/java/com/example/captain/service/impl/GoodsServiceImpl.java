package com.example.captain.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.captain.config.AmazonSiteConfig;
import com.example.captain.dto.GoodsCreateRequest;
import com.example.captain.dto.GoodsDTO;
import com.example.captain.dto.GoodsQueryParam;
import com.example.captain.entity.Goods;
import com.example.captain.entity.Product;
import com.example.captain.entity.Store;
import com.example.captain.entity.Supply;
import com.example.captain.enums.AmazonSite;
import com.example.captain.enums.SiteRegion;
import com.example.captain.mapper.GoodsMapper;
import com.example.captain.mapper.ProductMapper;
import com.example.captain.mapper.SupplyMapper;
import com.example.captain.service.GoodsService;
import com.example.captain.service.ProductService;
import com.example.captain.service.StoreService;
import com.example.captain.service.SupplyService;
import com.example.captain.util.AsinCompressor;
import com.example.captain.util.EanCodeUtil;
import com.example.common.exception.BusinessException;
import com.example.common.model.ErrorCodeEnum;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Amazon待发布商品服务实现类
 */
@Service
@Slf4j
public class GoodsServiceImpl implements GoodsService {

    private final GoodsMapper goodsMapper;
    private final ProductMapper productMapper;
    private final SupplyMapper supplyMapper;
    private final ProductService productService;
    private final SupplyService supplyService;
    private final StoreService storeService;
    private final AmazonSiteConfig amazonSiteConfig;
    private final ObjectMapper objectMapper;

    @Autowired
    public GoodsServiceImpl(GoodsMapper goodsMapper,
                           ProductMapper productMapper,
                           SupplyMapper supplyMapper,
                           ProductService productService,
                           SupplyService supplyService,
                           StoreService storeService,
                           AmazonSiteConfig amazonSiteConfig,
                           ObjectMapper objectMapper) {
        this.goodsMapper = goodsMapper;
        this.productMapper = productMapper;
        this.supplyMapper = supplyMapper;
        this.productService = productService;
        this.supplyService = supplyService;
        this.storeService = storeService;
        this.amazonSiteConfig = amazonSiteConfig;
        this.objectMapper = objectMapper;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<GoodsDTO> createGoods(GoodsCreateRequest request) {
        log.info("开始创建Amazon商品信息: eventId={}, amazonAsin={}, supplyObjectId={}, storeId={}, amazonSite={}",
                request.getEventId(), request.getAmazonAsin(), request.getSupplyObjectId(),
                request.getStoreId(), request.getAmazonSite());

        List<GoodsDTO> resultList = new ArrayList<>();

        // 1. 获取Amazon商品信息
        Product product = getProductByAsin(request.getAmazonAsin());
        if (product == null) {
            throw new BusinessException(ErrorCodeEnum.RESOURCE_NOT_FOUND,
                    "未找到ASIN为 " + request.getAmazonAsin() + " 的Amazon商品");
        }

        // 2. 获取1688货源信息
        Supply supply = getSupplyByObjectIdAndAsin(request.getSupplyObjectId(), request.getAmazonAsin());
        if (supply == null) {
            throw new BusinessException(ErrorCodeEnum.RESOURCE_NOT_FOUND,
                    "未找到object_id为 " + request.getSupplyObjectId() + " 且amazon_asin为 " + request.getAmazonAsin() + " 的1688货源");
        }

        // 3. 获取店铺信息
        Store store = getStoreById(request.getStoreId());
        if (store == null) {
            throw new BusinessException(ErrorCodeEnum.RESOURCE_NOT_FOUND,
                    "未找到ID为 " + request.getStoreId() + " 的店铺");
        }

        // 4. 验证数据关联性
        if (!request.getAmazonAsin().equals(supply.getAmazonAsin())) {
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR,
                    "Amazon商品ASIN与1688货源关联的ASIN不匹配");
        }

        // 5. 检查是否已存在相同的商品记录
        Goods existingGoods = goodsMapper.findByEventIdAndAmazonAsin(request.getEventId(), request.getAmazonAsin());
        if (existingGoods != null) {
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR,
                    "该事件下已存在相同ASIN的商品记录");
        }

        // 6. 创建主商品数据
        Goods mainGoods = createGoodsFromProductAndSupply(request.getEventId(), product, supply, store, request.getAmazonSite(), false);
                
        List<Product> childProducts = null;
        // 7. 如果是父变体商品，创建子商品
        if (product.getIsParentVariant() != null && product.getIsParentVariant() &&
            product.getIsMainProduct() != null && product.getIsMainProduct()) {

            log.info("检测到父变体商品，开始创建子商品: parentAsin={}", product.getAsin());

            // 根据父ASIN查询子商品列表
            childProducts = productMapper.findChildProductsByParentAsin(product.getAsin());

            // 设置父级主商品的总库存信息
            mainGoods.setQuantity(20 * childProducts.size()); // 子商品默认库存20 * 子商品数量            
        }

        goodsMapper.insert(mainGoods);
        resultList.add(convertToDTO(mainGoods));

        log.info("成功创建主商品: id={}, itemSku={}", mainGoods.getId(), mainGoods.getItemSku());

        if (childProducts !=null){
            for (Product childProduct : childProducts) {
                try {
                    // 检查子商品是否已存在（添加parent_child='child'约束条件）
                    Goods existingChildGoods = goodsMapper.findChildGoodsByEventIdAndAmazonAsin(request.getEventId(), childProduct.getAsin());
                    if (existingChildGoods != null) {
                        log.warn("子商品已存在，跳过创建: asin={}", childProduct.getAsin());
                        continue;
                    }

                    // 创建子商品
                    Goods childGoods = createGoodsFromProductAndSupply(request.getEventId(), childProduct, supply, store, request.getAmazonSite(), true);
                    goodsMapper.insert(childGoods);
                    resultList.add(convertToDTO(childGoods));

                    log.info("成功创建子商品: id={}, itemSku={}, asin={}",
                            childGoods.getId(), childGoods.getItemSku(), childProduct.getAsin());

                } catch (Exception e) {
                    log.error("创建子商品失败: asin={}, error={}", childProduct.getAsin(), e.getMessage(), e);
                    // 继续处理其他子商品，不中断整个流程
                }
            }

            log.info("完成变体商品创建，共创建 {} 个商品（包含主商品）", resultList.size());
        }

        return resultList;
    }

    @Override
    public IPage<GoodsDTO> getGoodsPage(GoodsQueryParam param) {
        log.info("分页查询商品列表: param={}", param);

        // 创建分页对象
        Page<Goods> page = new Page<>(param.getPage(), param.getSize());

        // 构建查询条件
        LambdaQueryWrapper<Goods> queryWrapper = new LambdaQueryWrapper<>();

        // 添加事件ID过滤条件
        if (param.getEventId() != null) {
            queryWrapper.eq(Goods::getEventId, param.getEventId());
        }

        // 添加Amazon ASIN过滤条件
        if (StringUtils.hasText(param.getAmazonAsin())) {
            queryWrapper.eq(Goods::getAmazonAsin, param.getAmazonAsin());
        }

        // 添加状态过滤条件
        if (param.getStatus() != null) {
            queryWrapper.eq(Goods::getStatus, param.getStatus());
        }

        // 添加卖家SKU过滤条件
        if (StringUtils.hasText(param.getItemSku())) {
            queryWrapper.like(Goods::getItemSku, param.getItemSku());
        }

        // 按ID倒序排序
        queryWrapper.orderByDesc(Goods::getId);

        // 执行查询
        IPage<Goods> goodsPage = goodsMapper.selectPage(page, queryWrapper);

        // 转换为DTO对象
        IPage<GoodsDTO> dtoPage = goodsPage.convert(this::convertToDTO);

        return dtoPage;
    }

    @Override
    public GoodsDTO getGoodsDTOById(Long id) {
        log.info("根据ID获取商品详情: id={}", id);

        Goods goods = goodsMapper.selectById(id);
        if (goods == null) {
            throw new BusinessException(ErrorCodeEnum.RESOURCE_NOT_FOUND, "商品不存在");
        }

        return convertToDTO(goods);
    }

    @Override
    public List<Goods> getGoodsByEventId(Long eventId) {
        return goodsMapper.findByEventId(eventId);
    }

    @Override
    public List<Goods> getGoodsByAmazonAsin(String amazonAsin) {
        return goodsMapper.findByAmazonAsin(amazonAsin);
    }

    @Override
    public GoodsDTO getGoodsByItemSku(String itemSku) {
        log.info("根据卖家SKU获取商品: itemSku={}", itemSku);

        Goods goods = goodsMapper.findByItemSku(itemSku);
        if (goods == null) {
            throw new BusinessException(ErrorCodeEnum.RESOURCE_NOT_FOUND, "商品不存在");
        }

        return convertToDTO(goods);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateGoodsStatus(Long id, Integer status, String reviewNotes, Long reviewedBy) {
        log.info("更新商品状态: id={}, status={}, reviewedBy={}", id, status, reviewedBy);

        Goods goods = goodsMapper.selectById(id);
        if (goods == null) {
            throw new BusinessException(ErrorCodeEnum.RESOURCE_NOT_FOUND, "商品不存在");
        }

        goods.setStatus(status);
        goods.setReviewNotes(reviewNotes);
        goods.setReviewedBy(reviewedBy);
        goods.setReviewedAt(LocalDateTime.now());
        goods.setUpdatedAt(LocalDateTime.now());

        int result = goodsMapper.updateById(goods);
        return result > 0;
    }

    /**
     * 根据ASIN获取Product信息
     */
    private Product getProductByAsin(String asin) {
        try {
            return productService.getProductDTOByAsin(asin) != null ?
                   productService.getProductById(productService.getProductDTOByAsin(asin).getId()) : null;
        } catch (Exception e) {
            log.warn("获取Product信息失败: asin={}, error={}", asin, e.getMessage());
            return null;
        }
    }

    /**
     * 根据object_id获取Supply信息
     */
    private Supply getSupplyByObjectId(String objectId) {
        try {
            return supplyService.getSupplyDTOByObjectId(objectId) != null ?
                   supplyService.getSupplyById(supplyService.getSupplyDTOByObjectId(objectId).getId()) : null;
        } catch (Exception e) {
            log.warn("获取Supply信息失败: objectId={}, error={}", objectId, e.getMessage());
            return null;
        }
    }

    /**
     * 根据object_id和amazon_asin获取Supply信息
     */
    private Supply getSupplyByObjectIdAndAsin(String objectId, String amazonAsin) {
        try {
            // 直接使用MyBatis-Plus查询，避免重复数据问题
            LambdaQueryWrapper<Supply> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Supply::getObjectId, objectId)
                       .eq(Supply::getAmazonAsin, amazonAsin);

            // 使用SupplyMapper直接查询
            return supplyMapper.selectOne(queryWrapper);

        } catch (Exception e) {
            log.warn("获取Supply信息失败: objectId={}, amazonAsin={}, error={}", objectId, amazonAsin, e.getMessage());
            return null;
        }
    }

    /**
     * 根据ID获取Store信息
     */
    private Store getStoreById(Long storeId) {
        try {
            return storeService.getStoreEntityById(storeId);
        } catch (Exception e) {
            log.warn("获取Store信息失败: storeId={}, error={}", storeId, e.getMessage());
            return null;
        }
    }

    /**
     * 从Product、Supply和Store创建Goods对象
     */
    private Goods createGoodsFromProductAndSupply(Long eventId, Product product, Supply supply, Store store, AmazonSite amazonSite, boolean isChildProduct) {
        Goods goods = new Goods();
        LocalDateTime now = LocalDateTime.now();

        // 基本信息
        goods.setEventId(eventId);
        goods.setAmazonAsin(product.getAsin());
        goods.setSupplyId(supply.getId());

        // 产品类型和基本信息
        goods.setFeedProductType(StringUtils.hasText(product.getProductType()) ? product.getProductType() : "home");
        goods.setItemSku(generateItemSku(product, isChildProduct));

        // 根据AmazonSite所属区域从店铺获取对应品牌名称
        String brandName = getBrandNameByRegion(store, amazonSite);
        goods.setBrandName(brandName);
        goods.setManufacturer(brandName); // 制造商也使用相同的品牌名称

        
        goods.setItemName(product.getTitle());
        goods.setRecommendedBrowseNodes(product.getRecommendedBrowseNodes());
        goods.setMaxOrderQuantity(100);
        goods.setCountryOfOrigin("CN");

        // 根据AmazonSite设置货币
        goods.setCurrency(amazonSite.getCurrencyCode());

        // 根据AmazonSite的汇率计算价格
        setPriceInfoWithExchangeRate(goods, product, supply, amazonSite);

        // 库存信息
        goods.setQuantity(20); // 默认库存

        // 图片信息
        setImageInfo(goods, product, supply);

        // 变体信息
        setVariantInfo(goods, product,isChildProduct,store);

        // 描述信息
        goods.setUpdateDelete("Update");
        goods.setProductDescription(product.getDescription());
        goods.setPartNumber(generateItemSku(product, false)); //使用主商品的sku

        // 产品特性
        setBulletPoints(goods, product);

        // 产品属性
        goods.setMetalType("other");
        goods.setListPriceWithTax(new BigDecimal("1"));
        goods.setListPrice(new BigDecimal("0"));
        goods.setFulfillmentCenterId("DEFAULT");
        goods.setUnitCountType("Gram");
        goods.setUnitCount(1);
        goods.setWebsiteShippingWeightUnitOfMeasure("KG");
        goods.setWebsiteShippingWeight(new BigDecimal("1000"));


        // 履行和价格
        goods.setConditionType("New");

        // 状态信息
        goods.setStatus(0); // 待审核
        goods.setSourceType(1); // 自动生成
        goods.setGenerationRules("基于Amazon商品(" + product.getAsin() + ")、1688货源(" + supply.getObjectId() +
                ")和店铺(" + store.getStoreName() + ")针对" + amazonSite.getCountryName() + "站点自动生成");

        // 时间戳
        goods.setCreatedAt(now);
        goods.setUpdatedAt(now);

        return goods;
    }

    /**
     * 生成商品SKU
     *
     * @param product 商品信息
     * @param isChildProduct 是否为子商品
     * @return 生成的SKU
     */
    private String generateItemSku(Product product, boolean isChildProduct) {
        if (isChildProduct) {
            // 子商品直接返回product_sku
            return product.getProductSku();
        } else {
            // 主商品使用压缩的ASIN
            return AsinCompressor.compress(product.getAsin());
        }
    }

    /**
     * 从标题中提取品牌名称
     */
    private String extractBrandFromTitle(String title) {
        if (!StringUtils.hasText(title)) {
            return "Generic";
        }

        // 简单的品牌提取逻辑：取第一个单词
        String[] words = title.split("\\s+");
        if (words.length > 0) {
            return words[0].replaceAll("[^a-zA-Z0-9]", "");
        }

        return "Generic";
    }

    /**
     * 根据Amazon站点区域从店铺获取对应品牌名称
     */
    private String getBrandNameByRegion(Store store, AmazonSite amazonSite) {
        SiteRegion siteRegion = amazonSite.getSiteRegion();

        switch (siteRegion) {
            case NORTH_AMERICA:
                // 北美站使用北美站品牌
                if (StringUtils.hasText(store.getNorthAmericaBrand())) {
                    return store.getNorthAmericaBrand();
                }
                break;
            case EUROPE:
                // 欧洲站使用欧洲站品牌
                if (StringUtils.hasText(store.getEuropeBrand())) {
                    return store.getEuropeBrand();
                }
                break;
            case ASIA_PACIFIC:
                // 亚太站优先使用北美站品牌，如果没有则使用欧洲站品牌
                if (StringUtils.hasText(store.getNorthAmericaBrand())) {
                    return store.getNorthAmericaBrand();
                } else if (StringUtils.hasText(store.getEuropeBrand())) {
                    return store.getEuropeBrand();
                }
                break;
        }

        // 如果都没有配置，使用店铺名称作为品牌
        if (StringUtils.hasText(store.getStoreName())) {
            return store.getStoreName();
        }

        // 最后的默认值
        return "Generic";
    }

    /**
     * 设置价格信息
     */
    private void setPriceInfo(Goods goods, Product product, Supply supply) {
        try {
            // 从Amazon商品获取基础价格
            if (StringUtils.hasText(product.getPrice())) {
                String priceStr = product.getPrice().replaceAll("[^0-9.]", "");
                BigDecimal amazonPrice = new BigDecimal(priceStr);

                // 从1688货源获取成本价格
                BigDecimal supplyPrice = null;
                if (StringUtils.hasText(supply.getPrice())) {
                    String supplyPriceStr = supply.getPrice().replaceAll("[^0-9.]", "");
                    supplyPrice = new BigDecimal(supplyPriceStr);
                }

                // 计算销售价格（可以根据业务规则调整）
                BigDecimal standardPrice = amazonPrice;
                if (supplyPrice != null) {
                    // 简单的定价策略：成本价 * 2.5
                    BigDecimal calculatedPrice = supplyPrice.multiply(new BigDecimal("2.5"));
                    standardPrice = calculatedPrice.min(amazonPrice); // 不超过Amazon价格
                }

                goods.setStandardPrice(standardPrice.setScale(2, RoundingMode.HALF_UP));
                goods.setListPrice(standardPrice.multiply(new BigDecimal("1.2")).setScale(2, RoundingMode.HALF_UP));
            }
        } catch (Exception e) {
            log.warn("设置价格信息失败: {}", e.getMessage());
            // 设置默认价格
            goods.setStandardPrice(new BigDecimal("29.99"));
            goods.setListPrice(new BigDecimal("35.99"));
        }
    }

    /**
     * 根据Amazon站点汇率设置价格信息
     */
    private void setPriceInfoWithExchangeRate(Goods goods, Product product, Supply supply, AmazonSite amazonSite) {
        try {
            // 获取当前汇率
            BigDecimal exchangeRate = amazonSiteConfig.getCurrentExchangeRate(amazonSite.getCountryCode());

            log.info("计算价格: 站点={}, 货币={}, 汇率={}",
                    amazonSite.getCountryName(), amazonSite.getCurrencyCode(), exchangeRate);

            // 从Amazon商品获取基础价格（假设为美元价格）
            BigDecimal basePrice = null;
            if (StringUtils.hasText(product.getPrice())) {
                String priceStr = product.getPrice().replaceAll("[^0-9.]", "");
                basePrice = new BigDecimal(priceStr);
            }

            // 从1688货源获取成本价格（人民币）
            BigDecimal supplyCostRMB = null;
            if (StringUtils.hasText(supply.getPrice())) {
                String supplyPriceStr = supply.getPrice().replaceAll("[^0-9.]", "");
                supplyCostRMB = new BigDecimal(supplyPriceStr);
            }

            BigDecimal standardPrice;

            if (supplyCostRMB != null && exchangeRate.compareTo(BigDecimal.ZERO) > 0) {
                // 使用1688成本价格计算
                // 成本价格（人民币）转换为目标货币：成本价 / 汇率 * 倍率
                BigDecimal costInTargetCurrency = supplyCostRMB.divide(exchangeRate, 4, RoundingMode.HALF_UP);

                // 定价策略：成本价 * 2.5 倍
                BigDecimal calculatedPrice = costInTargetCurrency.multiply(new BigDecimal("2.5"));

                // 如果有Amazon参考价格，不超过参考价格的1.2倍
                if (basePrice != null) {
                    // 将Amazon价格转换为目标货币（假设Amazon价格为美元）
                    BigDecimal amazonPriceInTargetCurrency;
                    if ("USD".equals(amazonSite.getCurrencyCode())) {
                        amazonPriceInTargetCurrency = basePrice;
                    } else {
                        // 美元转目标货币：美元价格 * 美元汇率 / 目标货币汇率
                        BigDecimal usdRate = amazonSiteConfig.getCurrentExchangeRate("US");
                        amazonPriceInTargetCurrency = basePrice.multiply(usdRate).divide(exchangeRate, 4, RoundingMode.HALF_UP);
                    }

                    BigDecimal maxPrice = amazonPriceInTargetCurrency.multiply(new BigDecimal("1.2"));
                    standardPrice = calculatedPrice.min(maxPrice);
                } else {
                    standardPrice = calculatedPrice;
                }

                log.info("价格计算详情: 成本价(RMB)={}, 目标货币成本价={}, 计算价格={}, 最终价格={}",
                        supplyCostRMB, costInTargetCurrency, calculatedPrice, standardPrice);

            } else if (basePrice != null) {
                // 如果没有1688价格，使用Amazon价格转换
                if ("USD".equals(amazonSite.getCurrencyCode())) {
                    standardPrice = basePrice;
                } else {
                    // 美元转目标货币
                    BigDecimal usdRate = amazonSiteConfig.getCurrentExchangeRate("US");
                    standardPrice = basePrice.multiply(usdRate).divide(exchangeRate, 4, RoundingMode.HALF_UP);
                }

                log.info("使用Amazon价格转换: 原价(USD)={}, 转换价格({})={}",
                        basePrice, amazonSite.getCurrencyCode(), standardPrice);
            } else {
                // 都没有，使用默认价格
                standardPrice = getDefaultPriceForCurrency(amazonSite.getCurrencyCode());
                log.warn("使用默认价格: {}={}", amazonSite.getCurrencyCode(), standardPrice);
            }

            // 设置价格（保留2位小数）
            goods.setStandardPrice(standardPrice.setScale(2, RoundingMode.HALF_UP));
            goods.setListPrice(standardPrice.multiply(new BigDecimal("1.2")).setScale(2, RoundingMode.HALF_UP));

        } catch (Exception e) {
            log.warn("根据汇率设置价格信息失败: {}", e.getMessage(), e);
            // 设置默认价格
            BigDecimal defaultPrice = getDefaultPriceForCurrency(amazonSite.getCurrencyCode());
            goods.setStandardPrice(defaultPrice);
            goods.setListPrice(defaultPrice.multiply(new BigDecimal("1.2")).setScale(2, RoundingMode.HALF_UP));
        }
    }

    /**
     * 获取不同货币的默认价格
     */
    private BigDecimal getDefaultPriceForCurrency(String currencyCode) {
        switch (currencyCode) {
            case "USD": return new BigDecimal("29.99");
            case "CAD": return new BigDecimal("39.99");
            case "MXN": return new BigDecimal("599.99");
            case "GBP": return new BigDecimal("24.99");
            case "EUR": return new BigDecimal("27.99");
            case "SEK": return new BigDecimal("299.99");
            case "PLN": return new BigDecimal("119.99");
            default: return new BigDecimal("29.99");
        }
    }

    /**
     * 设置图片信息
     */
    private void setImageInfo(Goods goods, Product product, Supply supply) {
        try {
            // 优先使用Amazon商品图片
            if (StringUtils.hasText(product.getImages())) {
                List<String> imageUrls = objectMapper.readValue(product.getImages(),
                        new TypeReference<List<String>>() {});

                if (!imageUrls.isEmpty()) {
                    goods.setMainImageUrl(imageUrls.get(0));

                    // 设置其他图片
                    for (int i = 1; i < Math.min(imageUrls.size(), 9); i++) {
                        setOtherImageUrl(goods, i, imageUrls.get(i));
                    }
                }
            }

            // 如果没有Amazon图片，使用1688图片
            if (!StringUtils.hasText(goods.getMainImageUrl()) && StringUtils.hasText(supply.getImageUrl())) {
                goods.setMainImageUrl(supply.getImageUrl());
            }
        } catch (Exception e) {
            log.warn("设置图片信息失败: {}", e.getMessage());
        }
    }

    /**
     * 设置其他图片URL
     */
    private void setOtherImageUrl(Goods goods, int index, String url) {
        switch (index) {
            case 1: goods.setOtherImageUrl1(url); break;
            case 2: goods.setOtherImageUrl2(url); break;
            case 3: goods.setOtherImageUrl3(url); break;
            case 4: goods.setOtherImageUrl4(url); break;
            case 5: goods.setOtherImageUrl5(url); break;
            case 6: goods.setOtherImageUrl6(url); break;
            case 7: goods.setOtherImageUrl7(url); break;
            case 8: goods.setOtherImageUrl8(url); break;
        }
    }

    /**
     * 设置变体信息
     */
    private void setVariantInfo(Goods goods, Product product,boolean isChildProduct, Store store) {
        // 1. 判断是否为变体商品的主商品
        if (product.getIsMainProduct() != null && product.getIsMainProduct() &&
            product.getIsParentVariant() != null && product.getIsParentVariant() &&
            !isChildProduct) {
            goods.setParentChild("parent");

            // 为主商品设置变体主题
            setVariationThemeForParent(goods, product);
        }
        // 2. 判断是否为变体商品的子商品
        else if (isChildProduct) {
            goods.setParentChild("child");
            goods.setParentSku(AsinCompressor.compress(product.getParentAsin()));
            goods.setRelationshipType("Variation");
            // 生成EAN编码并设置为外部产品ID
            String eanCode = generateEanCode(store);
            goods.setExternalProductId(eanCode);
            goods.setExternalProductIdType("EAN");

            // 解析变体属性并设置变体主题和属性
            parseAndSetVariantAttributes(goods, product);
        }else{
            // 生成EAN编码并设置为外部产品ID
            String eanCode = generateEanCode(store);
            goods.setExternalProductId(eanCode);
            goods.setExternalProductIdType("EAN");
        }
    }

    /**
     * 为主商品设置变体主题
     */
    private void setVariationThemeForParent(Goods goods, Product product) {
        if (!StringUtils.hasText(product.getVariantAttributes())) {
            return;
        }

        try {
            // 解析JSON格式的变体属性
            Map<String, Object> variantAttrs = objectMapper.readValue(
                product.getVariantAttributes(),
                new TypeReference<Map<String, Object>>() {}
            );

            boolean hasColorName = variantAttrs.containsKey("color_name");
            boolean hasSizeName = variantAttrs.containsKey("size_name");

            if (hasColorName && hasSizeName) {
                // 同时包含颜色和尺寸
                goods.setVariationTheme("ColorSize");
                log.info("主商品设置变体主题为ColorSize: productId={}", product.getId());

            } else if (hasSizeName) {
                // 只包含尺寸
                goods.setVariationTheme("Size");
                log.info("主商品设置变体主题为Size: productId={}", product.getId());

            } else if (hasColorName) {
                // 只包含颜色
                goods.setVariationTheme("Color");
                log.info("主商品设置变体主题为Color: productId={}", product.getId());
            }

        } catch (Exception e) {
            log.warn("主商品解析变体属性失败: productId={}, variantAttributes={}, error={}",
                    product.getId(), product.getVariantAttributes(), e.getMessage());
        }
    }

    /**
     * 解析并设置变体属性
     */
    private void parseAndSetVariantAttributes(Goods goods, Product product) {
        if (!StringUtils.hasText(product.getVariantAttributes())) {
            return;
        }

        try {
            // 解析JSON格式的变体属性
            Map<String, Object> variantAttrs = objectMapper.readValue(
                product.getVariantAttributes(),
                new TypeReference<Map<String, Object>>() {}
            );

            boolean hasColorName = variantAttrs.containsKey("color_name");
            boolean hasSizeName = variantAttrs.containsKey("size_name");

            if (hasColorName && hasSizeName) {
                // 同时包含颜色和尺寸
                goods.setVariationTheme("ColorSize");

                String sizeValue = String.valueOf(variantAttrs.get("size_name"));
                goods.setSizeName(sizeValue);
                goods.setSizeMap(sizeValue);

                String colorValue = String.valueOf(variantAttrs.get("color_name"));
                goods.setColorName(colorValue);
                goods.setColorMap(colorValue);

                log.info("设置变体主题为ColorSize: sizeName={}, colorName={}", sizeValue, colorValue);

            } else if (hasSizeName) {
                // 只包含尺寸
                goods.setVariationTheme("Size");

                String sizeValue = String.valueOf(variantAttrs.get("size_name"));
                goods.setSizeName(sizeValue);
                goods.setSizeMap(sizeValue);

                log.info("设置变体主题为Size: sizeName={}", sizeValue);

            } else if (hasColorName) {
                // 只包含颜色
                goods.setVariationTheme("Color");

                String colorValue = String.valueOf(variantAttrs.get("color_name"));
                goods.setColorName(colorValue);
                goods.setColorMap(colorValue);

                log.info("设置变体主题为Color: colorName={}", colorValue);
            }

        } catch (Exception e) {
            log.warn("解析变体属性失败: productId={}, variantAttributes={}, error={}",
                    product.getId(), product.getVariantAttributes(), e.getMessage());
        }
    }

    /**
     * 设置产品特性
     */
    private void setBulletPoints(Goods goods, Product product) {
        if (StringUtils.hasText(product.getFeature())) {
            String[] features = product.getFeature().split("\n");
            for (int i = 0; i < Math.min(features.length, 5); i++) {
                String feature = features[i].trim();
                if (StringUtils.hasText(feature)) {
                    setBulletPoint(goods, i + 1, feature);
                }
            }
        }
    }

    /**
     * 设置单个产品特性
     */
    private void setBulletPoint(Goods goods, int index, String feature) {
        switch (index) {
            case 1: goods.setBulletPoint1(feature); break;
            case 2: goods.setBulletPoint2(feature); break;
            case 3: goods.setBulletPoint3(feature); break;
            case 4: goods.setBulletPoint4(feature); break;
            case 5: goods.setBulletPoint5(feature); break;
        }
    }

    /**
     * 生成EAN编码
     */
    private String generateEanCode(Store store) {
        try {
            // 检查店铺是否配置了EAN信息
            if (!isEanConfigValid(store)) {
                log.warn("店铺EAN配置不完整，跳过EAN编码生成: storeId={}", store.getId());
                return null;
            }

            // 获取下一个产品代码
            String nextProductCode = storeService.getNextEanProductCode(store.getId());

            // 格式化产品代码为5位
            String formattedProductCode = EanCodeUtil.formatNumber(Integer.parseInt(nextProductCode), 5);

            // 生成EAN-13编码
            String eanCode = EanCodeUtil.generateEAN13(
                store.getEanCountryCode(),
                store.getEanManufacturerCode(),
                formattedProductCode
            );

            if (eanCode != null) {
                log.info("成功生成EAN编码: eanCode={}, storeId={}, productCode={}",
                        eanCode, store.getId(), formattedProductCode);
                return eanCode;
            } else {
                log.warn("EAN编码生成失败: storeId={}, countryCode={}, manufacturerCode={}, productCode={}",
                        store.getId(), store.getEanCountryCode(), store.getEanManufacturerCode(), formattedProductCode);
                return null;
            }

        } catch (Exception e) {
            log.error("生成EAN编码时发生异常: storeId={}, error={}", store.getId(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 验证店铺EAN配置是否有效
     */
    private boolean isEanConfigValid(Store store) {
        return StringUtils.hasText(store.getEanCountryCode()) &&
               StringUtils.hasText(store.getEanManufacturerCode()) &&
               StringUtils.hasText(store.getEanProductCodeCurrent());
    }

    /**
     * 转换为DTO对象
     */
    private GoodsDTO convertToDTO(Goods goods) {
        GoodsDTO dto = new GoodsDTO();
        BeanUtils.copyProperties(goods, dto);
        return dto;
    }
}
