package com.example.worker.service;

import com.example.worker.config.AppConfig;
import com.example.worker.websocket.NotificationWebSocketClient;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.util.concurrent.TimeUnit;

/**
 * WebSocket连接管理器
 */
@Service
@Slf4j
public class WebSocketConnectionManager {

    @Autowired
    private AppConfig appConfig;

    @Autowired
    private AuthService authService;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private AmazonService amazonService;

    private NotificationWebSocketClient webSocketClient;

    /**
     * 初始化WebSocket连接
     */
    @PostConstruct
    public void init() {
        connectWebSocket();
    }

    /**
     * 连接WebSocket
     */
    public void connectWebSocket() {
        try {
            log.info("正在连接WebSocket...");

            // 获取JWT令牌
            String token = authService.getToken();
            if (token == null) {
                log.error("无法获取JWT令牌，WebSocket连接失败");
                return;
            }

            // 创建WebSocket客户端
            webSocketClient = NotificationWebSocketClient.create(
                appConfig.getWebsocket().getUrl(),
                token,
                objectMapper,
                appConfig,
                amazonService,
                authService
            );

            // 连接WebSocket
            webSocketClient.connect();

            log.info("WebSocket连接请求已发送");
        } catch (Exception e) {
            log.error("连接WebSocket时发生错误", e);
        }
    }

    /**
     * 检查WebSocket连接状态
     * 每30秒执行一次
     */
    @Scheduled(fixedRate = 30, timeUnit = TimeUnit.SECONDS)
    public void checkConnection() {
        if (webSocketClient == null || !webSocketClient.isOpen()) {
            log.warn("WebSocket连接已关闭，尝试重新连接");
            connectWebSocket();
        }
    }

    /**
     * 关闭WebSocket连接
     */
    public void closeConnection() {
        if (webSocketClient != null && webSocketClient.isOpen()) {
            webSocketClient.close();
            log.info("WebSocket连接已关闭");
        }
    }
}
