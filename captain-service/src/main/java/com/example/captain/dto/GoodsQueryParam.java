package com.example.captain.dto;

import lombok.Data;

/**
 * 商品查询参数
 */
@Data
public class GoodsQueryParam {
    /**
     * 页码
     */
    private Integer page = 1;

    /**
     * 每页记录数
     */
    private Integer size = 10;

    /**
     * 事件ID
     */
    private Long eventId;

    /**
     * Amazon商品ASIN
     */
    private String amazonAsin;

    /**
     * 状态：0-待审核，1-已审核，2-已发布，3-已拒绝
     */
    private Integer status;

    /**
     * 卖家SKU
     */
    private String itemSku;
}
