package com.example.captain.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.captain.entity.Store;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * 店铺信息数据访问层
 */
@Mapper
public interface StoreMapper extends BaseMapper<Store> {

    /**
     * 根据店铺名称查询店铺（排除已删除的记录）
     *
     * @param storeName 店铺名称
     * @return 店铺信息
     */
    @Select("SELECT * FROM stores WHERE store_name = #{storeName} AND deleted = 0")
    Store findByStoreName(@Param("storeName") String storeName);

    /**
     * 根据用户名查询店铺（排除已删除的记录）
     *
     * @param username 用户名
     * @return 店铺信息
     */
    @Select("SELECT * FROM stores WHERE username = #{username} AND deleted = 0")
    Store findByUsername(@Param("username") String username);

    /**
     * 更新EAN产品代码当前值
     *
     * @param id 店铺ID
     * @param newCode 新的EAN产品代码
     * @return 更新行数
     */
    @Update("UPDATE stores SET ean_product_code_current = #{newCode}, updated_at = NOW() WHERE id = #{id} AND deleted = 0")
    int updateEanProductCodeCurrent(@Param("id") Long id, @Param("newCode") String newCode);

    /**
     * 软删除店铺
     *
     * @param id 店铺ID
     * @return 更新行数
     */
    @Update("UPDATE stores SET deleted = 1, updated_at = NOW() WHERE id = #{id} AND deleted = 0")
    int softDeleteById(@Param("id") Long id);

    /**
     * 更新店铺状态
     *
     * @param id 店铺ID
     * @param status 状态
     * @return 更新行数
     */
    @Update("UPDATE stores SET status = #{status}, updated_at = NOW() WHERE id = #{id} AND deleted = 0")
    int updateStatus(@Param("id") Long id, @Param("status") Integer status);
}
