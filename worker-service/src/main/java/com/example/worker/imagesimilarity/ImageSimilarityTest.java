package com.example.worker.imagesimilarity;

import org.opencv.core.*;
import org.opencv.imgcodecs.Imgcodecs;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;

/**
 * 图像相似度测试类
 */
public class ImageSimilarityTest {

    static {
        // 使用OpenCVManager初始化OpenCV库
        OpenCVManager.initialize();
    }

    public static void main(String[] args) {
        try {
            // 亚马逊商品图片路径
            String amazonImagePath = "images/gzj/gzj.jpg";

            // 1688商品图片目录
            String alibaba1688Directory = "images/gzj/1688";

            System.out.println("开始比较图片相似度...");

            // 确保图片目录存在
            ensureDirectoriesExist(amazonImagePath, alibaba1688Directory);

            // 展示商品分割效果
            System.out.println("\n=== 商品主体分割 ===");
            System.out.println("对白底商品图进行主体分割，去除背景干扰");

            // 创建分割服务，使用try-with-resources确保资源释放
            Mat amazonImage = null;
            Mat normalizedAmazon = null;

            try (SimpleProductSegmentation segmentation = new SimpleProductSegmentation()) {
                // 分割亚马逊商品图片
                System.out.println("\n【传统分割】效果展示:");
                amazonImage = segmentation.segmentProduct(amazonImagePath);

                // 保存分割后的图片
                String segmentedAmazonPath = "images/gzj/segmented/amazon_traditional.jpg";
                segmentation.saveSegmentedImage(amazonImage, segmentedAmazonPath);
                System.out.println("亚马逊商品图片传统分割完成，保存至: " + segmentedAmazonPath);

                // 标准化商品图像
                Size standardSize = new Size(224, 224);
                normalizedAmazon = segmentation.normalizeProductImage(amazonImage, standardSize);
                segmentation.saveSegmentedImage(normalizedAmazon, "images/gzj/segmented/amazon_normalized.jpg");
                System.out.println("亚马逊商品图片标准化完成");
            } finally {
                // 释放Mat资源
                OpenCVManager.releaseMats(amazonImage, normalizedAmazon);
            }

            // 使用传统特征综合比较
            System.out.println("\n=== 传统特征综合比较 ===");
            System.out.println("直方图比较权重: 50%, 模板匹配权重: 25%, 纹理特征权重: 25%");
            System.out.println("这种组合能有效捕捉商品的颜色、形状和纹理特征");

            // 创建相似度服务，使用try-with-resources确保资源释放
            try (CustomSegmentationSimilarityService similarityService = new CustomSegmentationSimilarityService()) {
                // 获取1688目录中的所有图片
                List<CustomSegmentationSimilarityService.SimilarityResult> results =
                    similarityService.findSimilarImagesInDirectory(amazonImagePath, alibaba1688Directory);

                // 打印结果
                System.out.println("\n【传统特征综合】比较结果:");
                printResults(results);
            } // 自动调用similarityService.close()

        } catch (Exception e) {
            System.err.println("执行过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 确保目录存在
     */
    private static void ensureDirectoriesExist(String amazonImagePath, String alibaba1688Directory) {
        // 确保输出目录存在
        new File("images/gzj/segmented").mkdirs();

        // 检查亚马逊图片是否存在
        if (!new File(amazonImagePath).exists()) {
            System.err.println("警告: 亚马逊商品图片不存在: " + amazonImagePath);
        }

        // 检查1688目录是否存在
        File alibaba1688Dir = new File(alibaba1688Directory);
        if (!alibaba1688Dir.exists() || !alibaba1688Dir.isDirectory()) {
            System.err.println("警告: 1688商品图片目录不存在: " + alibaba1688Directory);
        } else {
            // 检查目录中是否有图片
            File[] imageFiles = alibaba1688Dir.listFiles((dir, name) ->
                name.toLowerCase().endsWith(".jpg") ||
                name.toLowerCase().endsWith(".jpeg") ||
                name.toLowerCase().endsWith(".png") ||
                name.toLowerCase().endsWith(".webp"));

            if (imageFiles == null || imageFiles.length == 0) {
                System.err.println("警告: 1688商品图片目录中没有图片: " + alibaba1688Directory);
            } else {
                System.out.println("找到 " + imageFiles.length + " 张1688商品图片");
            }
        }
    }

    /**
     * 打印相似度结果
     */
    private static void printResults(List<CustomSegmentationSimilarityService.SimilarityResult> results) {
        if (results.isEmpty()) {
            System.out.println("没有找到相似图片");
            return;
        }

        System.out.println("找到 " + results.size() + " 张相似图片，按相似度降序排列:");
        System.out.println("--------------------------------------------------");
        System.out.printf("%-30s | %s\n", "图片", "相似度");
        System.out.println("--------------------------------------------------");

        for (int i = 0; i < results.size(); i++) {
            CustomSegmentationSimilarityService.SimilarityResult result = results.get(i);
            System.out.printf("%-30s | %.4f\n",
                new File(result.getImagePath()).getName(),
                result.getSimilarity());
        }

        System.out.println("--------------------------------------------------");
    }
}
