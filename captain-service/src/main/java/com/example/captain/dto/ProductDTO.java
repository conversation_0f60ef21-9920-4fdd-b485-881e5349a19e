package com.example.captain.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 产品数据传输对象
 */
@Data
public class ProductDTO {
    /**
     * 产品ID
     */
    private Long id;

    /**
     * 关联的事件ID
     */
    private Long eventId;

    /**
     * Amazon标准识别号
     */
    private String asin;

    /**
     * 父商品ASIN
     */
    private String parentAsin;

    /**
     * 是否为父变体商品
     */
    private Boolean isParentVariant;

    /**
     * 是否为主商品
     */
    private Boolean isMainProduct;

    /**
     * 商品链接
     */
    private String productLink;

    /**
     * 商品标题
     */
    private String title;

    /**
     * 商品价格
     */
    private String price;

    /**
     * 商品类型
     */
    private String productType;

    /**
     * 国家
     */
    private String country;

    /**
     * 商品短描述
     */
    private String feature;

    /**
     * 商品长描述
     */
    private String description;

    /**
     * 图片URL列表，JSON格式
     */
    private String images;

    /**
     * 变体属性，JSON格式
     */
    private String variantAttributes;

    /**
     * 商品SKU
     */
    private String productSku;

    /**
     * 原始JSON数据
     */
    private String rawData;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * Amazon推荐浏览节点ID
     */
    private String recommendedBrowseNodes;

    /**
     * 产品详细信息
     */
    private String prodDetails;

    /**
     * 产品概览信息
     */
    private String productOverview;
}
