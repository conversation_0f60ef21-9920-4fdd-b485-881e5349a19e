package com.example.worker.imagesimilarity;

import org.opencv.core.*;
import org.opencv.imgcodecs.Imgcodecs;
import org.opencv.imgproc.Imgproc;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 自定义分割方法相似度比较服务
 * 使用SimpleProductSegmentation进行图像相似度比较
 */
public class CustomSegmentationSimilarityService implements AutoCloseable {

    // 分割服务
    private SimpleProductSegmentation segmentationService;

    // 特征提取器
    private TextureFeatureExtractor textureExtractor;

    // 权重配置 - 传统特征综合比较
    private final double histogramWeight = 0.5;  // 直方图比较权重: 50%
    private final double templateWeight = 0.25;  // 模板匹配权重: 25%
    private final double lbpTextureWeight = 0.25;  // 纹理特征权重: 25%

    /**
     * 构造函数
     */
    public CustomSegmentationSimilarityService() {
        // 初始化分割服务
        segmentationService = new SimpleProductSegmentation();

        // 初始化特征提取器
        textureExtractor = new TextureFeatureExtractor();

        // 确保OpenCV已初始化
        OpenCVManager.initialize();
    }

    /**
     * 释放资源
     */
    @Override
    public void close() {
        try {
            // 关闭分割服务
            if (segmentationService != null) {
                segmentationService.close();
            }

            // 如果TextureFeatureExtractor实现了AutoCloseable，也关闭它
            if (textureExtractor instanceof AutoCloseable) {
                ((AutoCloseable) textureExtractor).close();
            }

            System.out.println("CustomSegmentationSimilarityService资源已释放");
        } catch (Exception e) {
            System.err.println("关闭CustomSegmentationSimilarityService时出错: " + e.getMessage());
        }
    }

    /**
     * 计算传统特征综合相似度 (直方图比较50% + 模板匹配25% + 纹理特征25%)
     * @param sourceImagePath 源图片路径
     * @param targetImagePath 目标图片路径
     * @return 传统特征综合相似度分数 (0-1.0)
     */
    public double calculateTraditionalSimilarityOnly(String sourceImagePath, String targetImagePath) {
        Mat sourceImage = null;
        Mat targetImage = null;
        Mat normalizedSource = null;
        Mat normalizedTarget = null;

        try {
            // 分割商品主体
            sourceImage = segmentationService.segmentProduct(sourceImagePath);
            targetImage = segmentationService.segmentProduct(targetImagePath);

            // 标准化商品图像
            Size standardSize = new Size(224, 224);
            normalizedSource = segmentationService.normalizeProductImage(sourceImage, standardSize);
            normalizedTarget = segmentationService.normalizeProductImage(targetImage, standardSize);

            // 计算传统特征综合相似度
            return calculateTraditionalSimilarity(normalizedSource, normalizedTarget);
        } finally {
            // 释放Mat资源
            OpenCVManager.releaseMats(sourceImage, targetImage, normalizedSource, normalizedTarget);
        }
    }

    /**
     * 计算传统特征综合相似度 (直方图比较50% + 模板匹配25% + 纹理特征25%)
     */
    private double calculateTraditionalSimilarity(Mat sourceImage, Mat targetImage) {
        try {
            // 计算直方图相似度 (HSV颜色空间)
            double histogramSimilarity = calculateColorSimilarity(sourceImage, targetImage);

            // 计算模板匹配相似度
            double templateSimilarity = calculateTemplateMatchingSimilarity(sourceImage, targetImage);

            // 计算纹理特征相似度 (LBP)
            double textureSimilarity = calculateTextureSimilarity(sourceImage, targetImage);

            // 加权综合相似度
            double traditionalSimilarity =
                histogramWeight * histogramSimilarity +
                templateWeight * templateSimilarity +
                lbpTextureWeight * textureSimilarity;

            // 限制在0-1范围内
            return Math.max(0.0, Math.min(1.0, traditionalSimilarity));
        } catch (Exception e) {
            System.err.println("计算传统特征综合相似度时出错: " + e.getMessage());
            return 0.0;
        }
    }

    /**
     * 计算颜色相似度 (HSV直方图)
     */
    private double calculateColorSimilarity(Mat sourceImage, Mat targetImage) {
        // 创建所有需要释放的Mat对象
        Mat sourceHsv = new Mat();
        Mat targetHsv = new Mat();
        Mat sourceHist = new Mat();
        Mat targetHist = new Mat();
        Mat mask = new Mat();
        MatOfInt histSize = new MatOfInt(50, 60);
        MatOfFloat ranges = new MatOfFloat(0, 180, 0, 256);
        MatOfInt channels = new MatOfInt(0, 1);

        try {
            // 转换为HSV颜色空间
            Imgproc.cvtColor(sourceImage, sourceHsv, Imgproc.COLOR_BGR2HSV);
            Imgproc.cvtColor(targetImage, targetHsv, Imgproc.COLOR_BGR2HSV);

            // 计算直方图
            Imgproc.calcHist(
                java.util.Arrays.asList(sourceHsv),
                channels,
                mask,
                sourceHist,
                histSize,
                ranges
            );

            Imgproc.calcHist(
                java.util.Arrays.asList(targetHsv),
                channels,
                mask,
                targetHist,
                histSize,
                ranges
            );

            // 归一化直方图
            Core.normalize(sourceHist, sourceHist, 0, 1, Core.NORM_MINMAX);
            Core.normalize(targetHist, targetHist, 0, 1, Core.NORM_MINMAX);

            // 比较直方图 (使用相关性方法)
            double similarity = Imgproc.compareHist(sourceHist, targetHist, Imgproc.CV_COMP_CORREL);

            // 确保相似度在0-1范围内
            return Math.max(0.0, Math.min(1.0, similarity));
        } catch (Exception e) {
            System.err.println("计算颜色相似度时出错: " + e.getMessage());
            return 0.0;
        } finally {
            // 释放所有Mat资源
            OpenCVManager.releaseMats(sourceHsv, targetHsv, sourceHist, targetHist, mask);
            histSize.release();
            ranges.release();
            channels.release();
        }
    }

    /**
     * 计算纹理相似度 (LBP特征)
     */
    private double calculateTextureSimilarity(Mat sourceImage, Mat targetImage) {
        // 创建所有需要释放的Mat对象
        Mat sourceGray = new Mat();
        Mat targetGray = new Mat();
        Mat sourceLBP = null;
        Mat targetLBP = null;
        Mat sourceLBPHist = null;
        Mat targetLBPHist = null;

        try {
            // 转换为灰度图
            Imgproc.cvtColor(sourceImage, sourceGray, Imgproc.COLOR_BGR2GRAY);
            Imgproc.cvtColor(targetImage, targetGray, Imgproc.COLOR_BGR2GRAY);

            // 计算LBP特征
            sourceLBP = textureExtractor.computeLBP(sourceGray);
            targetLBP = textureExtractor.computeLBP(targetGray);

            // 计算LBP直方图
            sourceLBPHist = textureExtractor.computeLBPHistogram(sourceLBP);
            targetLBPHist = textureExtractor.computeLBPHistogram(targetLBP);

            // 比较直方图 (使用相关性方法)
            double similarity = Imgproc.compareHist(sourceLBPHist, targetLBPHist, Imgproc.CV_COMP_CORREL);

            return similarity;
        } catch (Exception e) {
            System.err.println("计算纹理相似度时出错: " + e.getMessage());
            return 0.0;
        } finally {
            // 释放所有Mat资源
            OpenCVManager.releaseMats(sourceGray, targetGray, sourceLBP, targetLBP, sourceLBPHist, targetLBPHist);
        }
    }

    /**
     * 计算模板匹配相似度
     */
    private double calculateTemplateMatchingSimilarity(Mat sourceImage, Mat targetImage) {
        // 创建所有需要释放的Mat对象
        Mat sourceGray = new Mat();
        Mat targetGray = new Mat();
        Mat resizedTarget = new Mat();
        Mat result = new Mat();

        try {
            // 转换为灰度图
            Imgproc.cvtColor(sourceImage, sourceGray, Imgproc.COLOR_BGR2GRAY);
            Imgproc.cvtColor(targetImage, targetGray, Imgproc.COLOR_BGR2GRAY);

            // 调整目标图像大小以匹配源图像
            Imgproc.resize(targetGray, resizedTarget, sourceGray.size());

            // 使用归一化相关系数匹配方法
            Imgproc.matchTemplate(resizedTarget, sourceGray, result, Imgproc.TM_CCOEFF_NORMED);

            // 找到最佳匹配位置
            Core.MinMaxLocResult mmr = Core.minMaxLoc(result);

            // 返回最大相关系数作为相似度 (范围0-1)
            return mmr.maxVal;

        } catch (Exception e) {
            System.err.println("计算模板匹配相似度时出错: " + e.getMessage());
            return 0.0;
        } finally {
            // 释放所有Mat资源
            OpenCVManager.releaseMats(sourceGray, targetGray, resizedTarget, result);
        }
    }

    /**
     * 在目录中查找与源图片最相似的图片
     * @param sourceImagePath 源图片路径
     * @param targetDirectory 目标图片目录
     * @return 按相似度排序的结果列表
     */
    public List<SimilarityResult> findSimilarImagesInDirectory(String sourceImagePath, String targetDirectory) throws Exception {
        // 确保OpenCV已初始化
        OpenCVManager.initialize();

        // 获取目录中的所有图片
        List<String> imagePaths = Files.walk(Paths.get(targetDirectory))
            .filter(Files::isRegularFile)
            .map(Path::toString)
            .filter(path -> path.toLowerCase().endsWith(".jpg") ||
                           path.toLowerCase().endsWith(".jpeg") ||
                           path.toLowerCase().endsWith(".png") ||
                           path.toLowerCase().endsWith(".webp"))
            .collect(Collectors.toList());

        List<SimilarityResult> results = new ArrayList<>();

        for (String targetPath : imagePaths) {
            try {
                double similarity = calculateTraditionalSimilarityOnly(sourceImagePath, targetPath);
                results.add(new SimilarityResult(targetPath, similarity));
            } catch (Exception e) {
                System.err.println("处理图片时出错: " + targetPath + " - " + e.getMessage());
            }
        }

        // 按相似度降序排序
        results.sort(Comparator.comparing(SimilarityResult::getSimilarity).reversed());

        return results;
    }

    /**
     * 相似度结果类
     */
    public static class SimilarityResult {
        private final String imagePath;
        private final double similarity;

        public SimilarityResult(String imagePath, double similarity) {
            this.imagePath = imagePath;
            this.similarity = similarity;
        }

        public String getImagePath() {
            return imagePath;
        }

        public double getSimilarity() {
            return similarity;
        }

        @Override
        public String toString() {
            return String.format("图片: %s, 相似度: %.4f",
                new File(imagePath).getName(), similarity);
        }
    }
}
