package com.example.captain.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * Amazon产品Excel导出数据传输对象
 * 包含所有需要导出到Excel模板的字段
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AmazonProductExcelData {

    // ========== 基本产品信息 ==========
    
    /**
     * 产品类型
     */
    private String feedProductType;
    
    /**
     * 卖家SKU
     */
    private String itemSku;
    
    /**
     * 品牌名称
     */
    private String brandName;
    
    /**
     * 产品ID (通常是ASIN)
     */
    private String externalProductId;
    
    /**
     * 产品ID类型 (通常是ASIN)
     */
    private String externalProductIdType;
    
    /**
     * 商品名称
     */
    private String itemName;
    
    /**
     * 制造商
     */
    private String manufacturer;
    
    /**
     * 推荐浏览节点
     */
    private String recommendedBrowseNodes;
    
    /**
     * 最大订购数量
     */
    private Integer maxOrderQuantity;
    
    /**
     * 原产国
     */
    private String countryOfOrigin;

    // ========== 价格和库存 ==========
    
    /**
     * 标准价格
     */
    private BigDecimal standardPrice;
    
    /**
     * 库存数量
     */
    private Integer quantity;

    // ========== 图片信息 ==========
    
    /**
     * 主图片URL
     */
    private String mainImageUrl;
    
    /**
     * 其他图片URL列表 (最多8张)
     */
    private List<String> otherImageUrls;
    
    /**
     * 样本图片URL
     */
    private String swatchImageUrl;

    // ========== 变体信息 ==========
    
    /**
     * 父子关系 (parent/child)
     */
    private String parentChild;
    
    /**
     * 父SKU
     */
    private String parentSku;
    
    /**
     * 关系类型
     */
    private String relationshipType;
    
    /**
     * 变体主题
     */
    private String variationTheme;

    // ========== 基本描述信息 ==========
    
    /**
     * 更新删除标志
     */
    private String updateDelete;
    
    /**
     * 产品描述
     */
    private String productDescription;
    
    /**
     * 制造商零件号
     */
    private String partNumber;

    // ========== 产品特性 ==========
    
    /**
     * 关键产品特性列表 (最多5个)
     */
    private List<String> bulletPoints;
    
    /**
     * 搜索关键词
     */
    private String genericKeywords;

    // ========== 产品属性 ==========
    
    /**
     * 金属类型
     */
    private String metalType;
    
    /**
     * 部门名称
     */
    private String departmentName;
    
    /**
     * 宝石类型
     */
    private String gemType;
    
    /**
     * 特殊功能
     */
    private String specialFeatures;
    
    /**
     * 颜色名称
     */
    private String colorName;
    
    /**
     * 颜色映射
     */
    private String colorMap;
    
    /**
     * 尺寸名称
     */
    private String sizeName;

    // ========== 尺寸和重量 ==========
    
    /**
     * 网站运输重量
     */
    private BigDecimal websiteShippingWeight;
    
    /**
     * 网站运输重量单位
     */
    private String websiteShippingWeightUnitOfMeasure;
    
    /**
     * 尺寸映射
     */
    private String sizeMap;
    
    /**
     * 单位数量
     */
    private Integer unitCount;
    
    /**
     * 单位数量类型
     */
    private String unitCountType;
    
    /**
     * 深度（前后）
     */
    private BigDecimal depthFrontToBack;
    
    /**
     * 深度单位
     */
    private String depthFrontToBackUnitOfMeasure;
    
    /**
     * 宽度（左右）
     */
    private BigDecimal depthWidthSideToSide;
    
    /**
     * 宽度单位
     */
    private String depthWidthSideToSideUnitOfMeasure;
    
    /**
     * 高度（底部到顶部）
     */
    private BigDecimal depthHeightFloorToTop;
    
    /**
     * 高度单位
     */
    private String depthHeightFloorToTopUnitOfMeasure;

    // ========== 履行和价格 ==========
    
    /**
     * 履行中心ID
     */
    private String fulfillmentCenterId;
    
    /**
     * 货币
     */
    private String currency;
    
    /**
     * 标价
     */
    private BigDecimal listPrice;
    
    /**
     * 含税标价
     */
    private BigDecimal listPriceWithTax;
    
    /**
     * 商品状态
     */
    private String conditionType;

    // ========== 合规性信息 ==========
    
    /**
     * 保修描述
     */
    private String warrantyDescription;
    
    /**
     * 是否包含电池
     */
    private String areBatteriesIncluded;
    
    /**
     * 是否需要电池
     */
    private String batteriesRequired;
    
    /**
     * 危险品法规声明
     */
    private String supplierDeclaredDgHzRegulation1;

    // ========== 辅助方法 ==========
    
    /**
     * 获取指定索引的其他图片URL
     * @param index 索引 (0-7)
     * @return 图片URL，如果不存在返回null
     */
    public String getOtherImageUrl(int index) {
        if (otherImageUrls == null || index < 0 || index >= otherImageUrls.size()) {
            return null;
        }
        return otherImageUrls.get(index);
    }
    
    /**
     * 获取指定索引的关键产品特性
     * @param index 索引 (0-4)
     * @return 特性描述，如果不存在返回null
     */
    public String getBulletPoint(int index) {
        if (bulletPoints == null || index < 0 || index >= bulletPoints.size()) {
            return null;
        }
        return bulletPoints.get(index);
    }
}
