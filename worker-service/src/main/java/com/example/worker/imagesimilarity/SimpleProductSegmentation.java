package com.example.worker.imagesimilarity;

import org.opencv.core.*;
import org.opencv.imgcodecs.Imgcodecs;
import org.opencv.imgproc.Imgproc;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * 简单商品分割类 - 使用传统方法从白底商品图中提取商品主体
 */
public class SimpleProductSegmentation implements AutoCloseable {

    /**
     * 构造函数
     */
    public SimpleProductSegmentation() {
        // 确保OpenCV已初始化
        OpenCVManager.initialize();
    }

    /**
     * 释放资源
     */
    @Override
    public void close() {
        // 目前没有需要释放的资源
        // 如果将来添加了需要释放的资源，可以在这里处理
    }

    /**
     * 从图像中分割商品主体
     * @param imagePath 图像路径
     * @return 分割后的商品主体图像
     */
    public Mat segmentProduct(String imagePath) {
        // 读取图像
        Mat image = Imgcodecs.imread(imagePath);
        if (image.empty()) {
            throw new RuntimeException("无法读取图片: " + imagePath);
        }

        // 分割商品主体
        return segmentProduct(image);
    }

    /**
     * 从图像中分割商品主体
     * @param image 输入图像
     * @return 分割后的商品主体图像
     */
    public Mat segmentProduct(Mat image) {
        // 创建图像副本
        Mat result = image.clone();

        // 对于白底图片，使用阈值分割
        Mat mask = createMaskForWhiteBackground(image);

        // 应用掩码提取商品主体
        Mat segmented = new Mat();
        image.copyTo(segmented, mask);

        // 裁剪到商品主体区域
        Rect boundingRect = getBoundingRect(mask);
        if (boundingRect.width > 0 && boundingRect.height > 0) {
            segmented = new Mat(segmented, boundingRect);
        }

        return segmented;
    }

    /**
     * 为白底图片创建掩码
     * @param image 输入图像
     * @return 掩码图像
     */
    private Mat createMaskForWhiteBackground(Mat image) {
        // 转换为灰度图
        Mat gray = new Mat();
        Imgproc.cvtColor(image, gray, Imgproc.COLOR_BGR2GRAY);

        // 应用高斯模糊减少噪声
        Mat blurred = new Mat();
        Imgproc.GaussianBlur(gray, blurred, new Size(5, 5), 0);

        // 使用Otsu阈值法分割前景和背景
        Mat thresh = new Mat();
        Imgproc.threshold(blurred, thresh, 0, 255, Imgproc.THRESH_BINARY_INV + Imgproc.THRESH_OTSU);

        // 形态学操作改善分割结果
        Mat kernel = Imgproc.getStructuringElement(Imgproc.MORPH_RECT, new Size(3, 3));
        Mat opening = new Mat();
        Imgproc.morphologyEx(thresh, opening, Imgproc.MORPH_OPEN, kernel, new Point(-1, -1), 2);

        // 寻找最大轮廓（假设是商品主体）
        List<MatOfPoint> contours = new ArrayList<>();
        Mat hierarchy = new Mat();
        Imgproc.findContours(opening, contours, hierarchy, Imgproc.RETR_EXTERNAL, Imgproc.CHAIN_APPROX_SIMPLE);

        // 如果没有找到轮廓，返回原图的掩码
        if (contours.isEmpty()) {
            return Mat.ones(image.size(), CvType.CV_8UC1);
        }

        // 找到最大轮廓
        double maxArea = 0;
        MatOfPoint largestContour = null;
        for (MatOfPoint contour : contours) {
            double area = Imgproc.contourArea(contour);
            if (area > maxArea) {
                maxArea = area;
                largestContour = contour;
            }
        }

        // 创建掩码
        Mat mask = Mat.zeros(gray.size(), CvType.CV_8UC1);
        if (largestContour != null) {
            Imgproc.drawContours(mask, List.of(largestContour), 0, new Scalar(255), -1);
        }

        // 应用形态学闭运算填充内部空洞
        Mat closingKernel = Imgproc.getStructuringElement(Imgproc.MORPH_ELLIPSE, new Size(5, 5));
        Imgproc.morphologyEx(mask, mask, Imgproc.MORPH_CLOSE, closingKernel);

        return mask;
    }

    /**
     * 获取掩码的边界矩形
     * @param mask 掩码图像
     * @return 边界矩形
     */
    private Rect getBoundingRect(Mat mask) {
        // 寻找非零像素的边界
        Mat nonZero = new Mat();
        Core.findNonZero(mask, nonZero);

        if (nonZero.empty()) {
            return new Rect();
        }

        return Imgproc.boundingRect(nonZero);
    }

    /**
     * 标准化商品图像尺寸
     * @param image 输入图像
     * @param targetSize 目标尺寸
     * @return 标准化后的图像
     */
    public Mat normalizeProductImage(Mat image, Size targetSize) {
        // 计算宽高比
        double aspectRatio = (double) image.width() / image.height();

        // 计算新尺寸，保持宽高比
        Size newSize;
        if (aspectRatio > 1) {
            // 宽大于高
            newSize = new Size(targetSize.width, targetSize.width / aspectRatio);
        } else {
            // 高大于宽
            newSize = new Size(targetSize.height * aspectRatio, targetSize.height);
        }

        // 调整图像大小
        Mat resized = new Mat();
        Imgproc.resize(image, resized, newSize);

        // 创建目标尺寸的画布（白色背景）
        Mat canvas = new Mat(targetSize, image.type(), new Scalar(255, 255, 255));

        // 计算居中位置
        int x = (int) ((targetSize.width - newSize.width) / 2);
        int y = (int) ((targetSize.height - newSize.height) / 2);

        // 将调整大小后的图像复制到画布中心
        Mat roi = new Mat(canvas, new Rect(x, y, (int) newSize.width, (int) newSize.height));
        resized.copyTo(roi);

        return canvas;
    }

    /**
     * 保存分割后的图像
     * @param image 分割后的图像
     * @param outputPath 输出路径
     */
    public void saveSegmentedImage(Mat image, String outputPath) {
        // 确保输出目录存在
        File outputFile = new File(outputPath);
        outputFile.getParentFile().mkdirs();

        // 保存图像
        Imgcodecs.imwrite(outputPath, image);
    }
}
