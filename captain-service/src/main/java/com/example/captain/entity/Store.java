package com.example.captain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 店铺信息实体类
 * 用于存储Amazon店铺的基本信息和配置
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "stores", autoResultMap = true)
public class Store {

    /**
     * 店铺ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 店铺名称
     */
    @TableField("store_name")
    private String storeName;

    /**
     * 店铺链接
     */
    @TableField("store_url")
    private String storeUrl;

    /**
     * 用户名
     */
    @TableField("username")
    private String username;

    /**
     * 密码（加密存储）
     */
    @TableField("password")
    private String password;

    /**
     * 北美站品牌
     */
    @TableField("north_america_brand")
    private String northAmericaBrand;

    /**
     * 欧洲站品牌
     */
    @TableField("europe_brand")
    private String europeBrand;

    /**
     * EAN国家代码
     */
    @TableField("ean_country_code")
    private String eanCountryCode;

    /**
     * EAN厂商代码
     */
    @TableField("ean_manufacturer_code")
    private String eanManufacturerCode;

    /**
     * EAN产品代码当前值
     */
    @TableField("ean_product_code_current")
    private String eanProductCodeCurrent;

    /**
     * 状态：1-正常，0-禁用
     */
    @TableField("status")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField("updated_at")
    private LocalDateTime updatedAt;

    /**
     * 是否删除：1-已删除，0-未删除
     */
    @TableField("deleted")
    private Integer deleted;
}
