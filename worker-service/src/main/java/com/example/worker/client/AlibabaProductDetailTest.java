package com.example.worker.client;

import java.util.Map;

/**
 * AlibabaClient 商品详情页测试类
 * 用于测试阿里巴巴客户端的打开商品详情页功能
 */
public class AlibabaProductDetailTest {

    /**
     * 主方法，用于测试 AlibabaClient 的打开商品详情页功能
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        System.out.println("开始测试 AlibabaClient 打开商品详情页功能...");

        // 默认商品URL
        String productUrl = "https://detail.1688.com/offer/654756988475.html?fromkv=cbuPcPlugin:imageSearchDrawer";
        
        // 处理命令行参数
        if (args.length > 0) {
            // 第一个参数是商品URL
            productUrl = args[0];
        }
        
        System.out.println("使用商品URL: " + productUrl);

        // 创建 AlibabaClient 实例
        try (AlibabaClient client = new AlibabaClient()) {
            // 调用打开商品详情页方法
            Map<String, String> result = client.openProductDetailPage(productUrl);
            
            // 打印结果
            System.out.println("\n商品详情信息:");
            if (result.containsKey("error")) {
                System.err.println("获取商品详情出错: " + result.get("error"));
            } else {
                System.out.println("----------------------------------------");
                System.out.println("商品属性信息:");
                System.out.println(result.get("productAttributes"));
                System.out.println("----------------------------------------");
                System.out.println("包装信息:");
                System.out.println(result.get("packagingInfo"));
                System.out.println("----------------------------------------");
            }

            System.out.println("\n测试完成");
        } catch (Exception e) {
            System.err.println("测试过程中出错: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
