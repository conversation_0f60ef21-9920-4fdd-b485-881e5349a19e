package com.example.captain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 货币信息模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CurrencyInfo {
    
    /**
     * 货币代码（ISO 4217）
     */
    private String currencyCode;
    
    /**
     * 货币名称
     */
    private String currencyName;
    
    /**
     * 货币符号
     */
    private String currencySymbol;
    
    /**
     * 对人民币汇率
     */
    private BigDecimal exchangeRate;
    
    /**
     * 使用该货币的国家数量
     */
    private Integer countryCount;
    
    /**
     * 使用该货币的国家列表
     */
    private List<String> countries;
    
    /**
     * 小数位数
     */
    private Integer decimalPlaces;
    
    /**
     * 是否为主要货币
     */
    private Boolean isMajor;
}
