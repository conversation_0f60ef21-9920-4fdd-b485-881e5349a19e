package com.example.captain.util;

import com.example.captain.model.ProductDimensionsAndWeight;
import com.example.captain.service.MockOpenAIProductAnalysisService;

/**
 * 模拟OpenAI测试运行器
 * 使用正则表达式模拟OpenAI Function Call功能
 * 不依赖网络连接，可以离线测试
 */
public class MockOpenAITestRunner {

    public static void main(String[] args) {
        System.out.println("🤖 模拟OpenAI商品信息提取测试");
        System.out.println("============================================================");
        System.out.println("注意: 这是一个模拟版本，使用正则表达式而非真实的OpenAI API");
        System.out.println();
        
        // 初始化模拟服务
        MockOpenAIProductAnalysisService service = new MockOpenAIProductAnalysisService();
        
        // 测试案例1: 蓝牙耳机
        testBluetoothHeadphones(service);
        
        // 测试案例2: 笔记本支架
        testLaptopStand(service);
        
        // 测试案例3: 无线充电器
        testWirelessCharger(service);
        
        // 测试案例4: 游戏鼠标
        testGamingMouse(service);
        
        // 测试案例5: 不完整数据
        testIncompleteData(service);
        
        System.out.println("✅ 所有模拟测试完成！");
        System.out.println("\n💡 提示: 如果模拟测试效果良好，说明Function Call逻辑正确，");
        System.out.println("   问题主要在于网络连接。请检查网络配置或使用代理。");
    }
    
    private static void testBluetoothHeadphones(MockOpenAIProductAnalysisService service) {
        System.out.println("🎧 测试案例1: 蓝牙耳机");
        System.out.println("----------------------------------------");
        
        String title = "Wireless Bluetooth Headphones - Over Ear Headphones with Microphone, 40H Playtime, Deep Bass, Weight: 0.5kg";
        String feature = "【40H Playtime】: 40 hours wireless mode. Weight: 500g. Dimensions: 20cm x 15cm x 8cm. 【Premium Sound】: Advanced 40mm drivers.";
        String description = "Premium wireless Bluetooth headphones. Package dimensions: 20 x 15 x 8 centimeters. Shipping weight: 500 grams. Comfortable over-ear design.";
        
        runTest(service, title, feature, description);
    }
    
    private static void testLaptopStand(MockOpenAIProductAnalysisService service) {
        System.out.println("\n💻 测试案例2: 笔记本支架");
        System.out.println("----------------------------------------");
        
        String title = "Adjustable Laptop Stand for Desk, Aluminum Laptop Riser, Ergonomic Computer Stand";
        String feature = "【Ergonomic Design】: 6 adjustable levels. Weight: 1.2kg. Size: 28cm(L) x 22cm(W) x 6cm(H). 【Sturdy】: Premium aluminum alloy.";
        String description = "High-quality aluminum laptop stand. Product dimensions: 28 x 22 x 6 cm. Net weight: 1200g. Supports 10-17.3 inch laptops.";
        
        runTest(service, title, feature, description);
    }
    
    private static void testWirelessCharger(MockOpenAIProductAnalysisService service) {
        System.out.println("\n🔋 测试案例3: 无线充电器");
        System.out.println("----------------------------------------");
        
        String title = "Wireless Charger, 15W Fast Wireless Charging Pad Compatible with iPhone, Samsung";
        String feature = "【15W Fast Charging】: Up to 15W fast charging. 【Compact】: 8mm thick. Dimensions: 10cm diameter x 0.8cm height. Weight: 150g.";
        String description = "Compact wireless charging pad. Specifications: Diameter 10cm, Height 0.8cm, Weight 150g. LED indicator included.";
        
        runTest(service, title, feature, description);
    }
    
    private static void testGamingMouse(MockOpenAIProductAnalysisService service) {
        System.out.println("\n🖱️ 测试案例4: 游戏鼠标");
        System.out.println("----------------------------------------");
        
        String title = "Gaming Mouse, RGB Wired Gaming Mouse with 12000 DPI, 7 Programmable Buttons";
        String feature = "【High Precision】: Up to 12000 DPI. 【Ergonomic Design】: Comfortable grip. Product size: 12.5cm x 6.8cm x 4.2cm. Net weight: 95g.";
        String description = "Professional gaming mouse with high-precision sensor. Dimensions: 125mm x 68mm x 42mm. Weight: 95 grams. Features 7 programmable buttons.";
        
        runTest(service, title, feature, description);
    }
    
    private static void testIncompleteData(MockOpenAIProductAnalysisService service) {
        System.out.println("\n📱 测试案例5: 不完整数据");
        System.out.println("----------------------------------------");
        
        String title = "Smartphone Case, Clear Protective Case with Shock Absorption";
        String feature = "【Crystal Clear】: Transparent design shows off your phone's original beauty. 【Drop Protection】: Military-grade protection.";
        String description = "High-quality transparent phone case with excellent protection. Made from premium TPU material. Easy installation and precise cutouts.";
        
        runTest(service, title, feature, description);
    }
    
    private static void runTest(MockOpenAIProductAnalysisService service, String title, String feature, String description) {
        // 打印输入
        System.out.println("📝 输入:");
        System.out.println("Title: " + truncate(title, 80));
        System.out.println("Feature: " + truncate(feature, 80));
        System.out.println("Description: " + truncate(description, 80));
        
        System.out.println("\n🔄 使用模拟服务提取信息...");
        
        try {
            // 执行提取
            ProductDimensionsAndWeight result = service.extractDimensionsAndWeight(title, feature, description);
            
            // 打印结果
            System.out.println("\n📊 提取结果:");
            if (result != null && result.hasValidData()) {
                printResult(result);
                System.out.println("✅ 提取成功");
            } else {
                System.out.println("❌ 未提取到有效数据");
            }
        } catch (Exception e) {
            System.out.println("❌ 提取失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void printResult(ProductDimensionsAndWeight result) {
        // 重量
        if (result.getShippingWeight() != null) {
            System.out.println("🏋️ 重量: " + result.getShippingWeight() + " " + 
                (result.getShippingWeightUnitOfMeasure() != null ? result.getShippingWeightUnitOfMeasure() : ""));
        }
        
        // 尺寸
        boolean hasDimensions = false;
        StringBuilder dimensions = new StringBuilder("📏 尺寸: ");
        
        if (result.getItemDepthFrontToBack() != null) {
            dimensions.append("长").append(result.getItemDepthFrontToBack());
            if (result.getItemDepthUnit() != null) dimensions.append(result.getItemDepthUnit());
            hasDimensions = true;
        }
        
        if (result.getItemWidthSideToSide() != null) {
            if (hasDimensions) dimensions.append(" × ");
            dimensions.append("宽").append(result.getItemWidthSideToSide());
            if (result.getItemWidthUnit() != null) dimensions.append(result.getItemWidthUnit());
            hasDimensions = true;
        }
        
        if (result.getItemHeightFloorToTop() != null) {
            if (hasDimensions) dimensions.append(" × ");
            dimensions.append("高").append(result.getItemHeightFloorToTop());
            if (result.getItemHeightUnitOfMeasure() != null) dimensions.append(result.getItemHeightUnitOfMeasure());
            hasDimensions = true;
        }
        
        if (hasDimensions) {
            System.out.println(dimensions.toString());
        }
    }
    
    private static String truncate(String text, int maxLength) {
        if (text == null) return "null";
        if (text.length() <= maxLength) return text;
        return text.substring(0, maxLength - 3) + "...";
    }
}
