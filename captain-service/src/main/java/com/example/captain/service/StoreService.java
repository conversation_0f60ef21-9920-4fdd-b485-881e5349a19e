package com.example.captain.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.example.captain.dto.*;
import com.example.captain.entity.Store;

/**
 * 店铺信息服务接口
 */
public interface StoreService {

    /**
     * 分页查询店铺列表
     *
     * @param param 查询参数
     * @return 分页结果
     */
    IPage<StoreDTO> getStorePage(StoreQueryParam param);

    /**
     * 根据ID获取店铺详情
     *
     * @param id 店铺ID
     * @return 店铺详情
     */
    StoreDTO getStoreDTOById(Long id);

    /**
     * 根据店铺名称获取店铺详情
     *
     * @param storeName 店铺名称
     * @return 店铺详情
     */
    StoreDTO getStoreByName(String storeName);

    /**
     * 创建店铺
     *
     * @param request 创建请求
     * @return 创建的店铺信息
     */
    StoreDTO createStore(StoreCreateRequest request);

    /**
     * 更新店铺信息
     *
     * @param id 店铺ID
     * @param request 更新请求
     * @return 更新后的店铺信息
     */
    StoreDTO updateStore(Long id, StoreUpdateRequest request);

    /**
     * 更新店铺状态
     *
     * @param id 店铺ID
     * @param request 状态更新请求
     */
    void updateStoreStatus(Long id, StoreStatusUpdateRequest request);

    /**
     * 删除店铺（软删除）
     *
     * @param id 店铺ID
     */
    void deleteStore(Long id);

    /**
     * 获取下一个EAN产品代码
     *
     * @param id 店铺ID
     * @return 下一个EAN产品代码
     */
    String getNextEanProductCode(Long id);

    /**
     * 根据ID获取店铺实体（内部使用）
     *
     * @param id 店铺ID
     * @return 店铺实体
     */
    Store getStoreEntityById(Long id);
}
