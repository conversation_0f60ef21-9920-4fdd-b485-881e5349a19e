-- 为products表添加is_main_product字段
ALTER TABLE products 
ADD COLUMN is_main_product BOOLEAN DEFAULT FALSE COMMENT '是否为主商品：true-主商品，false-子商品';

-- 为新字段创建索引
CREATE INDEX idx_is_main_product ON products(is_main_product);

-- 更新现有数据的is_main_product字段
UPDATE products 
SET is_main_product = CASE 
    WHEN parent_asin IS NULL THEN TRUE
    WHEN parent_asin IS NOT NULL AND asin = parent_asin THEN TRUE
    ELSE FALSE
END;
