package com.example.captain.service;

import com.example.captain.model.ProductDimensionsAndWeight;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

/**
 * OpenAI商品分析服务测试类
 * 注意：此测试需要配置有效的OpenAI API密钥才能运行
 */
@SpringBootTest
@TestPropertySource(properties = {
    "openai.api-key=your-openai-api-key-here",  // 请替换为实际的API密钥
    "openai.model=gpt-3.5-turbo"
})
public class OpenAIProductAnalysisServiceTest {

    @Test
    public void testExtractDimensionsAndWeight() {
        // 创建测试用的OpenAI服务
        ObjectMapper objectMapper = new ObjectMapper();
        OpenAIProductAnalysisService service = new OpenAIProductAnalysisService(
            "your-openai-api-key-here",  // 请替换为实际的API密钥
            objectMapper
        );

        // 测试数据
        String title = "Wireless Bluetooth Headphones - Over Ear Headphones with Microphone, 40H Playtime, Deep Bass, Soft Earmuffs, Foldable, Wired and Wireless Modes for PC/Cell Phones/TV/Travel";
        String feature = "【40H Playtime & Fast Charging】: Wireless headphones can be used for 40 hours in wireless mode (at 50% volume). Weight: 0.5 kg. Dimensions: 20cm x 15cm x 8cm";
        String description = "Premium wireless Bluetooth headphones with superior sound quality. Package dimensions: 20 x 15 x 8 centimeters. Shipping weight: 500 grams. Perfect for music lovers and professionals.";

        // 执行测试
        ProductDimensionsAndWeight result = service.extractDimensionsAndWeight(title, feature, description);

        // 验证结果
        if (result != null) {
            System.out.println("提取结果:");
            System.out.println("运输重量: " + result.getShippingWeight() + " " + result.getShippingWeightUnitOfMeasure());
            System.out.println("深度: " + result.getItemDepthFrontToBack() + " " + result.getItemDepthUnit());
            System.out.println("宽度: " + result.getItemWidthSideToSide() + " " + result.getItemWidthUnit());
            System.out.println("高度: " + result.getItemHeightFloorToTop() + " " + result.getItemHeightUnitOfMeasure());
        } else {
            System.out.println("未能提取到数据");
        }
    }

    @Test
    public void testWithoutApiKey() {
        // 测试没有API密钥的情况
        ObjectMapper objectMapper = new ObjectMapper();
        OpenAIProductAnalysisService service = new OpenAIProductAnalysisService("", objectMapper);

        String title = "Test Product";
        String feature = "Test feature";
        String description = "Test description";

        ProductDimensionsAndWeight result = service.extractDimensionsAndWeight(title, feature, description);

        // 应该返回null，因为没有API密钥
        assert result == null : "没有API密钥时应该返回null";
        System.out.println("没有API密钥时正确返回null");
    }
}
