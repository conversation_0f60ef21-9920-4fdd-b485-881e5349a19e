package com.example.worker.client;

import com.microsoft.playwright.*;
import com.microsoft.playwright.options.LoadState;
import com.microsoft.playwright.options.WaitForSelectorState;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Scanner;
import java.util.stream.Collectors;

/**
 * Amazon客户端
 * 用于管理Amazon会话和浏览器操作
 */
@Slf4j
public class AmazonClient implements AutoCloseable {

    // 会话文件路径常量
    private static final String SESSION_FILE_PATH = "src/main/resources/session/amazon_session.json";
    private static final String AMAZON_URL = "https://www.amazon.co.uk/";

    private final Playwright playwright;
    private final Browser browser;
    private BrowserContext context;
    private boolean headless;
    // 会话初始化标识
    private boolean sessionInitialized = false;

    /**
     * 创建Amazon客户端
     *
     * @param headless 是否使用无头模式
     */
    public AmazonClient(boolean headless) {
        this.headless = headless;
        log.info("正在初始化AmazonClient...");

        // 确保会话文件目录存在
        try {
            Path resourceDir = Paths.get(SESSION_FILE_PATH).getParent();
            if (resourceDir != null && !Files.exists(resourceDir)) {
                Files.createDirectories(resourceDir);
                log.info("创建会话文件目录: {}", resourceDir);
            }
        } catch (Exception e) {
            log.warn("创建会话文件目录时出错: {}", e.getMessage());
        }

        playwright = Playwright.create();
        browser = playwright.chromium().launch(
                new BrowserType.LaunchOptions()
                        .setHeadless(headless)
        );
        log.info("AmazonClient初始化完成");
    }

    /**
     * 初始化会话
     * 1. 加载会话文件并检查登录状态
     * 2. 如果未登录，等待用户手动登录
     * 3. 如果已登录，直接更新会话文件
     * 4. 保存会话文件
     *
     * @return 操作是否成功
     */
    public boolean initSession() {
        // 如果会话已初始化，直接返回true
        if (sessionInitialized) {
            log.info("会话已初始化，跳过初始化过程");
            return true;
        }

        log.info("开始Amazon会话管理...");
        Path sessionPath = Paths.get(SESSION_FILE_PATH);
        boolean sessionExists = Files.exists(sessionPath);

        try (Scanner scanner = new Scanner(System.in, StandardCharsets.UTF_8.name())) {
            // 如果是无头模式但需要登录，临时切换到有头模式
            boolean tempHeadless = false;

            // 创建上下文
            if (sessionExists) {
                log.info("已找到会话文件: {}, 正在加载...", SESSION_FILE_PATH);

                // 创建带会话状态的浏览器上下文
                context = browser.newContext(
                    new Browser.NewContextOptions()
                        .setStorageStatePath(sessionPath)
                );

                // 创建页面并导航到Amazon
                Page page = context.newPage();
                log.info("正在打开Amazon...");

                // 设置导航超时时间，避免长时间等待
                try {
                    // 使用较短的超时时间进行导航
                    page.navigate(AMAZON_URL, new Page.NavigateOptions().setTimeout(15000));
                } catch (Exception e) {
                    log.warn("导航超时，但将继续尝试检查页面状态: {}", e.getMessage());
                    // 即使导航超时，页面可能已经部分加载，我们仍然可以继续
                }

                // 不等待整个页面加载，直接检查账户元素
                log.info("检查账户元素是否已加载...");

                // 使用轮询方式检查账户元素，最多尝试5次
                ElementHandle accountElement = null;
                boolean elementFound = false;

                for (int attempt = 1; attempt <= 5; attempt++) {
                    try {
                        // 使用较短的超时时间
                        accountElement = page.waitForSelector("#nav-link-accountList-nav-line-1",
                            new Page.WaitForSelectorOptions()
                                .setState(WaitForSelectorState.VISIBLE)
                                .setTimeout(3000));

                        if (accountElement != null) {
                            log.info("账户元素已加载 (尝试 {}/5): {}", attempt, accountElement.textContent());
                            elementFound = true;
                            break;
                        }
                    } catch (Exception e) {
                        log.warn("等待账户元素超时 (尝试 {}/5)", attempt);

                        // 尝试点击页面任意位置，有时可以触发加载
                        try {
                            page.mouse().click(100, 100);
                        } catch (Exception ignored) {
                            // 忽略点击错误
                        }

                        // 等待一段时间再次尝试
                        try {
                            page.waitForTimeout(1000);
                        } catch (Exception ignored) {
                            // 忽略等待错误
                        }
                    }
                }

                if (!elementFound) {
                    log.warn("无法找到账户元素，将使用备用方法检查登录状态");
                }

                // 检查是否已登录
                boolean isLoggedIn = isLoggedInToAmazon(page);

                if (isLoggedIn) {
                    // 已登录，直接更新会话文件
                    log.info("检测到登录状态，无需重新登录");
                    System.out.println("\n检测到您已登录Amazon，正在更新会话文件...");
                    saveSessionState(context, sessionPath);
                } else {
                    // 未登录，需要切换到有头模式
                    log.info("未检测到登录状态，需要登录");

                    // 关闭当前上下文
                    context.close();

                    // 如果当前是无头模式，需要重新创建浏览器
                    if (headless) {
                        tempHeadless = true;
                        log.info("切换到有头模式以便登录");
                    }

                    // 创建新的上下文（有头模式）
                    handleLogin(scanner, sessionPath, tempHeadless);
                }
            } else {
                // 会话文件不存在，需要登录
                log.info("会话文件不存在，需要创建新会话");

                // 如果当前是无头模式，需要切换到有头模式
                if (headless) {
                    tempHeadless = true;
                    log.info("切换到有头模式以便登录");
                }

                handleLogin(scanner, sessionPath, tempHeadless);
            }

            // 设置会话初始化标识为true
            sessionInitialized = true;
            return true;
        } catch (Exception e) {
            log.error("管理会话状态时出错", e);
            return false;
        }
    }

    /**
     * 处理登录流程
     *
     * @param scanner 控制台输入扫描器
     * @param sessionPath 会话文件路径
     * @param tempHeadless 是否是临时有头模式
     */
    private void handleLogin(Scanner scanner, Path sessionPath, boolean tempHeadless) {
        try {
            // 如果需要临时切换到有头模式
            Browser loginBrowser = tempHeadless ?
                playwright.chromium().launch(new BrowserType.LaunchOptions().setHeadless(false)) :
                browser;

            try {
                // 创建新的上下文
                BrowserContext loginContext = loginBrowser.newContext();

                // 创建页面并导航到Amazon
                Page page = loginContext.newPage();
                log.info("正在打开Amazon...");

                // 设置导航超时时间，避免长时间等待
                try {
                    // 使用较短的超时时间进行导航
                    page.navigate(AMAZON_URL, new Page.NavigateOptions().setTimeout(15000));
                } catch (Exception e) {
                    log.warn("导航超时，但将继续尝试检查页面状态: {}", e.getMessage());
                    // 即使导航超时，页面可能已经部分加载，我们仍然可以继续
                }

                // 不等待整个页面加载，直接检查账户元素
                log.info("检查账户元素是否已加载...");

                // 使用轮询方式检查账户元素，最多尝试5次
                ElementHandle accountElement = null;
                boolean elementFound = false;

                for (int attempt = 1; attempt <= 5; attempt++) {
                    try {
                        // 使用较短的超时时间
                        accountElement = page.waitForSelector("#nav-link-accountList-nav-line-1",
                            new Page.WaitForSelectorOptions()
                                .setState(WaitForSelectorState.VISIBLE)
                                .setTimeout(3000));

                        if (accountElement != null) {
                            log.info("账户元素已加载 (尝试 {}/5): {}", attempt, accountElement.textContent());
                            elementFound = true;
                            break;
                        }
                    } catch (Exception e) {
                        log.warn("等待账户元素超时 (尝试 {}/5)", attempt);

                        // 尝试点击页面任意位置，有时可以触发加载
                        try {
                            page.mouse().click(100, 100);
                        } catch (Exception ignored) {
                            // 忽略点击错误
                        }

                        // 等待一段时间再次尝试
                        try {
                            page.waitForTimeout(1000);
                        } catch (Exception ignored) {
                            // 忽略等待错误
                        }
                    }
                }

                if (!elementFound) {
                    log.warn("无法找到账户元素，将继续登录流程");
                }

                // 提示用户登录
                System.out.println("\n请在浏览器中登录Amazon...");
                System.out.println("登录后，在控制台输入'done'保存会话。");

                waitForUserInput(scanner);
                saveSessionState(loginContext, sessionPath);

                // 关闭登录上下文
                loginContext.close();
            } finally {
                // 如果使用了临时浏览器，关闭它
                if (tempHeadless) {
                    loginBrowser.close();
                }
            }

            // 重新加载会话
            if (context != null) {
                context.close();
            }

            context = browser.newContext(
                new Browser.NewContextOptions()
                    .setStorageStatePath(sessionPath)
            );

        } catch (Exception e) {
            log.error("处理登录时出错", e);
        }
    }

    /**
     * 获取Amazon产品链接和下一页链接
     *
     * @param url Amazon产品搜索页面URL
     * @return 包含产品链接和下一页链接的Map
     */
    public Map<String, Object> getProductLinksAndNextPage(String url) {
        log.info("正在获取Amazon产品链接和下一页: {}", url);

        // 在try块外声明页面变量，以便在catch块中访问
        Page page = null;

        try {
            // 确保上下文已初始化
            if (context == null) {
                log.error("浏览器上下文未初始化，请先调用initSession()");
                return Map.of("error", "浏览器上下文未初始化");
            }

            // 创建页面
            page = context.newPage();

            // 设置页面超时时间
            page.setDefaultTimeout(60000); // 60秒

            // 导航到URL
            log.info("正在打开网页: {}", url);
            page.navigate(url);

            // 等待页面基本结构加载完成，比waitForLoadState()更快
            page.waitForLoadState(LoadState.DOMCONTENTLOADED);

            log.info("页面标题: {}", page.title());

            // 等待产品列表加载
            page.waitForSelector("a.a-link-normal.s-line-clamp-2.s-link-style.a-text-normal",
                new Page.WaitForSelectorOptions().setTimeout(10000));

            // 获取产品链接
            String productLinksScript = "Array.from(document.querySelectorAll('a.a-link-normal.s-line-clamp-2.s-link-style.a-text-normal')).map(link => 'https://www.amazon.co.uk'+link.getAttribute('href'))";
            @SuppressWarnings("unchecked")
            List<String> productLinks = (List<String>) page.evaluate(productLinksScript);

            // 获取下一页链接
            String nextPageScript = "node=document.querySelector('a.s-pagination-item.s-pagination-next.s-pagination-button.s-pagination-button-accessibility.s-pagination-separator');node?'https://www.amazon.co.uk'+node.getAttribute('href'):null";
            String nextPage = (String) page.evaluate(nextPageScript);

            // 输出结果到控制台
            System.out.println("\n产品链接:");
            for (int i = 0; i < productLinks.size(); i++) {
                System.out.println((i + 1) + ". " + productLinks.get(i));
            }

            System.out.println("\n下一页: " + (nextPage != null ? nextPage : "无"));

            // 使用HashMap代替Map.of()，因为Map.of()不允许null值
            Map<String, Object> result = new HashMap<>();
            result.put("product_links", productLinks);
            result.put("next_page", nextPage);
            return result;

        } catch (Exception e) {
            log.error("获取Amazon产品链接时出错", e);

            // 保存调试信息到debug目录
            saveDebugInfo(page);

            // 使用HashMap代替Map.of()，因为Map.of()不允许null值
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", e.getMessage() != null ? e.getMessage() : "未知错误");
            return errorResult;
        } finally {
            // 确保页面被关闭
            if (page != null) {
                try {
                    page.close();
                } catch (Exception ex) {
                    log.warn("关闭页面时出错", ex);
                }
            }
        }
    }

    /**
     * 获取浏览器上下文
     *
     * @return 浏览器上下文
     */
    public BrowserContext getContext() {
        return context;
    }

    /**
     * 检查Amazon登录状态
     * 使用多种方法检查登录状态，提高可靠性
     *
     * @param page Playwright页面对象
     * @return 是否已登录
     */
    private boolean isLoggedInToAmazon(Page page) {
        // 方法1: 检查账户元素文本
        try {
            ElementHandle accountElement = page.querySelector("#nav-link-accountList-nav-line-1");
            if (accountElement != null) {
                String accountText = accountElement.textContent();
                boolean isLoggedIn = !accountText.contains("Sign in");
                log.info("方法1 - Amazon账户状态: {}, 登录状态: {}", accountText, isLoggedIn);
                if (isLoggedIn) {
                    return true;
                }
            }
        } catch (Exception e) {
            log.warn("方法1检查登录状态时出错: {}", e.getMessage());
        }

        // 方法2: 检查欢迎消息
        try {
            ElementHandle welcomeElement = page.querySelector("#nav-link-accountList");
            if (welcomeElement != null) {
                String hoverText = welcomeElement.getAttribute("aria-label");
                if (hoverText != null && !hoverText.contains("Sign in")) {
                    log.info("方法2 - 检测到欢迎消息: {}", hoverText);
                    return true;
                }
            }
        } catch (Exception e) {
            log.warn("方法2检查登录状态时出错: {}", e.getMessage());
        }

        // 方法3: 检查页面上是否有"Hello, sign in"文本
        try {
            String pageContent = page.content();
            if (!pageContent.contains("Hello, sign in")) {
                // 如果页面上没有"Hello, sign in"文本，可能已登录
                log.info("方法3 - 页面内容不包含'Hello, sign in'，可能已登录");
                return true;
            }
        } catch (Exception e) {
            log.warn("方法3检查登录状态时出错: {}", e.getMessage());
        }

        // 方法4: 检查是否有"Your Account"链接
        try {
            ElementHandle yourAccountLink = page.querySelector("a[href*='/gp/css/homepage']");
            if (yourAccountLink != null) {
                log.info("方法4 - 检测到'Your Account'链接，已登录");
                return true;
            }
        } catch (Exception e) {
            log.warn("方法4检查登录状态时出错: {}", e.getMessage());
        }

        // 如果所有方法都失败，则认为未登录
        log.warn("所有方法都未检测到登录状态，认为未登录");
        return false;
    }

    /**
     * 等待用户在控制台输入完成
     *
     * @param scanner 控制台输入扫描器
     * @return 用户输入
     */
    private String waitForUserInput(Scanner scanner) {
        String input;
        do {
            input = scanner.nextLine().trim().toLowerCase();
        } while (!input.equals("done"));
        return input;
    }

    /**
     * 保存会话状态到文件
     *
     * @param context 浏览器上下文
     * @param sessionPath 会话文件路径
     */
    private void saveSessionState(BrowserContext context, Path sessionPath) {
        try {
            // 确保目录存在
            Path resourceDir = sessionPath.getParent();
            if (!Files.exists(resourceDir)) {
                Files.createDirectories(resourceDir);
            }

            // 保存会话状态
            context.storageState(new BrowserContext.StorageStateOptions().setPath(sessionPath));
            log.info("会话状态已保存到: {}", sessionPath);
            System.out.println("会话文件已保存！");
        } catch (Exception e) {
            log.error("保存会话状态时出错", e);
            System.out.println("保存会话文件失败: " + e.getMessage());
        }
    }

    /**
     * 关闭资源
     */
    @Override
    public void close() {
        try {
            if (context != null) {
                context.close();
            }
            if (browser != null) {
                browser.close();
            }
            if (playwright != null) {
                playwright.close();
            }
            // 重置会话初始化标识
            sessionInitialized = false;
            log.info("AmazonClient已关闭");
        } catch (Exception e) {
            log.error("关闭AmazonClient时出错", e);
        }
    }

    /**
     * 获取Amazon商品详细信息
     *
     * @param url Amazon商品详情页URL
     * @return 包含商品详细信息的Map
     */
    public Map<String, Object> getProductInfo(String url) {
        log.info("正在获取Amazon商品详细信息: {}", url);

        // 在try块外声明页面变量，以便在catch块中访问
        Page page = null;

        try {
            // 确保上下文已初始化
            if (context == null) {
                log.error("浏览器上下文未初始化，请先调用initSession()");
                // 使用HashMap代替Map.of()，因为Map.of()不允许null值
                Map<String, Object> errorResult = new HashMap<>();
                errorResult.put("error", "浏览器上下文未初始化");
                return errorResult;
            }

            // 创建页面
            page = context.newPage();

            // 设置页面超时时间
            page.setDefaultTimeout(60000); // 60秒

            // 导航到URL
            log.info("正在打开商品页面: {}", url);
            page.navigate(url);

            // 等待页面基本结构加载完成，比waitForLoadState()更快
            page.waitForLoadState(LoadState.DOMCONTENTLOADED);

            log.info("页面标题: {}", page.title());

            // 定义数据抽取模板
            Map<String, String> dataPattern = new java.util.HashMap<>();

            // ASIN
            dataPattern.put("ASIN", "document.getElementById(\"ASIN\").value");

            // BuyBox价格
            dataPattern.put("price", "document.querySelector(\".a-offscreen\").innerText");

            // Recommended Browse Nodes （推荐的浏览节点)
            dataPattern.put("recommended_browse_nodes",
                "Product_type_node_list=document.querySelectorAll(\"span.a-list-item a.a-link-normal.a-color-tertiary\")\n" +
                "    max_node_id = 0\n" +
                "for (let index = 0;index < Product_type_node_list.length; index++) {\n" +
                "    n = Product_type_node_list[index]\n" +
                "    url = n.href\n" +
                "    match = url.match(/node=(.+)/s);\n" +
                "    Product_type = ''\n" +
                "    if(match){\n" +
                "        node_id = match[1]\n" +
                "        if(node_id > max_node_id){\n" +
                "                max_node_id = node_id\n" +
                "                Product_type=n.innerText\n" +
                "        }\n" +
                "        \n" +
                "    }\n" +
                "}\n" +
                "max_node_id");

            // Product Type（产品类型）
            dataPattern.put("Product_type",
                "Product_type_node_list=document.querySelectorAll(\"span.a-list-item a.a-link-normal.a-color-tertiary\")\n" +
                "    Product_type = ''\n" +
                "    max_node_id = 0\n" +
                "for (let index = 0;index < Product_type_node_list.length; index++) {\n" +
                "    n = Product_type_node_list[index]\n" +
                "    url = n.href\n" +
                "    match = url.match(/node=(.+)/s);\n" +
                "    if(match){\n" +
                "        node_id = match[1]\n" +
                "        if(node_id > max_node_id){\n" +
                "                max_node_id = node_id\n" +
                "                Product_type=n.innerText\n" +
                "        }\n" +
                "        \n" +
                "    }\n" +
                "}\n" +
                "Product_type");

            // 产品详情表格
            dataPattern.put("prod_details", "document.querySelector('#prodDetails').innerText");

            // 产品概览
            dataPattern.put("product_overview", "document.querySelector('#productOverview_feature_div').innerText");

            // 高清图地址列表
            dataPattern.put("images",
                "pageSource = document.documentElement.outerHTML\n" +
                "const regex = /'colorImages':(.+)\"/g;\n" +
                "himatch = pageSource.match(regex);\n" +
                "eval('colorImages = {'+himatch[0]+'}]}}');\n" +
                "imgs = [];\n" +
                "for (let index = 0; index < colorImages['colorImages']['initial'].length; index++) {\n" +
                "    item = colorImages['colorImages']['initial'][index];\n" +
                "    if(item['hiRes']){\n" +
                "        imgs.push(item['hiRes']);\n" +
                "    }else{\n" +
                "        imgs.push(item['large']);\n" +
                "    }\n" +
                "}\n" +
                "imgs");

            // 产品名称
            dataPattern.put("title", "document.getElementById(\"title\").innerText");

            // 产品短描述
            dataPattern.put("feature", "document.querySelector(\"#feature-bullets ul\").innerText");

            // 产品长描述
            dataPattern.put("description", "node=document.getElementById(\"productDescription\")?document.getElementById(\"productDescription\"):document.getElementById(\"aplus_feature_div\");node?node.innerText:null");

            // 国家
            dataPattern.put("country", "document.getElementById(\"nav-logo\").innerText");

            // 变体商品链接
            dataPattern.put("productVariants",
                "pagesource = document.documentElement.outerHTML\n" +
                "productVariants = []\n" +
                "const variationValuesRegex = /\"shouldUseDPXTwisterData\" :([\\s\\S]+?)\"unselectedDimCount\"/;\n" +
                "match = pagesource.match(variationValuesRegex);\n" +
                "if(match){ \n" +
                "    // 变体商品\n" +
                "    // variationValues 变体 key_value\n" +
                "    // dimensionToAsinMap 变体组合映射的 ASIN\n" +
                "    const asinMapRegex = /\"currentAsin\" :([\\s\\S]+?),\\n\\s+\"dimensionsDisplayType\"/;       \n" +
                "    m = match[1].match(asinMapRegex);\n" +
                "    eval('variation = {\"currentAsin\":'+m[1]+'}')\n" +
                "\n" +
                "    parentAsin = variation['parentAsin']\n" +
                "    currentAsin = variation['currentAsin']\n" +
                "    // 获取所有可能的下标组合\n" +
                "        const variationKeys = variation.dimensions; // 获取所有 variation 的键\n" +
                "        const variationLengths = variationKeys.map(key => variation.variationValues[key].length); // 获取每个 variation 的长度\n" +
                "        const combinations = [];\n" +
                "\n" +
                "        // 递归生成所有可能的下标组合\n" +
                "        function generateCombinations(index, currentCombination, results,variationKeys) {\n" +
                "            if (index === variationKeys.length) {\n" +
                "            results.push(currentCombination.slice());\n" +
                "            return;\n" +
                "            }\n" +
                "            for (let i = 0; i < variationLengths[index]; i++) {\n" +
                "                currentCombination[index] = i;\n" +
                "                generateCombinations(index + 1, currentCombination, results,variationKeys);\n" +
                "            }\n" +
                "        }\n" +
                "\n" +
                "        generateCombinations(0, [], combinations,variationKeys);           \n" +
                "\n" +
                "        // 遍历所有组合并获取对应的 ASIN 值\n" +
                "        combinations.forEach(combination => {\n" +
                "            // 构造键值，例如 \"0_0\" 或 \"0_1_2\" 等\n" +
                "            const key = combination.join('_');\n" +
                "            const asin = variation.dimensionToAsinMap[key]; // 获取 ASIN 值\n" +
                "            if (asin) {\n" +
                "                console.log('==>',key,asin)\n" +
                "                \n" +
                "                // 添加变体商品链接\n" +
                "                const link = 'https://www.amazon.co.uk/dp/'+asin+'?th=1'\n" +
                "                // 由于parentAsin会指向一个变体商品，所以第一个currentAsin 会被当做 parentAsin 保存下来\n" +
                "                // 导出csv的时候根据 parentAsin 来生成id，设置型号为Parent\n" +
                "                console.log(currentAsin+ \" 商品解析出变体商品：\",link)\n" +
                "                let result = new Map();\n" +
                "                product_sku = ''\n" +
                "                combination.forEach((value, index) => {\n" +
                "                    const keyName = variationKeys[index];\n" +
                "                    result[keyName] = variation.variationValues[keyName][value];\n" +
                "                    product_sku = product_sku+' '+result[keyName]\n" +
                "                });\n" +
                "\n" +
                "                result['parentAsin'] = currentAsin\n" +
                "                result['link'] = link\n" +
                "                result['asin'] = asin\n" +
                "                result['product_sku'] = product_sku    \n" +
                "                productVariants.push(result)             \n" +
                "                console.log('productVariants==>:',productVariants)\n" +
                "            }\n" +
                "        });        \n" +
                "    }\n" +
                "    productVariants");

            // 产品详细信息
            dataPattern.put("prod_details", "document.querySelector('#prodDetails')?document.querySelector('#prodDetails').innerText:null");

            // 产品概览
            dataPattern.put("product_overview", "document.querySelector('#productOverview_feature_div')?document.querySelector('#productOverview_feature_div').innerText:null");

            // 抽取数据
            Map<String, Object> productInfo = extractProductInfo(page, dataPattern);

            // 下载商品图片
            if (productInfo.containsKey("ASIN") && productInfo.containsKey("images")) {
                String asin = (String) productInfo.get("ASIN");
                @SuppressWarnings("unchecked")
                List<String> imageUrls = (List<String>) productInfo.get("images");

                if (asin != null && imageUrls != null && !imageUrls.isEmpty()) {
                    List<String> savedImagePaths = saveProductImages(asin, imageUrls, page);
                    productInfo.put("saved_images", savedImagePaths);
                    log.info("已保存 {} 张商品图片", savedImagePaths.size());
                }
            }

            // 输出结果到控制台
            System.out.println("\n商品信息:");
            productInfo.forEach((key, value) -> {
                if (value instanceof List) {
                    System.out.println(key + ": " + ((List<?>) value).size() + " 项");
                } else {
                    System.out.println(key + ": " + value);
                }
            });

            return productInfo;

        } catch (Exception e) {
            log.error("获取Amazon商品信息时出错", e);

            // 保存调试信息到debug目录
            saveDebugInfo(page);

            // 使用HashMap代替Map.of()，因为Map.of()不允许null值
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", e.getMessage() != null ? e.getMessage() : "未知错误");
            return errorResult;
        } finally {
            // 确保页面被关闭
            if (page != null) {
                try {
                    page.close();
                } catch (Exception ex) {
                    log.warn("关闭页面时出错", ex);
                }
            }
        }
    }

    /**
     * 从页面中抽取商品信息
     *
     * @param page 页面对象
     * @param dataPattern 数据抽取模板
     * @return 抽取的商品信息
     */
    private Map<String, Object> extractProductInfo(Page page, Map<String, String> dataPattern) {
        Map<String, Object> result = new java.util.HashMap<>();

        // 遍历数据抽取模板
        for (Map.Entry<String, String> entry : dataPattern.entrySet()) {
            String key = entry.getKey();
            String script = entry.getValue();

            try {
                // 执行JavaScript脚本抽取数据
                Object value = page.evaluate(script);
                result.put(key, value);
                log.debug("成功抽取 {}: {}", key, value);
            } catch (Exception e) {
                log.warn("抽取 {} 时出错: {}", key, e.getMessage());
                result.put(key, null);
            }
        }

        return result;
    }

    /**
     * 保存商品图片到指定文件夹
     *
     * @param asin 商品ASIN
     * @param imageUrls 图片URL列表
     * @param page Playwright页面对象，用于下载图片
     * @return 保存的图片路径列表
     */
    public List<String> saveProductImages(String asin, List<String> imageUrls, Page page) {
        String folderPath = "images/amazon/" + asin;
        log.info("开始保存Amazon商品图片到文件夹: {}", folderPath);
        List<String> savedImagePaths = new ArrayList<>();

        try {
            // 确保目标文件夹存在
            Path folder = Paths.get(folderPath);
            if (!Files.exists(folder)) {
                Files.createDirectories(folder);
                log.info("创建商品图片保存目录: {}", folder);
            }

            // 下载并保存每个图片
            int count = 0;
            for (String imageUrl : imageUrls) {
                try {
                    // 生成唯一文件名
                    String fileName = asin + "_" + count + getFileExtension(imageUrl);
                    Path imagePath = folder.resolve(fileName);

                    // 使用Playwright下载图片
                    byte[] imageBytes = page.request().fetch(imageUrl).body();

                    // 保存图片
                    if (imageBytes != null) {
                        Files.write(imagePath, imageBytes);
                        savedImagePaths.add(imagePath.toString());
                        log.info("已保存商品图片 {}/{}: {}", count + 1, imageUrls.size(), imagePath);
                    } else {
                        log.warn("无法下载图片: {}", imageUrl);
                    }

                    count++;
                } catch (Exception e) {
                    log.error("保存图片时出错: {}", imageUrl, e);
                }
            }

            log.info("成功保存 {} 个图片到 {}", savedImagePaths.size(), folderPath);
        } catch (Exception e) {
            log.error("保存商品图片时出错", e);
        }

        return savedImagePaths;
    }

    /**
     * 获取URL的文件扩展名
     *
     * @param url 图片URL
     * @return 文件扩展名（包括点号）
     */
    private String getFileExtension(String url) {
        try {
            // 移除URL参数
            String cleanUrl = url.split("\\?")[0];

            // 获取路径部分
            String path = new java.net.URL(cleanUrl).getPath();

            // 提取扩展名
            int lastDotPos = path.lastIndexOf('.');
            if (lastDotPos >= 0) {
                String extension = path.substring(lastDotPos);
                // 验证扩展名是否为常见图片格式
                if (extension.matches("\\.(jpg|jpeg|png|gif|bmp|webp|svg)")) {
                    return extension;
                }
            }
        } catch (Exception e) {
            log.debug("获取文件扩展名时出错: {}", url, e);
        }

        // 默认返回.jpg
        return ".jpg";
    }

    /**
     * 保存页面调试信息到debug目录
     * 包括页面截图和页面源码
     *
     * @param page Playwright页面对象
     */
    private void saveDebugInfo(Page page) {
        try {
            // 确保debug目录存在
            Path debugDir = Paths.get("debug");
            if (!Files.exists(debugDir)) {
                Files.createDirectories(debugDir);
                log.info("创建debug目录: {}", debugDir);
            }

            // 生成带时间戳的文件名
            String timestamp = String.valueOf(System.currentTimeMillis());

            // 保存页面截图
            if (page != null) {
                Path screenshotPath = debugDir.resolve("amazon-error-" + timestamp + ".png");
                page.screenshot(new Page.ScreenshotOptions().setPath(screenshotPath));
                log.info("错误页面截图已保存到: {}", screenshotPath);

                // 保存页面源码
                Path htmlPath = debugDir.resolve("amazon-error-" + timestamp + ".html");
                String pageContent = page.content();
                Files.write(htmlPath, pageContent.getBytes(StandardCharsets.UTF_8));
                log.info("错误页面源码已保存到: {}", htmlPath);
            } else {
                log.warn("无法保存页面截图和源码，页面对象为null");
            }
        } catch (Exception e) {
            log.error("保存调试信息时出错", e);
        }
    }
}