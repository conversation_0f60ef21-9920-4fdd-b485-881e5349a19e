package com.example.worker.service;

import com.example.worker.client.Alibaba1688Client;
import com.microsoft.playwright.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.PreDestroy;
import java.util.List;
import java.util.Map;
import java.util.Random;

/**
 * 阿里巴巴1688服务
 * 用于处理1688相关操作
 */
@Service
@Slf4j
public class Alibaba1688Service implements AutoCloseable {

    private Alibaba1688Client alibaba1688Client;
    private Page homePage; // 保存打开的1688首页页面对象

    // 人类操作延迟范围（毫秒）
    private static final int MIN_DELAY = 1000;  // 最小延迟
    private static final int MAX_DELAY = 3000;  // 最大延迟
    private static final Random random = new Random();

    /**
     * 构造函数
     * 初始化Alibaba1688Client并打开1688首页
     */
    public Alibaba1688Service() {
        // 创建无头模式的Alibaba1688Client
        this.alibaba1688Client = new Alibaba1688Client(false);

        // 初始化会话
        try {
            boolean sessionInitialized = alibaba1688Client.initSession();
            if (sessionInitialized) {
                log.info("Alibaba1688会话初始化成功");
            } else {
                log.warn("Alibaba1688会话初始化失败，可能需要手动登录");
            }

            // 打开1688首页并保持浏览器窗口打开
            this.homePage = alibaba1688Client.openHomePage();
            if (this.homePage != null) {
                log.info("已成功打开1688首页，标题: {}", homePage.title());
                System.out.println("已成功打开1688首页，浏览器窗口将保持打开状态");
            } else {
                log.error("打开1688首页失败");
                System.err.println("打开1688首页失败，请检查网络连接或会话状态");
            }
        } catch (Exception e) {
            log.error("初始化Alibaba1688Service时出错", e);
            System.err.println("初始化Alibaba1688Service时出错: " + e.getMessage());
        }

        log.info("Alibaba1688Service已初始化");
    }

    /**
     * 使用图片链接搜索商品
     *
     * @param imageUrl 图片URL
     * @return 搜索结果
     */
    public Map<String, Object> searchByImage(String imageUrl) {
        log.info("正在使用图片搜索1688商品: {}", imageUrl);
        return alibaba1688Client.searchByImage(imageUrl);
    }

    /**
     * 关闭服务
     * 释放Alibaba1688Client资源
     */
    @Override
    @PreDestroy
    public void close() {
        try {
            // 先关闭打开的首页
            if (homePage != null) {
                try {
                    homePage.close();
                    log.info("已关闭1688首页");
                } catch (Exception e) {
                    log.warn("关闭1688首页时出错: {}", e.getMessage());
                }
                homePage = null;
            }

            // 然后关闭客户端
            if (alibaba1688Client != null) {
                alibaba1688Client.close();
                log.info("Alibaba1688Client已关闭");
            }

            log.info("Alibaba1688Service已关闭");
        } catch (Exception e) {
            log.error("关闭Alibaba1688Service时发生错误", e);
        }
    }

    /**
     * 模拟人类操作的随机延迟
     * 在操作之间添加随机延迟，使自动化操作更自然
     */
    private static void humanDelay() {
        try {
            int delay = MIN_DELAY + random.nextInt(MAX_DELAY - MIN_DELAY);
            log.debug("添加人类操作延迟: {}毫秒", delay);
            Thread.sleep(delay);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("人类操作延迟被中断", e);
        }
    }

    /**
     * 执行图片搜索并显示结果
     *
     * @param alibaba1688Service 阿里巴巴1688服务实例
     * @param imageUrl 图片URL
     */
    private static void executeImageSearch(Alibaba1688Service alibaba1688Service, String imageUrl) {
        System.out.println("开始使用图片搜索1688商品...");
        System.out.println("图片URL: " + imageUrl);

        Map<String, Object> result = alibaba1688Service.searchByImage(imageUrl);

        if (result.containsKey("error")) {
            System.err.println("搜索出错: " + result.get("error"));
        } else {
            System.out.println("搜索成功!");
            System.out.println("页面标题: " + result.get("title"));
            System.out.println("当前URL: " + result.get("url"));
            System.out.println("截图保存路径: " + result.get("screenshot_path"));

            // 输出调试信息
            @SuppressWarnings("unchecked")
            List<String> foundSelectors = (List<String>) result.get("found_selectors");
            System.out.println("\n找到的选择器:");
            if (foundSelectors != null && !foundSelectors.isEmpty()) {
                for (String selector : foundSelectors) {
                    System.out.println("  - " + selector);
                }
            } else {
                System.out.println("  未找到匹配的选择器");
            }

            // 输出提取的商品信息
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> products = (List<Map<String, Object>>) result.get("products");
            System.out.println("\n提取到的商品信息 (" + products.size() + " 个商品):");

            // 显示前5个商品的信息
            int displayCount = Math.min(products.size(), 5);
            for (int i = 0; i < displayCount; i++) {
                Map<String, Object> product = products.get(i);
                System.out.println("\n商品 #" + (i + 1) + ":");
                System.out.println("  标题: " + product.get("title"));
                System.out.println("  价格: " + product.get("price"));
                System.out.println("  图片: " + product.get("imgUrl"));
                System.out.println("  链接: " + product.get("link"));
            }

            if (products.size() > 5) {
                System.out.println("\n... 还有 " + (products.size() - 5) + " 个商品 ...");
            } else if (products.isEmpty()) {
                System.out.println("  未找到商品信息");
            }

            // 输出页面源码的一部分
            String pageContent = (String) result.get("page_content");
            if (pageContent != null) {
                int maxLength = Math.min(pageContent.length(), 500);
                System.out.println("\n页面源码片段: " + pageContent.substring(0, maxLength) + "...");

                // 保存完整页面源码到文件
                System.out.println("完整页面源码已保存到截图同目录");
            }
        }
    }

    /**
     * 主方法 - 初始化Alibaba1688Service并停留在1688首页
     *
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        System.out.println("=== Alibaba1688Service.main() 开始执行 ===");

        Alibaba1688Client client = new Alibaba1688Client(false); // 创建有头模式的客户端
        try {
        client.searchByImagePlugin("https://m.media-amazon.com/images/I/61RjwGw-dkL.__AC_SX300_SY300_QL70_ML2_.jpg");
        
        } catch (Exception e) {
            System.err.println("执行过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        } finally {
            client.close();
        }

        System.out.println("=== Alibaba1688Service.main() 执行完毕 ===");
    }
}
