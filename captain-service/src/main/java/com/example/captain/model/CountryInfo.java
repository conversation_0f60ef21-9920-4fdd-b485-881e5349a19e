package com.example.captain.model;

import com.example.captain.enums.SiteRegion;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 国家信息模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CountryInfo {
    
    /**
     * 国家代码（ISO 3166-1 alpha-2）
     */
    private String countryCode;
    
    /**
     * 国家名称
     */
    private String countryName;
    
    /**
     * 货币代码（ISO 4217）
     */
    private String currencyCode;
    
    /**
     * 主要语言
     */
    private String language;
    
    /**
     * 站点区域
     */
    private SiteRegion siteRegion;
    
    /**
     * 站点区域中文名称
     */
    private String siteRegionName;
    
    /**
     * 对人民币汇率
     */
    private BigDecimal exchangeRate;
    
    /**
     * Amazon域名
     */
    private String amazonDomain;
    
    /**
     * Amazon市场ID
     */
    private String marketplaceId;
    
    /**
     * 汇率最后更新时间
     */
    private LocalDateTime exchangeRateUpdatedAt;
    
    /**
     * 是否启用
     */
    private Boolean enabled;
    
    /**
     * 排序顺序
     */
    private Integer sortOrder;
}
