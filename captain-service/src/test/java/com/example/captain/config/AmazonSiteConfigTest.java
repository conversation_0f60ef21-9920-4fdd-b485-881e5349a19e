package com.example.captain.config;

import com.example.captain.enums.SiteRegion;
import com.example.captain.model.CountryInfo;
import com.example.captain.model.CurrencyInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Amazon站点配置测试类
 */
class AmazonSiteConfigTest {

    private AmazonSiteConfig amazonSiteConfig;

    @BeforeEach
    void setUp() {
        amazonSiteConfig = new AmazonSiteConfig();
    }

    @Test
    void testGetAllCountries() {
        List<CountryInfo> countries = amazonSiteConfig.getAllCountries();
        
        assertNotNull(countries);
        assertEquals(11, countries.size());
        
        // 验证美国站点
        CountryInfo us = countries.stream()
                .filter(c -> "US".equals(c.getCountryCode()))
                .findFirst()
                .orElse(null);
        
        assertNotNull(us);
        assertEquals("美国", us.getCountryName());
        assertEquals("USD", us.getCurrencyCode());
        assertEquals("英语", us.getLanguage());
        assertEquals(SiteRegion.NORTH_AMERICA, us.getSiteRegion());
        assertEquals("北美站", us.getSiteRegionName());
        assertEquals(new BigDecimal("7.1810"), us.getExchangeRate());
        assertEquals("amazon.com", us.getAmazonDomain());
        assertEquals("ATVPDKIKX0DER", us.getMarketplaceId());
        assertTrue(us.getEnabled());
        assertEquals(1, us.getSortOrder());
    }

    @Test
    void testGetCountryByCode() {
        // 测试获取美国站点
        CountryInfo us = amazonSiteConfig.getCountryByCode("US");
        assertNotNull(us);
        assertEquals("美国", us.getCountryName());
        assertEquals("USD", us.getCurrencyCode());
        
        // 测试获取德国站点
        CountryInfo de = amazonSiteConfig.getCountryByCode("DE");
        assertNotNull(de);
        assertEquals("德国", de.getCountryName());
        assertEquals("EUR", de.getCurrencyCode());
        
        // 测试不存在的国家代码
        CountryInfo notFound = amazonSiteConfig.getCountryByCode("XX");
        assertNull(notFound);
    }

    @Test
    void testGetCountriesByRegion() {
        // 测试北美站
        List<CountryInfo> northAmerica = amazonSiteConfig.getCountriesByRegion(SiteRegion.NORTH_AMERICA);
        assertEquals(3, northAmerica.size());
        assertTrue(northAmerica.stream().anyMatch(c -> "美国".equals(c.getCountryName())));
        assertTrue(northAmerica.stream().anyMatch(c -> "加拿大".equals(c.getCountryName())));
        assertTrue(northAmerica.stream().anyMatch(c -> "墨西哥".equals(c.getCountryName())));
        
        // 测试欧洲站
        List<CountryInfo> europe = amazonSiteConfig.getCountriesByRegion(SiteRegion.EUROPE);
        assertEquals(8, europe.size());
        assertTrue(europe.stream().anyMatch(c -> "英国".equals(c.getCountryName())));
        assertTrue(europe.stream().anyMatch(c -> "德国".equals(c.getCountryName())));
    }

    @Test
    void testGetCountriesByRegionName() {
        List<CountryInfo> northAmerica = amazonSiteConfig.getCountriesByRegionName("北美站");
        assertEquals(3, northAmerica.size());
        
        List<CountryInfo> europe = amazonSiteConfig.getCountriesByRegionName("欧洲站");
        assertEquals(8, europe.size());
        
        List<CountryInfo> notFound = amazonSiteConfig.getCountriesByRegionName("未知站点");
        assertTrue(notFound.isEmpty());
    }

    @Test
    void testGetCountriesByCurrency() {
        // 测试EUR货币
        List<CountryInfo> eurCountries = amazonSiteConfig.getCountriesByCurrency("EUR");
        assertEquals(5, eurCountries.size());
        assertTrue(eurCountries.stream().allMatch(c -> "EUR".equals(c.getCurrencyCode())));
        
        // 测试USD货币
        List<CountryInfo> usdCountries = amazonSiteConfig.getCountriesByCurrency("USD");
        assertEquals(1, usdCountries.size());
        assertEquals("美国", usdCountries.get(0).getCountryName());
    }

    @Test
    void testGetAllRegions() {
        List<String> regions = amazonSiteConfig.getAllRegions();
        assertEquals(3, regions.size());
        assertTrue(regions.contains("北美站"));
        assertTrue(regions.contains("欧洲站"));
        assertTrue(regions.contains("亚太站"));
    }

    @Test
    void testGetAllCurrencies() {
        List<CurrencyInfo> currencies = amazonSiteConfig.getAllCurrencies();
        assertEquals(7, currencies.size());
        
        // 验证EUR货币信息
        CurrencyInfo eur = currencies.stream()
                .filter(c -> "EUR".equals(c.getCurrencyCode()))
                .findFirst()
                .orElse(null);
        
        assertNotNull(eur);
        assertEquals("欧元", eur.getCurrencyName());
        assertEquals("€", eur.getCurrencySymbol());
        assertEquals(5, eur.getCountryCount());
        assertTrue(eur.getIsMajor());
        assertEquals(2, eur.getDecimalPlaces());
    }

    @Test
    void testUpdateExchangeRate() {
        // 获取初始汇率
        BigDecimal originalRate = amazonSiteConfig.getCurrentExchangeRate("US");
        assertEquals(new BigDecimal("7.1810"), originalRate);
        
        // 更新汇率
        BigDecimal newRate = new BigDecimal("7.5000");
        amazonSiteConfig.updateExchangeRate("US", newRate);
        
        // 验证汇率已更新
        BigDecimal updatedRate = amazonSiteConfig.getCurrentExchangeRate("US");
        assertEquals(newRate, updatedRate);
        
        // 重置汇率
        amazonSiteConfig.resetExchangeRate("US");
        BigDecimal resetRate = amazonSiteConfig.getCurrentExchangeRate("US");
        assertEquals(originalRate, resetRate);
    }

    @Test
    void testUpdateExchangeRateValidation() {
        // 测试无效汇率
        assertThrows(IllegalArgumentException.class, () -> {
            amazonSiteConfig.updateExchangeRate("US", BigDecimal.ZERO);
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            amazonSiteConfig.updateExchangeRate("US", new BigDecimal("-1"));
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            amazonSiteConfig.updateExchangeRate("US", null);
        });
        
        // 测试无效国家代码
        assertThrows(IllegalArgumentException.class, () -> {
            amazonSiteConfig.updateExchangeRate("XX", new BigDecimal("7.0"));
        });
    }
}
