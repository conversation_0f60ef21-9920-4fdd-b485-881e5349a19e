package com.example.worker.imagesimilarity;

import org.opencv.core.Mat;

import java.io.File;
import java.lang.reflect.Field;

/**
 * OpenCV资源管理类
 * 负责OpenCV库的加载和释放
 */
public class OpenCVManager {

    private static boolean initialized = false;
    private static boolean shutdownHookDisabled = false;

    /**
     * 初始化OpenCV库
     */
    public static synchronized void initialize() {
        if (!initialized) {
            try {
                // 使用nu.pattern.OpenCV加载本地库
                nu.pattern.OpenCV.loadLocally();
                System.out.println("OpenCV 库加载成功: " + org.opencv.core.Core.VERSION);
                initialized = true;

                // 禁用OpenCV的关闭钩子
                disableShutdownHook();

                // 注册JVM关闭钩子，确保资源释放
                Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                    release();
                }));

            } catch (Exception e) {
                System.err.println("无法加载OpenCV库: " + e.getMessage());
                e.printStackTrace();
            }
        }
    }

    /**
     * 禁用OpenCV的关闭钩子
     * 防止JVM关闭时出现类加载器问题
     */
    public static synchronized void disableShutdownHook() {
        if (shutdownHookDisabled) {
            return;
        }

        try {
            // 获取OpenCV的TemporaryDirectory类
            Class<?> tempDirClass = Class.forName("nu.pattern.OpenCV$TemporaryDirectory");

            // 获取shutdownHook字段
            Field shutdownHookField = tempDirClass.getDeclaredField("shutdownHook");
            shutdownHookField.setAccessible(true);

            // 获取INSTANCE字段
            Field instanceField = tempDirClass.getDeclaredField("INSTANCE");
            instanceField.setAccessible(true);
            Object tempDirInstance = instanceField.get(null);

            // 获取shutdownHook实例
            Thread shutdownHook = (Thread) shutdownHookField.get(tempDirInstance);

            // 移除关闭钩子
            if (shutdownHook != null) {
                Runtime.getRuntime().removeShutdownHook(shutdownHook);
                System.out.println("已禁用OpenCV关闭钩子");
                shutdownHookDisabled = true;
            }
        } catch (Exception e) {
            System.err.println("禁用OpenCV关闭钩子时出错: " + e.getMessage());
        }
    }

    /**
     * 释放OpenCV资源
     */
    public static synchronized void release() {
        if (initialized) {
            try {
                // 如果已经禁用了关闭钩子，则手动清理临时文件
                if (shutdownHookDisabled) {
                    cleanupTempFiles();
                } else {
                    // 尝试通过反射调用nu.pattern.OpenCV的清理方法
                    try {
                        Class<?> opencvClass = Class.forName("nu.pattern.OpenCV");

                        // 尝试获取并调用releaseTemporaryDirectory方法
                        try {
                            java.lang.reflect.Method releaseMethod = opencvClass.getDeclaredMethod("releaseTemporaryDirectory");
                            releaseMethod.setAccessible(true);
                            releaseMethod.invoke(null);
                            System.out.println("OpenCV临时目录已释放");
                        } catch (NoSuchMethodException e) {
                            // 如果方法不存在，尝试获取TemporaryDirectory内部类
                            Class<?> tempDirClass = Class.forName("nu.pattern.OpenCV$TemporaryDirectory");
                            java.lang.reflect.Field instanceField = tempDirClass.getDeclaredField("INSTANCE");
                            instanceField.setAccessible(true);
                            Object tempDirInstance = instanceField.get(null);

                            // 调用delete方法
                            java.lang.reflect.Method deleteMethod = tempDirClass.getDeclaredMethod("delete");
                            deleteMethod.setAccessible(true);
                            deleteMethod.invoke(tempDirInstance);
                            System.out.println("OpenCV临时目录已手动删除");
                        }
                    } catch (Exception e) {
                        System.err.println("释放OpenCV资源时出错: " + e.getMessage());
                        // 如果反射方法失败，尝试手动清理
                        cleanupTempFiles();
                    }
                }

                // 重置初始化标志
                initialized = false;

            } catch (Exception e) {
                System.err.println("释放OpenCV资源时出错: " + e.getMessage());
            }
        }
    }

    /**
     * 手动清理OpenCV临时文件
     */
    private static void cleanupTempFiles() {
        try {
            // 获取系统临时目录
            String tempDir = System.getProperty("java.io.tmpdir");
            File dir = new File(tempDir);

            // 查找OpenCV创建的临时文件/目录
            File[] files = dir.listFiles((d, name) -> name.startsWith("opencv_"));

            if (files != null) {
                for (File file : files) {
                    deleteRecursively(file);
                    System.out.println("已删除OpenCV临时文件: " + file.getAbsolutePath());
                }
            }
        } catch (Exception e) {
            System.err.println("清理OpenCV临时文件时出错: " + e.getMessage());
        }
    }

    /**
     * 递归删除文件或目录
     */
    private static void deleteRecursively(File file) {
        if (file.isDirectory()) {
            File[] children = file.listFiles();
            if (children != null) {
                for (File child : children) {
                    deleteRecursively(child);
                }
            }
        }
        file.delete();
    }

    /**
     * 释放Mat对象
     * @param mats 要释放的Mat对象
     */
    public static void releaseMats(Mat... mats) {
        if (mats != null) {
            for (Mat mat : mats) {
                if (mat != null && !mat.empty()) {
                    mat.release();
                }
            }
        }
    }
}
