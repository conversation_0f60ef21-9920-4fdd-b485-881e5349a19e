package com.example.captain.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 创建店铺请求对象
 */
@Data
public class StoreCreateRequest {

    /**
     * 店铺名称
     */
    @NotBlank(message = "店铺名称不能为空")
    @Size(max = 100, message = "店铺名称长度不能超过100个字符")
    private String storeName;

    /**
     * 店铺链接
     */
    @Size(max = 500, message = "店铺链接长度不能超过500个字符")
    private String storeUrl;

    /**
     * 用户名
     */
    @NotBlank(message = "用户名不能为空")
    @Size(max = 50, message = "用户名长度不能超过50个字符")
    private String username;

    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 50, message = "密码长度必须在6-50个字符之间")
    private String password;

    /**
     * 北美站品牌
     */
    @Size(max = 100, message = "北美站品牌长度不能超过100个字符")
    private String northAmericaBrand;

    /**
     * 欧洲站品牌
     */
    @Size(max = 100, message = "欧洲站品牌长度不能超过100个字符")
    private String europeBrand;

    /**
     * EAN国家代码
     */
    @Size(max = 3, message = "EAN国家代码长度不能超过3个字符")
    private String eanCountryCode;

    /**
     * EAN厂商代码
     */
    @Size(max = 10, message = "EAN厂商代码长度不能超过10个字符")
    private String eanManufacturerCode;

    /**
     * EAN产品代码当前值
     */
    @Size(max = 20, message = "EAN产品代码当前值长度不能超过20个字符")
    private String eanProductCodeCurrent;
}
