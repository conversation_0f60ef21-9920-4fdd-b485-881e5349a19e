package com.example.worker.client;

import com.microsoft.playwright.*;
import com.microsoft.playwright.options.BoundingBox;
import com.microsoft.playwright.options.LoadState;
import com.microsoft.playwright.options.WaitForSelectorState;
import lombok.extern.slf4j.Slf4j;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

/**
 * 阿里巴巴1688客户端
 * 用于管理1688会话和浏览器操作，实现图片搜索功能
 */
@Slf4j
public class Alibaba1688Client implements AutoCloseable {

    // 1688图搜URL
    private static final String ALIBABA_SEARCH_URL = "https://s.1688.com/youyuan/index.htm";
    private static final String ALIBABA_LOGIN_URL = "https://login.1688.com/member/signin.htm";
    private static final String ALIBABA_HOME_URL = "https://www.1688.com/";

    // 会话文件路径常量
    private static final String SESSION_FILE_PATH = "src/main/resources/session/alibaba1688_session.json";

    // Edge浏览器路径
    private static final String EDGE_BROWSER_PATH = "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\msedge.exe";

    // 人类操作延迟范围（毫秒）
    private static final int MIN_DELAY = 500;  // 最小延迟
    private static final int MAX_DELAY = 2000; // 最大延迟

    private final Playwright playwright;
    private Browser browser;
    private BrowserContext context;
    private boolean headless;
    private final Random random = new Random();

    /**
     * 创建1688客户端
     *
     * @param headless 是否使用无头模式
     */
    public Alibaba1688Client(boolean headless) {
        this.headless = headless;
        log.info("正在初始化Alibaba1688Client...");

        // 确保会话文件目录存在
        try {
            Path resourceDir = Paths.get(SESSION_FILE_PATH).getParent();
            if (resourceDir != null && !Files.exists(resourceDir)) {
                Files.createDirectories(resourceDir);
                log.info("创建会话文件目录: {}", resourceDir);
            }
        } catch (Exception e) {
            log.warn("创建会话文件目录时出错: {}", e.getMessage());
        }

        // 创建Playwright实例
        playwright = Playwright.create();

        // 启动浏览器 - 使用本地安装的Edge浏览器
        browser = playwright.chromium().launch(
                new BrowserType.LaunchOptions()
                        .setHeadless(headless)
                        .setExecutablePath(Paths.get(EDGE_BROWSER_PATH))
                        .setArgs(List.of("--disable-extensions", "--disable-popup-blocking"))
        );

        // 创建浏览器上下文 - 初始化时不加载会话，等initSession调用时再加载
        context = browser.newContext(
            new Browser.NewContextOptions()
                .setViewportSize(1920, 1080)
                .setUserAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36")
        );

        log.info("Alibaba1688Client初始化完成");
    }

    /**
     * 初始化会话
     * 1. 加载会话文件并检查登录状态
     * 2. 如果未登录，等待用户手动登录
     * 3. 如果已登录，直接更新会话文件
     * 4. 保存会话文件
     *
     * @return 操作是否成功
     */
    public boolean initSession() {
        log.info("开始阿里巴巴1688会话管理...");
        Path sessionPath = Paths.get(SESSION_FILE_PATH);
        boolean sessionExists = Files.exists(sessionPath);

        try (java.util.Scanner scanner = new java.util.Scanner(System.in, java.nio.charset.StandardCharsets.UTF_8.name())) {
            // 如果是无头模式但需要登录，临时切换到有头模式
            boolean tempHeadless = false;

            // 创建上下文
            if (sessionExists) {
                log.info("已找到会话文件: {}, 正在加载...", SESSION_FILE_PATH);

                // 关闭现有上下文
                if (context != null) {
                    context.close();
                }

                // 创建带会话状态的浏览器上下文
                context = browser.newContext(
                    new Browser.NewContextOptions()
                        .setStorageStatePath(sessionPath)
                        .setViewportSize(1920, 1080)
                        .setUserAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36")
                );

                // 创建页面并导航到阿里巴巴首页
                Page page = context.newPage();
                log.info("正在打开阿里巴巴首页...");
                humanDelay(); // 添加人类操作延迟
                page.navigate(ALIBABA_HOME_URL);
                page.waitForLoadState();
                humanDelay(); // 添加人类操作延迟

                // 检查是否已登录
                boolean isLoggedIn = isLoggedInToAlibaba(page);

                if (isLoggedIn) {
                    // 已登录，直接更新会话文件
                    log.info("检测到登录状态，无需重新登录");
                    System.out.println("\n检测到您已登录阿里巴巴1688，正在更新会话文件...");
                    humanDelay(); // 添加人类操作延迟
                    saveSessionState(context, sessionPath);
                    humanDelay(); // 添加人类操作延迟
                    page.close();
                } else {
                    // 未登录，需要切换到有头模式
                    log.info("未检测到登录状态，需要登录");
                    humanDelay(); // 添加人类操作延迟
                    page.close();

                    // 关闭当前上下文
                    context.close();

                    // 如果当前是无头模式，需要重新创建浏览器
                    if (headless) {
                        tempHeadless = true;
                        log.info("切换到有头模式以便登录");
                        humanDelay(); // 添加人类操作延迟
                    }

                    // 创建新的上下文（有头模式）
                    handleLogin(scanner, sessionPath, tempHeadless);
                }
            } else {
                // 会话文件不存在，需要登录
                log.info("会话文件不存在，需要创建新会话");
                humanDelay(); // 添加人类操作延迟

                // 如果当前是无头模式，需要切换到有头模式
                if (headless) {
                    tempHeadless = true;
                    log.info("切换到有头模式以便登录");
                    humanDelay(); // 添加人类操作延迟
                }

                handleLogin(scanner, sessionPath, tempHeadless);
                humanDelay(); // 添加人类操作延迟
            }

            return true;
        } catch (Exception e) {
            log.error("管理会话状态时出错", e);
            return false;
        }
    }

    /**
     * 使用图片链接搜索商品
     *
     * @param imageUrl 图片URL
     * @return 搜索结果页面源码
     */
    public Map<String, Object> searchByImage(String imageUrl) {
        log.info("正在使用图片搜索1688商品: {}", imageUrl);

        // 在try块外声明页面变量，以便在catch块中访问
        Page page = null;
        Page searchResultPage = null;

        try {
            // 确保会话已初始化
            Path sessionPath = Paths.get(SESSION_FILE_PATH);
            if (!Files.exists(sessionPath)) {
                log.info("会话文件不存在，尝试初始化会话");
                boolean sessionInitialized = initSession();
                if (!sessionInitialized) {
                    log.error("初始化会话失败，无法继续");
                    return Map.of("error", "初始化会话失败");
                }
            }

            // 创建页面
            page = context.newPage();
            page.setDefaultTimeout(60000); // 60秒

            // 导航到1688图搜页面
            log.info("正在打开1688图搜页面: {}", ALIBABA_SEARCH_URL);
            page.navigate(ALIBABA_SEARCH_URL);
            page.waitForLoadState();
            log.info("页面标题: {}", page.title());

            // 检查是否是验证页面
            if (isVerificationPage(page)) {
                log.info("检测到初始页面为验证页面");
                boolean verificationSuccess = handleVerification(page);

                // 即使验证失败，也继续尝试执行
                if (!verificationSuccess) {
                    log.warn("验证可能未完成，但将继续尝试执行。人工操作者可能需要介入解决问题。");
                }

                // 重新获取页面
                page = context.newPage();
                page.setDefaultTimeout(60000); // 60秒
                page.navigate(ALIBABA_SEARCH_URL);
                page.waitForLoadState();
                log.info("验证后重新加载页面，标题: {}", page.title());

                // 再次检查是否仍然是验证页面
                if (isVerificationPage(page)) {
                    log.warn("页面仍然需要验证，将保持浏览器打开状态，请人工介入解决");
                    // 不抛出异常，继续尝试执行
                }
            }

            humanDelay();

            // 输入图片URL
            inputImageUrl(page, imageUrl);
            humanDelay();

            // 点击搜索按钮并等待新窗口
            searchResultPage = clickSearchAndWaitForResults(page);

            // 如果没有获取到搜索结果页面，抛出异常
            if (searchResultPage == null) {
                throw new RuntimeException("未能获取到搜索结果页面");
            }

            // 等待搜索结果页面加载
            waitForSearchResultsToLoad(searchResultPage);

            // 检查是否有商品容器
            checkForProductContainers(searchResultPage);

            // 提取商品信息并保存图片
            Map<String, Object> result = extractAndSaveProducts(searchResultPage);
            return result;

        } catch (Exception e) {
            log.error("使用图片搜索1688商品时出错: {}", e.getMessage(), e);

            // 保存异常现场
            Page errorPage = (searchResultPage != null) ? searchResultPage : page;
            saveDebugInfo(errorPage);

            return Map.of(
                "error", e.getMessage(),
                "error_type", e.getClass().getSimpleName(),
                "stack_trace", getStackTraceAsString(e)
            );
        } finally {
            // 确保页面被关闭
            closePageSafely(searchResultPage);
            closePageSafely(page);
        }
    }

    /**
     * 输入图片URL到搜索框
     *
     * @param page 页面对象
     * @param imageUrl 图片URL
     * @throws RuntimeException 如果输入失败
     */
    private void inputImageUrl(Page page, String imageUrl) {
        try {
            // 等待搜索输入框加载
            page.waitForSelector("#alisearch-input", new Page.WaitForSelectorOptions().setTimeout(10000));
            log.info("搜索输入框已加载");
            humanDelay();

            // 首先尝试剪贴板方法
            try {
                page.evaluate(String.format("() => navigator.clipboard.writeText('%s')", imageUrl.replace("'", "\\'")));
                page.locator("#alisearch-input").click();
                humanDelay();
                page.keyboard().press("Control+V");
                log.info("已使用剪贴板方法输入图片URL");
            } catch (Exception e) {
                log.warn("剪贴板方法失败，使用直接输入: {}", e.getMessage());

                // 备用方法：直接输入
                page.locator("#alisearch-input").click();
                humanDelay();

                // 分段输入长URL
                String[] segments = splitLongString(imageUrl, 20);
                for (String segment : segments) {
                    page.locator("#alisearch-input").pressSequentially(segment, new Locator.PressSequentiallyOptions().setDelay(50));
                    humanDelay();
                }
                log.info("已直接输入图片URL");
            }
        } catch (Exception e) {
            throw new RuntimeException("输入图片URL失败: " + e.getMessage(), e);
        }
    }

    /**
     * 点击搜索按钮并等待结果页面
     *
     * @param page 当前页面
     * @return 搜索结果页面
     * @throws RuntimeException 如果点击搜索或等待结果失败
     */
    private Page clickSearchAndWaitForResults(Page page) {
        try {
            // 等待搜索按钮可点击
            String searchButtonSelector = "#alisearch-from > div > div > div.input-button";
            page.waitForSelector(searchButtonSelector, new Page.WaitForSelectorOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(10000));
            log.info("图搜按钮已可见");
            humanDelay();

            // 点击搜索按钮并等待新窗口
            log.info("点击图搜按钮并等待新窗口");
            Page popup = page.waitForPopup(() -> page.locator(searchButtonSelector).click());

            // 等待新窗口加载
            popup.waitForLoadState();
            String url = popup.url();
            log.info("新窗口已加载，URL: {}", url);

            // 验证是否是搜索结果页面
            if (isSearchResultPage(url)) {
                log.info("确认为搜索结果页面");

                // 激活新页面
                popup.bringToFront();

                // 关闭原始页面
                page.close();

                return popup;
            } else {
                log.error("新窗口不是搜索结果页面: {}", url);
                popup.close();
                throw new RuntimeException("新窗口不是搜索结果页面: " + url);
            }
        } catch (TimeoutError e) {
            log.error("等待新窗口超时: {}", e.getMessage());
            saveScreenshot(page, "alibaba1688-search-timeout");
            throw new RuntimeException("点击搜索按钮后等待新窗口超时", e);
        } catch (Exception e) {
            log.error("点击搜索按钮或等待结果时出错: {}", e.getMessage());
            throw new RuntimeException("点击搜索按钮或等待结果失败: " + e.getMessage(), e);
        }
    }

    /**
     * 检查URL是否是搜索结果页面
     */
    private boolean isSearchResultPage(String url) {
        return url.contains("tab=imageSearch") ||
               url.contains("imageId=") ||
               url.contains("youyuan") ||
               (url.contains("1688.com") && !url.equals(ALIBABA_SEARCH_URL));
    }

    /**
     * 等待搜索结果页面完全加载
     */
    private void waitForSearchResultsToLoad(Page page) {
        try {
            log.info("等待搜索结果页面加载...");

            // 等待DOM内容加载
            page.waitForLoadState(LoadState.DOMCONTENTLOADED);
            log.info("DOM内容已加载");

            // 检查是否是验证页面
            if (isVerificationPage(page)) {
                log.info("搜索结果页面需要验证");
                boolean verificationSuccess = handleVerification(page);

                // 即使验证失败，也继续尝试执行
                if (!verificationSuccess) {
                    log.warn("验证可能未完成，但将继续尝试执行。人工操作者可能需要介入解决问题。");
                    // 不抛出异常，继续尝试执行
                }
            }

            // 等待网络请求完成
            page.waitForLoadState(LoadState.NETWORKIDLE, new Page.WaitForLoadStateOptions().setTimeout(30000));
            log.info("网络请求已完成");

            humanDelay();

            // 验证URL包含搜索参数
            String pageUrl = page.url();
            if (!isSearchResultPage(pageUrl)) {
                log.warn("页面URL不包含搜索结果参数: {}", pageUrl);
                throw new RuntimeException("页面不是有效的搜索结果页面: " + pageUrl);
            }

            log.info("搜索结果页面已加载: {}", pageUrl);
        } catch (Exception e) {
            throw new RuntimeException("等待搜索结果页面加载失败: " + e.getMessage(), e);
        }
    }

    /**
     * 检查页面是否包含商品容器
     */
    private void checkForProductContainers(Page page) {
        try {
            log.info("检查商品容器元素...");

            // 等待商品容器元素
            page.waitForSelector("div.space-offer-card-box", new Page.WaitForSelectorOptions().setTimeout(5000));

            // 获取商品容器数量
            int cardCount = (int) page.evaluate("() => document.querySelectorAll('div.space-offer-card-box').length");
            log.info("找到 {} 个商品容器", cardCount);

            if (cardCount == 0) {
                throw new RuntimeException("未找到任何商品容器");
            }
        } catch (Exception e) {
            log.warn("检查商品容器时出错: {}", e.getMessage());
            saveScreenshot(page, "alibaba1688-no-products");
            throw new RuntimeException("未找到商品容器: " + e.getMessage(), e);
        }
    }

    /**
     * 安全关闭页面
     */
    private void closePageSafely(Page page) {
        if (page != null) {
            try {
                page.close();
            } catch (Exception e) {
                log.warn("关闭页面时出错: {}", e.getMessage());
            }
        }
    }

    /**
     * 获取异常堆栈信息为字符串
     */
    private String getStackTraceAsString(Throwable throwable) {
        if (throwable == null) return "";

        try (java.io.StringWriter sw = new java.io.StringWriter();
             java.io.PrintWriter pw = new java.io.PrintWriter(sw)) {
            throwable.printStackTrace(pw);
            return sw.toString();
        } catch (Exception e) {
            return "无法获取堆栈信息: " + e.getMessage();
        }
    }

    /**
     * 模拟人类操作的随机延迟
     * 在操作之间添加随机延迟，使自动化操作更自然
     */
    private void humanDelay() {
        try {
            int delay = MIN_DELAY + random.nextInt(MAX_DELAY - MIN_DELAY);
            log.debug("添加人类操作延迟: {}毫秒", delay);
            Thread.sleep(delay);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("人类操作延迟被中断", e);
        }
    }

    /**
     * 将长字符串分割成多个小段
     * 用于模拟人类分段输入长文本
     *
     * @param input 输入字符串
     * @param segmentLength 每段长度
     * @return 分割后的字符串数组
     */
    private String[] splitLongString(String input, int segmentLength) {
        if (input == null || input.isEmpty() || segmentLength <= 0) {
            return new String[]{input};
        }

        int totalSegments = (input.length() + segmentLength - 1) / segmentLength;
        String[] segments = new String[totalSegments];

        for (int i = 0; i < totalSegments; i++) {
            int startIndex = i * segmentLength;
            int endIndex = Math.min(startIndex + segmentLength, input.length());
            segments[i] = input.substring(startIndex, endIndex);
        }

        return segments;
    }

    /**
     * 保存页面截图
     *
     * @param page 页面对象
     * @param prefix 文件名前缀
     * @return 截图文件路径
     */
    private Path saveScreenshot(Page page, String prefix) {
        try {
            // 确保debug目录存在
            Path debugDir = Paths.get("debug");
            if (!Files.exists(debugDir)) {
                Files.createDirectories(debugDir);
            }

            // 生成带时间戳的文件名
            String timestamp = String.valueOf(System.currentTimeMillis());
            Path screenshotPath = debugDir.resolve(prefix + "-" + timestamp + ".png");

            // 保存截图
            page.screenshot(new Page.ScreenshotOptions().setPath(screenshotPath).setFullPage(true));

            return screenshotPath;
        } catch (Exception e) {
            log.error("保存页面截图时出错", e);
            return null;
        }
    }

    /**
     * 保存页面调试信息到debug目录
     * 包括页面截图和页面源码
     *
     * @param page Playwright页面对象
     */
    private void saveDebugInfo(Page page) {
        try {
            // 确保debug目录存在
            Path debugDir = Paths.get("debug");
            if (!Files.exists(debugDir)) {
                Files.createDirectories(debugDir);
                log.info("创建debug目录: {}", debugDir);
            }

            // 生成带时间戳的文件名
            String timestamp = String.valueOf(System.currentTimeMillis());

            // 保存页面截图
            if (page != null) {
                Path screenshotPath = debugDir.resolve("alibaba1688-error-" + timestamp + ".png");
                page.screenshot(new Page.ScreenshotOptions().setPath(screenshotPath));
                log.info("错误页面截图已保存到: {}", screenshotPath);

                // 保存页面源码
                Path htmlPath = debugDir.resolve("alibaba1688-error-" + timestamp + ".html");
                String pageContent = page.content();
                Files.write(htmlPath, pageContent.getBytes());
                log.info("错误页面源码已保存到: {}", htmlPath);
            } else {
                log.warn("无法保存页面截图和源码，页面对象为null");
            }
        } catch (Exception e) {
            log.error("保存调试信息时出错", e);
        }
    }


    /**
     * 提取商品信息并保存图片
     *
     * @param page Playwright页面对象
     * @return 包含商品信息和保存图片路径的结果Map
     */
    private Map<String, Object> extractAndSaveProducts(Page page) {
        log.info("开始提取商品信息并保存图片");

        try {
            // 检查是否是验证页面
            if (isVerificationPage(page)) {
                log.info("提取商品信息前需要验证");
                boolean verificationSuccess = handleVerification(page);

                // 即使验证失败，也继续尝试执行
                if (!verificationSuccess) {
                    log.warn("验证可能未完成，但将继续尝试提取商品信息。人工操作者可能需要介入解决问题。");
                    // 不抛出异常，继续尝试执行
                }
            }
            // 提取商品图片和链接 - 使用更健壮的脚本，基于截图中的实际页面结构
            String extractProductsScript =
                "(() => {\n" +
                "    const products = [];\n" +
                "    \n" +
                "    // 获取所有商品卡片\n" +
                "    const cards = document.querySelectorAll('div.space-offer-card-box');\n" +
                "    console.log(`找到 ${cards.length} 个商品卡片`);\n" +
                "    \n" +
                "    // 遍历商品卡片\n" +
                "    for (const card of cards) {\n" +
                "        try {\n" +
                "            // 提取商品信息\n" +
                "            const imgUrl = card.querySelector('div.img')?.style?.backgroundImage?.replace(/^url\\(['\"](.*)['\"]\\)$/, '$1') || '';\n" +
                "            const link = card.querySelector('div.mojar-element-image a')?.href || card.querySelector('a')?.href || '';\n" +
                "            const title = card.querySelector('div.mojar-element-title')?.innerText || '';\n" +
                "            const price = card.querySelector('div.showPricec')?.innerText || '';\n" +
                "            \n" +
                "            // 添加到结果中\n" +
                "            if (imgUrl || link || title || price) {\n" +
                "                products.push({ imgUrl, link, title, price });\n" +
                "            }\n" +
                "        } catch (e) {\n" +
                "            console.error('处理商品卡片时出错:', e);\n" +
                "        }\n" +
                "    }\n" +
                "    \n" +
                "    return {\n" +
                "        products,\n" +
                "        productCount: products.length,\n" +
                "        pageInfo: {\n" +
                "            url: window.location.href,\n" +
                "            title: document.title\n" +
                "        }\n" +
                "    };\n" +
                "})()";

            // 简单的页面结构调试信息
            String debugScript = "() => {\n" +
                "  return {\n" +
                "    cardCount: document.querySelectorAll('div.space-offer-card-box').length,\n" +
                "    url: window.location.href,\n" +
                "    title: document.title\n" +
                "  };\n" +
                "}";

            Object pageStructureDebug = page.evaluate(debugScript);
            log.info("页面结构详细调试信息: {}", pageStructureDebug);

            // 执行脚本获取商品信息
            @SuppressWarnings("unchecked")
            Map<String, Object> extractResult = (Map<String, Object>) page.evaluate(extractProductsScript);

            // 从结果中提取产品列表
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> products = (List<Map<String, Object>>) extractResult.get("products");

            log.info("已提取商品信息，共 {} 个商品", products.size());

            // 保存商品图片（背景图片）
            List<String> savedProductImages = saveProductImages(products, "images/alibaba1688/products/" + System.currentTimeMillis(), page);
            log.info("已保存 {} 张商品图片", savedProductImages.size());

            // 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("title", page.title());
            result.put("url", page.url());
            result.put("products", products);
            result.put("product_count", products.size());
            result.put("saved_product_images", savedProductImages);

            return result;
        } catch (Exception e) {
            log.error("提取商品信息并保存图片时出错", e);
            return Map.of("error", e.getMessage());
        }
    }

    /**
     * 保存商品图片（从product.imgUrl中提取的背景图片URL）
     *
     * @param products 商品列表
     * @param folderPath 保存图片的文件夹路径
     * @param page Playwright页面对象，用于下载图片
     * @return 保存的图片路径列表
     */
    public List<String> saveProductImages(List<Map<String, Object>> products, String folderPath, Page page) {
        log.info("开始保存商品图片到文件夹: {}", folderPath);
        List<String> savedImagePaths = new ArrayList<>();

        try {
            // 确保目标文件夹存在
            Path folder = Paths.get(folderPath);
            if (!Files.exists(folder)) {
                Files.createDirectories(folder);
                log.info("创建商品图片保存目录: {}", folder);
            }

            // 提取所有商品图片URL
            List<String> imageUrls = new ArrayList<>();
            for (Map<String, Object> product : products) {
                if (product.containsKey("imgUrl") && product.get("imgUrl") != null) {
                    String imgUrl = product.get("imgUrl").toString();
                    if (!imgUrl.isEmpty()) {
                        imageUrls.add(imgUrl);
                    }
                }
            }

            log.info("从商品中提取到 {} 个图片URL", imageUrls.size());

            // 下载并保存每个图片
            int count = 0;
            for (String imageUrl : imageUrls) {
                try {
                    // 生成唯一文件名
                    String fileName = "product_" + System.currentTimeMillis() + "_" + count + getFileExtension(imageUrl);
                    Path imagePath = folder.resolve(fileName);

                    // 使用Playwright下载图片 - 使用二进制方式
                    byte[] imageBytes = page.request().fetch(imageUrl).body();

                    // 保存图片
                    if (imageBytes != null) {
                        Files.write(imagePath, imageBytes);
                        savedImagePaths.add(imagePath.toString());
                        log.info("已保存商品图片 {}/{}: {}", count + 1, imageUrls.size(), imagePath);
                    } else {
                        log.warn("无法下载商品图片: {}", imageUrl);
                    }

                    count++;

                    // 添加延迟，避免请求过快
                    humanDelay();
                } catch (Exception e) {
                    log.error("保存商品图片时出错: {}", imageUrl, e);
                }
            }

            log.info("成功保存 {} 个商品图片到 {}", savedImagePaths.size(), folderPath);
        } catch (Exception e) {
            log.error("保存商品图片时出错", e);
        }

        return savedImagePaths;
    }

    /**
     * 获取URL的文件扩展名
     *
     * @param url 图片URL
     * @return 文件扩展名（包括点号）
     */
    private String getFileExtension(String url) {
        try {
            // 移除URL参数
            String cleanUrl = url.split("\\?")[0];

            // 获取路径部分
            String path = new java.net.URL(cleanUrl).getPath();

            // 提取扩展名
            int lastDotPos = path.lastIndexOf('.');
            if (lastDotPos >= 0) {
                String extension = path.substring(lastDotPos);
                // 验证扩展名是否为常见图片格式
                if (extension.matches("\\.(jpg|jpeg|png|gif|bmp|webp|svg)")) {
                    return extension;
                }
            }
        } catch (Exception e) {
            log.debug("获取文件扩展名时出错: {}", url, e);
        }

        // 默认返回.jpg
        return ".jpg";
    }

    /**
     * 处理登录流程
     *
     * @param scanner 控制台输入扫描器
     * @param sessionPath 会话文件路径
     * @param tempHeadless 是否是临时有头模式
     */
    private void handleLogin(java.util.Scanner scanner, Path sessionPath, boolean tempHeadless) {
        try {
            // 如果需要临时切换到有头模式
            Browser loginBrowser = tempHeadless ?
                playwright.chromium().launch(
                    new BrowserType.LaunchOptions()
                        .setHeadless(false)
                        .setExecutablePath(Paths.get(EDGE_BROWSER_PATH))
                        .setArgs(List.of("--disable-extensions", "--disable-popup-blocking"))
                ) :
                browser;

            try {
                // 创建新的上下文
                BrowserContext loginContext = loginBrowser.newContext();

                // 创建页面并导航到阿里巴巴登录页面
                Page page = loginContext.newPage();
                log.info("正在打开阿里巴巴登录页面...");
                humanDelay(); // 添加人类操作延迟
                page.navigate(ALIBABA_LOGIN_URL);
                page.waitForLoadState();
                humanDelay(); // 添加人类操作延迟

                // 提示用户登录
                System.out.println("\n请在浏览器中登录阿里巴巴1688...");
                System.out.println("登录后，在控制台输入'done'保存会话。");

                waitForUserInput(scanner);
                humanDelay(); // 添加人类操作延迟
                saveSessionState(loginContext, sessionPath);
                humanDelay(); // 添加人类操作延迟

                // 关闭登录上下文
                loginContext.close();
            } finally {
                // 如果使用了临时浏览器，关闭它
                if (tempHeadless) {
                    loginBrowser.close();
                }
            }

            // 重新加载会话
            if (context != null) {
                context.close();
            }

            humanDelay(); // 添加人类操作延迟

            context = browser.newContext(
                new Browser.NewContextOptions()
                    .setStorageStatePath(sessionPath)
                    .setViewportSize(1920, 1080)
                    .setUserAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36")
            );

            humanDelay(); // 添加人类操作延迟

        } catch (Exception e) {
            log.error("处理登录时出错", e);
        }
    }

    /**
     * 等待用户在控制台输入完成
     *
     * @param scanner 控制台输入扫描器
     * @return 用户输入
     */
    private String waitForUserInput(java.util.Scanner scanner) {
        String input;
        do {
            input = scanner.nextLine().trim().toLowerCase();
        } while (!input.equals("done"));
        return input;
    }

    /**
     * 保存会话状态到文件
     *
     * @param context 浏览器上下文
     * @param sessionPath 会话文件路径
     */
    private void saveSessionState(BrowserContext context, Path sessionPath) {
        try {
            // 确保目录存在
            Path resourceDir = sessionPath.getParent();
            if (!Files.exists(resourceDir)) {
                Files.createDirectories(resourceDir);
            }

            humanDelay(); // 添加人类操作延迟

            // 保存会话状态
            context.storageState(new BrowserContext.StorageStateOptions().setPath(sessionPath));
            log.info("会话状态已保存到: {}", sessionPath);
            System.out.println("会话文件已保存！");

            humanDelay(); // 添加人类操作延迟
        } catch (Exception e) {
            log.error("保存会话状态时出错", e);
            System.out.println("保存会话文件失败: " + e.getMessage());
        }
    }

    /**
     * 检查阿里巴巴1688登录状态
     *
     * @param page Playwright页面对象
     * @return 是否已登录
     */
    private boolean isLoggedInToAlibaba(Page page) {
        try {
            // 检查是否有登录状态元素
            // 阿里巴巴1688登录后通常会显示用户名或会员信息
            ElementHandle memberElement = page.querySelector(".sm-member-info");
            if (memberElement != null) {
                log.info("检测到会员信息元素，已登录");
                return true;
            }

            // 检查是否有登录按钮
            ElementHandle loginButton = page.querySelector(".sm-signin-info");
            if (loginButton != null) {
                String buttonText = loginButton.textContent();
                log.info("检测到登录按钮: {}", buttonText);
                return false;
            }

            // 检查顶部导航栏中的用户信息
            ElementHandle userInfo = page.querySelector(".sm-user-info");
            if (userInfo != null) {
                log.info("检测到用户信息元素，已登录");
                return true;
            }

            // 如果以上检查都未确定状态，尝试更通用的方法
            String pageContent = page.content().toLowerCase();
            boolean hasLoginLink = pageContent.contains("请登录") || pageContent.contains("免费注册");
            boolean hasUserInfo = pageContent.contains("会员中心") || pageContent.contains("我的阿里");

            log.info("页面内容分析 - 包含登录链接: {}, 包含用户信息: {}", hasLoginLink, hasUserInfo);
            return !hasLoginLink && hasUserInfo;
        } catch (Exception e) {
            log.warn("检查登录状态时出错: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 检测是否是验证页面
     *
     * @param page Playwright页面对象
     * @return 是否是验证页面
     */
    private boolean isVerificationPage(Page page) {
        try {
            if (page == null) return false;

            // 检查是否存在验证表单
            boolean hasVerifyForm = (boolean) page.evaluate("() => {" +
                "return document.querySelector('#nc-verify-form') !== null;" +
            "}");

            if (hasVerifyForm) {
                log.info("检测到验证页面，需要人工介入");
                return true;
            }

            return false;
        } catch (Exception e) {
            log.error("检测验证页面时出错", e);
            return false;
        }
    }

    /**
     * 处理验证页面，切换到有头浏览器并等待人工介入
     *
     * @param page Playwright页面对象
     * @return 验证是否成功
     */
    private boolean handleVerification(Page page) {
        try {
            log.info("切换到有头浏览器模式，等待人工介入完成验证");

            // 保存当前页面URL
            String currentUrl = page.url();
            log.info("当前验证页面URL: {}", currentUrl);

            // 关闭当前无头浏览器
            if (page != null) {
                page.close();
            }
            if (context != null) {
                context.close();
            }

            // 创建有头浏览器 - 使用本地安装的Edge浏览器
            BrowserType.LaunchOptions launchOptions = new BrowserType.LaunchOptions()
                .setHeadless(false)
                .setSlowMo(50)
                .setExecutablePath(Paths.get(EDGE_BROWSER_PATH))
                .setArgs(List.of("--disable-extensions", "--disable-popup-blocking"));

            browser = playwright.chromium().launch(launchOptions);

            // 创建新的上下文
            Browser.NewContextOptions contextOptions = new Browser.NewContextOptions()
                .setViewportSize(1366, 768)
                .setUserAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36");

            context = browser.newContext(contextOptions);

            // 创建新页面并导航到之前的URL
            Page newPage = context.newPage();
            newPage.navigate(currentUrl);

            // 等待验证表单出现
            try {
                newPage.waitForSelector("#nc-verify-form", new Page.WaitForSelectorOptions().setTimeout(10000));
                log.info("验证表单已加载");
            } catch (Exception e) {
                log.warn("等待验证表单出现时出错: {}", e.getMessage());
                // 即使找不到验证表单，也继续执行
            }

            // 显示提示信息，更详细的指导
            newPage.evaluate("() => {" +
                "const div = document.createElement('div');" +
                "div.style.position = 'fixed';" +
                "div.style.top = '10px';" +
                "div.style.left = '10px';" +
                "div.style.padding = '15px';" +
                "div.style.backgroundColor = 'rgba(255, 0, 0, 0.9)';" +
                "div.style.color = 'white';" +
                "div.style.zIndex = '9999';" +
                "div.style.fontSize = '16px';" +
                "div.style.borderRadius = '5px';" +
                "div.style.boxShadow = '0 4px 8px rgba(0,0,0,0.2)';" +
                "div.style.maxWidth = '400px';" +
                "div.style.lineHeight = '1.5';" +
                "div.innerHTML = '<b>需要人工验证</b><br>'" +
                "+ '1. 请完成页面上的验证操作<br>'" +
                "+ '2. 如果验证成功，页面会自动跳转<br>'" +
                "+ '3. 如果验证失败，请重试或尝试其他方法<br>'" +
                "+ '4. 完成验证后，点击此提示框关闭<br><br>'" +
                "+ '<small>即使验证未完成，系统也会尝试继续执行</small>';" +
                "div.style.cursor = 'pointer';" +
                "div.onclick = function() {" +
                "  this.remove();" +
                "  const successDiv = document.createElement('div');" +
                "  successDiv.style.position = 'fixed';" +
                "  successDiv.style.top = '10px';" +
                "  successDiv.style.left = '10px';" +
                "  successDiv.style.padding = '10px';" +
                "  successDiv.style.backgroundColor = 'rgba(0, 128, 0, 0.9)';" +
                "  successDiv.style.color = 'white';" +
                "  successDiv.style.zIndex = '9999';" +
                "  successDiv.style.fontSize = '16px';" +
                "  successDiv.style.borderRadius = '5px';" +
                "  successDiv.textContent = '提示已关闭，系统将继续执行';" +
                "  successDiv.style.opacity = '1';" +
                "  document.body.appendChild(successDiv);" +
                "  setTimeout(() => {" +
                "    let op = 1;" +
                "    const timer = setInterval(() => {" +
                "      if (op <= 0.1) {" +
                "        clearInterval(timer);" +
                "        successDiv.remove();" +
                "      }" +
                "      successDiv.style.opacity = op;" +
                "      op -= 0.1;" +
                "    }, 100);" +
                "  }, 2000);" +
                "};" +
                "document.body.appendChild(div);" +
            "}");

            log.info("已显示人工验证提示，等待用户操作...");

            // 等待用户完成验证（用户点击提示框关闭）
            newPage.waitForFunction("() => !document.querySelector('div[style*=\"position: fixed\"][style*=\"backgroundColor: red\"]')",
                new Page.WaitForFunctionOptions().setTimeout(300000)); // 5分钟超时

            log.info("用户已关闭提示框，等待页面加载完成");

            // 等待页面加载完成
            newPage.waitForLoadState(LoadState.NETWORKIDLE);

            // 检查是否还在验证页面
            boolean stillInVerification = isVerificationPage(newPage);
            if (stillInVerification) {
                log.warn("验证可能未完成，但将继续尝试执行");

                // 添加额外的提示，告知用户可以继续手动操作
                newPage.evaluate("() => {" +
                    "const warningDiv = document.createElement('div');" +
                    "warningDiv.style.position = 'fixed';" +
                    "warningDiv.style.bottom = '10px';" +
                    "warningDiv.style.right = '10px';" +
                    "warningDiv.style.padding = '10px';" +
                    "warningDiv.style.backgroundColor = 'rgba(255, 165, 0, 0.9)';" +
                    "warningDiv.style.color = 'white';" +
                    "warningDiv.style.zIndex = '9999';" +
                    "warningDiv.style.fontSize = '14px';" +
                    "warningDiv.style.borderRadius = '5px';" +
                    "warningDiv.innerHTML = '<b>提示</b>: 系统将继续尝试执行，但您可以继续手动操作以完成验证';" +
                    "document.body.appendChild(warningDiv);" +
                    "setTimeout(() => warningDiv.remove(), 10000);" +
                "});");

                // 即使验证未完成，也返回true以继续执行
                return true;
            }

            log.info("验证已完成，继续执行");
            return true;
        } catch (Exception e) {
            log.error("处理验证页面时出错", e);
            return false;
        }
    }

    /**
     * 打开1688首页并保持浏览器窗口打开
     *
     * @return 打开的页面对象
     */
    public Page openHomePage() {
        log.info("正在打开1688首页并保持窗口打开...");
        try {
            // 确保会话已初始化
            Path sessionPath = Paths.get(SESSION_FILE_PATH);
            if (!Files.exists(sessionPath)) {
                log.info("会话文件不存在，尝试初始化会话");
                boolean sessionInitialized = initSession();
                if (!sessionInitialized) {
                    log.error("初始化会话失败，无法继续");
                    return null;
                }
            }

            // 创建页面
            Page page = context.newPage();
            page.setDefaultTimeout(60000); // 60秒

            // 导航到1688首页
            log.info("正在打开1688首页: {}", ALIBABA_HOME_URL);
            humanDelay(); // 添加人类操作延迟
            page.navigate(ALIBABA_HOME_URL);
            page.waitForLoadState();
            humanDelay(); // 添加人类操作延迟

            log.info("1688首页已打开，标题: {}", page.title());

            return page;
        } catch (Exception e) {
            log.error("打开1688首页时出错: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 使用1688插件搜图功能
     * 打开图片链接，悬停在图片上，点击搜索图标进行搜索
     *
     * @param imageUrl 图片URL
     */
    public void searchByImagePlugin(String imageUrl) {
        log.info("正在使用1688插件搜图功能，图片URL: {}", imageUrl);

        // 在try块外声明页面变量，以便在catch块中访问
        Page imagePage = null;
        Page searchResultPage = null;

        try {           

            // 创建页面
            imagePage = context.newPage();
            imagePage.setDefaultTimeout(60000); // 60秒

            // 导航到图片URL
            log.info("正在打开图片URL: {}", imageUrl);
            imagePage.navigate(imageUrl);
            imagePage.waitForLoadState();
            log.info("图片页面已加载，标题: {}", imagePage.title());

            // 等待图片加载完成
            imagePage.waitForSelector("img", new Page.WaitForSelectorOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(10000));
            log.info("图片已加载");
            humanDelay();

            // 获取主图片元素
            ElementHandle mainImage = imagePage.querySelector("img");
            if (mainImage == null) {
                throw new RuntimeException("未找到图片元素");
            }

            // 获取图片位置和尺寸
            BoundingBox boundingBox = mainImage.boundingBox();
            if (boundingBox == null) {
                throw new RuntimeException("无法获取图片位置信息");
            }

            double x = boundingBox.x;
            double y = boundingBox.y;
            double width = boundingBox.width;
            double height = boundingBox.height;

            // 计算图片中心位置
            double centerX = x + width / 2;
            double centerY = y + height / 2;

            // 悬停在图片上
            log.info("悬停在图片上");
            imagePage.mouse().move(centerX, centerY);
            humanDelay();

            // 等待搜索图标出现
            log.info("等待搜索图标出现");
            imagePage.waitForSelector("img[src*='data:image/svg+xml']",
                new Page.WaitForSelectorOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(5000));

            // 查找搜索图标
            ElementHandle searchIcon = imagePage.querySelector("img[src*='data:image/svg+xml']");
            if (searchIcon == null) {
                log.warn("未找到搜索图标，尝试使用更通用的选择器");
                // 尝试使用更通用的选择器
                searchIcon = imagePage.querySelector("img[src*='svg']");

                if (searchIcon == null) {
                    throw new RuntimeException("未找到搜索图标");
                }
            }

            // 获取搜索图标的位置
            BoundingBox iconBox = searchIcon.boundingBox();
            if (iconBox == null) {
                throw new RuntimeException("无法获取搜索图标位置信息");
            }

            double iconX = iconBox.x + iconBox.width / 2;
            double iconY = iconBox.y + iconBox.height / 2;

            // 点击搜索图标并等待新窗口
            log.info("点击搜索图标并等待新窗口");

            // 创建一个单独的方法来处理点击操作，避免在lambda中使用非final变量
            final Page finalImagePage = imagePage;
            final double finalIconX = iconX;
            final double finalIconY = iconY;

            Page popup = finalImagePage.waitForPopup(() -> {
                finalImagePage.mouse().click(finalIconX, finalIconY);
            });

            // 等待新窗口加载
            popup.waitForLoadState();
            searchResultPage = popup;
            Thread.sleep(Long.MAX_VALUE);

            
        } catch (Exception e) {
            log.error("使用1688插件搜图功能时出错: {}", e.getMessage(), e);

        } finally {
            // 确保页面被关闭
            closePageSafely(searchResultPage);
            closePageSafely(imagePage);
        }
    }

    /**
     * 关闭资源
     */
    @Override
    public void close() {
        try {
            if (context != null) {
                context.close();
            }
            if (browser != null) {
                browser.close();
            }
            if (playwright != null) {
                playwright.close();
            }
            log.info("Alibaba1688Client已关闭");
        } catch (Exception e) {
            log.error("关闭Alibaba1688Client时出错", e);
        }
    }
}
