package com.example.captain.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.captain.dto.SupplyDTO;
import com.example.captain.dto.SupplyQueryParam;
import com.example.captain.entity.Supply;
import com.example.captain.mapper.SupplyMapper;
import com.example.captain.service.SupplyService;
import com.example.common.exception.BusinessException;
import com.example.common.model.ErrorCodeEnum;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 1688货源服务实现类
 */
@Service
@Slf4j
public class SupplyServiceImpl implements SupplyService {

    private final SupplyMapper supplyMapper;
    private final ObjectMapper objectMapper;

    @Autowired
    public SupplyServiceImpl(SupplyMapper supplyMapper, ObjectMapper objectMapper) {
        this.supplyMapper = supplyMapper;
        this.objectMapper = objectMapper;
    }

    @Override
    public int saveSupplies(Long eventId, String amazonAsin, List<Map<String, Object>> supplyInfoList) {
        if (eventId == null || amazonAsin == null || supplyInfoList == null || supplyInfoList.isEmpty()) {
            log.warn("保存1688货源数据参数无效: eventId={}, amazonAsin={}, supplyInfoList={}",
                    eventId, amazonAsin, supplyInfoList == null ? "null" : supplyInfoList.size());
            return 0;
        }

        log.info("开始保存1688货源数据: eventId={}, amazonAsin={}, 数量={}",
                eventId, amazonAsin, supplyInfoList.size());

        int count = 0;
        for (Map<String, Object> supplyInfo : supplyInfoList) {
            try {
                // 获取必要字段
                String objectId = String.valueOf(supplyInfo.get("objectId"));

                // 检查是否已存在相同的记录
                LambdaQueryWrapper<Supply> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(Supply::getEventId, eventId)
                        .eq(Supply::getAmazonAsin, amazonAsin)
                        .eq(Supply::getObjectId, objectId);

                Supply existingSupply = supplyMapper.selectOne(queryWrapper);
                if (existingSupply != null) {
                    log.info("1688货源已存在，将更新: eventId={}, amazonAsin={}, objectId={}",
                            eventId, amazonAsin, objectId);

                    // 更新现有记录
                    updateSupply(existingSupply, supplyInfo);
                    supplyMapper.updateById(existingSupply);
                } else {
                    // 创建新记录
                    Supply supply = createSupply(eventId, amazonAsin, supplyInfo);
                    supplyMapper.insert(supply);
                }

                count++;
                log.info("成功保存1688货源: eventId={}, amazonAsin={}, objectId={}",
                        eventId, amazonAsin, objectId);
            } catch (Exception e) {
                log.error("保存1688货源时出错: {}", e.getMessage(), e);
            }
        }

        return count;
    }

    /**
     * 创建新的1688货源记录
     */
    private Supply createSupply(Long eventId, String amazonAsin, Map<String, Object> supplyInfo) throws Exception {
        Supply supply = new Supply();
        supply.setEventId(eventId);
        supply.setAmazonAsin(amazonAsin);

        // 设置基本字段
        supply.setObjectId(String.valueOf(supplyInfo.get("objectId")));
        supply.setTitle(String.valueOf(supplyInfo.get("title")));
        supply.setPrice(String.valueOf(supplyInfo.get("price")));
        supply.setDetailUrl(String.valueOf(supplyInfo.get("detailUrl")));
        supply.setImageUrl(String.valueOf(supplyInfo.get("imageUrl")));
        supply.setLocalImagePath(String.valueOf(supplyInfo.get("localImagePath")));

        // 设置相似度
        if (supplyInfo.containsKey("similarity")) {
            try {
                supply.setSimilarity(new BigDecimal(String.valueOf(supplyInfo.get("similarity"))));
            } catch (Exception e) {
                log.warn("转换相似度失败: {}", e.getMessage());
            }
        }

        // 设置商品属性和包装信息
        if (supplyInfo.containsKey("productAttributes")) {
            supply.setProductAttributes(String.valueOf(supplyInfo.get("productAttributes")));
        }
        if (supplyInfo.containsKey("packagingInfo")) {
            supply.setPackagingInfo(String.valueOf(supplyInfo.get("packagingInfo")));
        }

        // 设置额外字段
        if (supplyInfo.containsKey("tpYear")) {
            supply.setTpYear(String.valueOf(supplyInfo.get("tpYear")));
        }
        if (supplyInfo.containsKey("companyName")) {
            supply.setCompanyName(String.valueOf(supplyInfo.get("companyName")));
        }
        if (supplyInfo.containsKey("tpCreditUrl")) {
            supply.setTpCreditUrl(String.valueOf(supplyInfo.get("tpCreditUrl")));
        }
        if (supplyInfo.containsKey("sales360Fuzzify")) {
            supply.setSales360Fuzzify(String.valueOf(supplyInfo.get("sales360Fuzzify")));
        }
        if (supplyInfo.containsKey("quantityBegin")) {
            supply.setQuantityBegin(String.valueOf(supplyInfo.get("quantityBegin")));
        }

        // 保存原始数据
        supply.setRawData(objectMapper.writeValueAsString(supplyInfo));

        // 设置时间
        LocalDateTime now = LocalDateTime.now();
        supply.setCreatedAt(now);
        supply.setUpdatedAt(now);

        return supply;
    }

    /**
     * 更新现有的1688货源记录
     */
    private void updateSupply(Supply supply, Map<String, Object> supplyInfo) throws Exception {
        // 更新基本字段
        supply.setTitle(String.valueOf(supplyInfo.get("title")));
        supply.setPrice(String.valueOf(supplyInfo.get("price")));
        supply.setDetailUrl(String.valueOf(supplyInfo.get("detailUrl")));
        supply.setImageUrl(String.valueOf(supplyInfo.get("imageUrl")));
        supply.setLocalImagePath(String.valueOf(supplyInfo.get("localImagePath")));

        // 更新相似度
        if (supplyInfo.containsKey("similarity")) {
            try {
                supply.setSimilarity(new BigDecimal(String.valueOf(supplyInfo.get("similarity"))));
            } catch (Exception e) {
                log.warn("转换相似度失败: {}", e.getMessage());
            }
        }

        // 更新商品属性和包装信息
        if (supplyInfo.containsKey("productAttributes")) {
            supply.setProductAttributes(String.valueOf(supplyInfo.get("productAttributes")));
        }
        if (supplyInfo.containsKey("packagingInfo")) {
            supply.setPackagingInfo(String.valueOf(supplyInfo.get("packagingInfo")));
        }

        // 更新额外字段
        if (supplyInfo.containsKey("tpYear")) {
            supply.setTpYear(String.valueOf(supplyInfo.get("tpYear")));
        }
        if (supplyInfo.containsKey("companyName")) {
            supply.setCompanyName(String.valueOf(supplyInfo.get("companyName")));
        }
        if (supplyInfo.containsKey("tpCreditUrl")) {
            supply.setTpCreditUrl(String.valueOf(supplyInfo.get("tpCreditUrl")));
        }
        if (supplyInfo.containsKey("sales360Fuzzify")) {
            supply.setSales360Fuzzify(String.valueOf(supplyInfo.get("sales360Fuzzify")));
        }
        if (supplyInfo.containsKey("quantityBegin")) {
            supply.setQuantityBegin(String.valueOf(supplyInfo.get("quantityBegin")));
        }

        // 更新原始数据
        supply.setRawData(objectMapper.writeValueAsString(supplyInfo));

        // 更新时间
        supply.setUpdatedAt(LocalDateTime.now());
    }

    @Override
    public List<Supply> getSuppliesByEventId(Long eventId) {
        return supplyMapper.findByEventId(eventId);
    }

    @Override
    public List<Supply> getSuppliesByEventIdAndAmazonAsin(Long eventId, String amazonAsin) {
        return supplyMapper.findByEventIdAndAmazonAsin(eventId, amazonAsin);
    }

    @Override
    public IPage<SupplyDTO> getSupplyPage(SupplyQueryParam param) {
        log.info("分页查询1688货源列表: param={}", param);

        // 创建分页对象
        Page<Supply> page = new Page<>(param.getPage(), param.getSize());

        // 构建查询条件
        LambdaQueryWrapper<Supply> queryWrapper = new LambdaQueryWrapper<>();

        // 添加Amazon ASIN过滤条件
        if (param.getAmazonAsin() != null && !param.getAmazonAsin().isEmpty()) {
            queryWrapper.eq(Supply::getAmazonAsin, param.getAmazonAsin());
        }

        // 按ID倒序排序
        queryWrapper.orderByDesc(Supply::getId);

        // 执行查询
        IPage<Supply> supplyPage = supplyMapper.selectPage(page, queryWrapper);

        // 转换为DTO对象
        IPage<SupplyDTO> dtoPage = supplyPage.convert(this::convertToDTO);

        return dtoPage;
    }

    @Override
    public SupplyDTO getSupplyDTOById(Long id) {
        log.info("根据ID获取1688货源详情: id={}", id);

        Supply supply = supplyMapper.selectById(id);
        if (supply == null) {
            throw new BusinessException(ErrorCodeEnum.RESOURCE_NOT_FOUND, "1688货源不存在");
        }

        return convertToDTO(supply);
    }

    @Override
    public SupplyDTO getSupplyDTOByObjectId(String objectId) {
        log.info("根据阿里巴巴商品ID获取1688货源详情: objectId={}", objectId);

        LambdaQueryWrapper<Supply> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Supply::getObjectId, objectId);

        Supply supply = supplyMapper.selectOne(queryWrapper);
        if (supply == null) {
            throw new BusinessException(ErrorCodeEnum.RESOURCE_NOT_FOUND, "1688货源不存在");
        }

        return convertToDTO(supply);
    }

    @Override
    public Supply getSupplyById(Long id) {
        log.info("根据ID获取1688货源实体: id={}", id);

        Supply supply = supplyMapper.selectById(id);
        if (supply == null) {
            throw new BusinessException(ErrorCodeEnum.RESOURCE_NOT_FOUND, "1688货源不存在");
        }

        return supply;
    }

    /**
     * 将实体对象转换为DTO对象
     */
    private SupplyDTO convertToDTO(Supply supply) {
        SupplyDTO dto = new SupplyDTO();
        BeanUtils.copyProperties(supply, dto);
        return dto;
    }
}
