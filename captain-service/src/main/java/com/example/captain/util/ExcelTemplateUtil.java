package com.example.captain.util;

import com.example.captain.dto.AmazonProductExcelData;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.io.*;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * Excel模板工具类
 * 基于Amazon产品模板处理Excel文件的读写操作
 */
@Slf4j
@Component
public class ExcelTemplateUtil {

    private static final String TEMPLATE_PATH = "csv/template.xls";
    private static final int HEADER_ROWS = 3; // 模板前3行为头部信息
    
    private Workbook workbook;
    private Sheet sheet;
    private int currentDataRow;

    /**
     * 加载Excel模板
     * @return 是否加载成功
     */
    public boolean loadTemplate() {
        try {
            ClassPathResource resource = new ClassPathResource(TEMPLATE_PATH);
            InputStream inputStream = resource.getInputStream();
            
            // 使用HSSFWorkbook处理.xls文件
            workbook = new HSSFWorkbook(inputStream);
            sheet = workbook.getSheetAt(0); // 获取第一个工作表
            currentDataRow = HEADER_ROWS; // 从第4行开始写入数据
            
            log.info("Excel模板加载成功: {}", TEMPLATE_PATH);
            return true;
            
        } catch (IOException e) {
            log.error("加载Excel模板失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 写入单个产品数据到Excel
     * @param productData 产品数据
     */
    public void writeProductData(AmazonProductExcelData productData) {
        if (workbook == null || sheet == null) {
            throw new IllegalStateException("Excel模板未加载，请先调用loadTemplate()方法");
        }

        Row dataRow = sheet.createRow(currentDataRow++);
        
        // 基本产品信息
        setCellValue(dataRow, "feed_product_type", productData.getFeedProductType());
        setCellValue(dataRow, "item_sku", productData.getItemSku());
        setCellValue(dataRow, "brand_name", productData.getBrandName());
        setCellValue(dataRow, "external_product_id", productData.getExternalProductId());
        setCellValue(dataRow, "external_product_id_type", productData.getExternalProductIdType());
        setCellValue(dataRow, "item_name", productData.getItemName());
        setCellValue(dataRow, "manufacturer", productData.getManufacturer());
        setCellValue(dataRow, "recommended_browse_nodes", productData.getRecommendedBrowseNodes());
        setCellValue(dataRow, "max_order_quantity", productData.getMaxOrderQuantity());
        setCellValue(dataRow, "country_of_origin", productData.getCountryOfOrigin());

        // 价格和库存
        setCellValue(dataRow, "standard_price", productData.getStandardPrice());
        setCellValue(dataRow, "quantity", productData.getQuantity());

        // 图片信息
        setCellValue(dataRow, "main_image_url", productData.getMainImageUrl());
        
        // 其他图片URL (最多8张)
        for (int i = 0; i < 8; i++) {
            String imageUrl = productData.getOtherImageUrl(i);
            setCellValue(dataRow, "other_image_url" + (i + 1), imageUrl);
        }
        setCellValue(dataRow, "swatch_image_url", productData.getSwatchImageUrl());

        // 变体信息
        setCellValue(dataRow, "parent_child", productData.getParentChild());
        setCellValue(dataRow, "parent_sku", productData.getParentSku());
        setCellValue(dataRow, "relationship_type", productData.getRelationshipType());
        setCellValue(dataRow, "variation_theme", productData.getVariationTheme());

        // 基本描述信息
        setCellValue(dataRow, "update_delete", productData.getUpdateDelete());
        setCellValue(dataRow, "product_description", productData.getProductDescription());
        setCellValue(dataRow, "part_number", productData.getPartNumber());

        // 产品特性 (最多5个)
        for (int i = 0; i < 5; i++) {
            String bulletPoint = productData.getBulletPoint(i);
            setCellValue(dataRow, "bullet_point" + (i + 1), bulletPoint);
        }
        setCellValue(dataRow, "generic_keywords", productData.getGenericKeywords());

        // 产品属性
        setCellValue(dataRow, "metal_type", productData.getMetalType());
        setCellValue(dataRow, "department_name", productData.getDepartmentName());
        setCellValue(dataRow, "gem_type", productData.getGemType());
        setCellValue(dataRow, "special_features", productData.getSpecialFeatures());
        setCellValue(dataRow, "color_name", productData.getColorName());
        setCellValue(dataRow, "color_map", productData.getColorMap());
        setCellValue(dataRow, "size_name", productData.getSizeName());

        // 尺寸和重量
        setCellValue(dataRow, "website_shipping_weight", productData.getWebsiteShippingWeight());
        setCellValue(dataRow, "website_shipping_weight_unit_of_measure", productData.getWebsiteShippingWeightUnitOfMeasure());
        setCellValue(dataRow, "size_map", productData.getSizeMap());
        setCellValue(dataRow, "unit_count", productData.getUnitCount());
        setCellValue(dataRow, "unit_count_type", productData.getUnitCountType());
        setCellValue(dataRow, "depth_front_to_back", productData.getDepthFrontToBack());
        setCellValue(dataRow, "depth_front_to_back_unit_of_measure", productData.getDepthFrontToBackUnitOfMeasure());
        setCellValue(dataRow, "depth_width_side_to_side", productData.getDepthWidthSideToSide());
        setCellValue(dataRow, "depth_width_side_to_side_unit_of_measure", productData.getDepthWidthSideToSideUnitOfMeasure());
        setCellValue(dataRow, "depth_height_floor_to_top", productData.getDepthHeightFloorToTop());
        setCellValue(dataRow, "depth_height_floor_to_top_unit_of_measure", productData.getDepthHeightFloorToTopUnitOfMeasure());

        // 履行和价格
        setCellValue(dataRow, "fulfillment_center_id", productData.getFulfillmentCenterId());
        setCellValue(dataRow, "currency", productData.getCurrency());
        setCellValue(dataRow, "list_price", productData.getListPrice());
        setCellValue(dataRow, "list_price_with_tax", productData.getListPriceWithTax());
        setCellValue(dataRow, "condition_type", productData.getConditionType());

        // 合规性信息
        setCellValue(dataRow, "warranty_description", productData.getWarrantyDescription());
        setCellValue(dataRow, "are_batteries_included", productData.getAreBatteriesIncluded());
        setCellValue(dataRow, "batteries_required", productData.getBatteriesRequired());
        setCellValue(dataRow, "supplier_declared_dg_hz_regulation1", productData.getSupplierDeclaredDgHzRegulation1());

        log.debug("写入产品数据: SKU={}, ASIN={}", productData.getItemSku(), productData.getExternalProductId());
    }

    /**
     * 批量写入产品数据
     * @param productDataList 产品数据列表
     */
    public void writeProductsData(List<AmazonProductExcelData> productDataList) {
        if (productDataList == null || productDataList.isEmpty()) {
            log.warn("产品数据列表为空，跳过写入操作");
            return;
        }

        log.info("开始批量写入产品数据，共{}个产品", productDataList.size());
        
        for (AmazonProductExcelData productData : productDataList) {
            try {
                writeProductData(productData);
            } catch (Exception e) {
                log.error("写入产品数据失败: SKU={}, 错误: {}", 
                    productData.getItemSku(), e.getMessage(), e);
            }
        }
        
        log.info("批量写入产品数据完成");
    }

    /**
     * 生成Excel文件
     * @param outputPath 输出文件路径，如果为null则生成临时文件
     * @return 生成的文件路径
     */
    public String generateExcelFile(String outputPath) {
        if (workbook == null) {
            throw new IllegalStateException("Excel工作簿未初始化");
        }

        try {
            String filePath;
            if (outputPath == null || outputPath.trim().isEmpty()) {
                // 生成临时文件
                String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
                filePath = System.getProperty("java.io.tmpdir") + File.separator + 
                          "amazon_products_" + timestamp + ".xls";
            } else {
                filePath = outputPath;
            }

            // 确保目录存在
            Path path = Paths.get(filePath);
            Files.createDirectories(path.getParent());

            // 写入文件
            try (FileOutputStream outputStream = new FileOutputStream(filePath)) {
                workbook.write(outputStream);
            }

            log.info("Excel文件生成成功: {}", filePath);
            return filePath;

        } catch (IOException e) {
            log.error("生成Excel文件失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成Excel文件失败", e);
        }
    }

    /**
     * 关闭工作簿，释放资源
     */
    public void close() {
        if (workbook != null) {
            try {
                workbook.close();
                log.debug("Excel工作簿已关闭");
            } catch (IOException e) {
                log.warn("关闭Excel工作簿时发生错误: {}", e.getMessage());
            }
        }
    }

    /**
     * 设置单元格值
     * @param row 行对象
     * @param fieldName 字段名
     * @param value 值
     */
    private void setCellValue(Row row, String fieldName, Object value) {
        int columnIndex = ExcelFieldMapping.getColumnIndex(fieldName);
        if (columnIndex == -1) {
            log.warn("未找到字段映射: {}", fieldName);
            return;
        }

        Cell cell = row.createCell(columnIndex);
        
        if (value == null) {
            cell.setCellValue("");
        } else if (value instanceof String) {
            cell.setCellValue((String) value);
        } else if (value instanceof Integer) {
            cell.setCellValue((Integer) value);
        } else if (value instanceof BigDecimal) {
            cell.setCellValue(((BigDecimal) value).doubleValue());
        } else if (value instanceof Double) {
            cell.setCellValue((Double) value);
        } else if (value instanceof Boolean) {
            cell.setCellValue((Boolean) value);
        } else {
            cell.setCellValue(value.toString());
        }
    }
}
