package com.example.captain.util;

import java.math.BigInteger;

/**
 * ASIN压缩工具类
 * 将ASIN字符串压缩为更短的表示形式，并提供解压缩功能
 */
public class AsinCompressor {

    // 使用数字和字母作为基本字符集，共62个字符
    private static final String CHARSET = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
    private static final int BASE = CHARSET.length();

    /**
     * 将ASIN压缩为更短的字符串表示
     *
     * @param asin 原始ASIN字符串
     * @return 压缩后的字符串
     */
    public static String compress(String asin) {
        if (asin == null || asin.isEmpty()) {
            return "";
        }

        try {
            // 特殊处理：如果ASIN长度为10且以B开头（标准Amazon ASIN格式）
            if (asin.length() == 10 && (asin.charAt(0) == 'B' || asin.charAt(0) == 'b')) {
                // 移除B前缀，只处理后9位
                String asinWithoutB = asin.substring(1);

                // 将ASIN视为36进制数（0-9, A-Z）
                BigInteger value = BigInteger.ZERO;
                BigInteger base36 = BigInteger.valueOf(36);

                for (int i = 0; i < asinWithoutB.length(); i++) {
                    char c = asinWithoutB.charAt(i);
                    int digit;

                    if (c >= '0' && c <= '9') {
                        digit = c - '0';
                    } else if (c >= 'A' && c <= 'Z') {
                        digit = c - 'A' + 10;
                    } else if (c >= 'a' && c <= 'z') {
                        // 将小写字母转为大写处理
                        digit = c - 'a' + 10;
                    } else {
                        // 对于其他字符，使用特殊编码
                        digit = 35; // 使用Z的值
                    }

                    value = value.multiply(base36).add(BigInteger.valueOf(digit));
                }

                // 将BigInteger转换为62进制表示
                StringBuilder compressed = new StringBuilder();
                BigInteger base62 = BigInteger.valueOf(BASE);

                while (value.compareTo(BigInteger.ZERO) > 0) {
                    int remainder = value.mod(base62).intValue();
                    compressed.insert(0, CHARSET.charAt(remainder));
                    value = value.divide(base62);
                }

                // 确保压缩后的长度不超过6个字符
                String result = compressed.toString();
                if (result.length() > 6) {
                    result = result.substring(0, 6);
                }

                return result;
            }

            // 对于非标准ASIN格式，使用更通用的压缩方法

            // 1. 将字符串转换为字节数组
            byte[] bytes = asin.getBytes();

            // 2. 将字节数组转换为一个大整数
            BigInteger bigInt = new BigInteger(1, bytes);

            // 3. 将大整数转换为62进制字符串
            StringBuilder result = new StringBuilder();
            BigInteger base62 = BigInteger.valueOf(BASE);

            while (bigInt.compareTo(BigInteger.ZERO) > 0) {
                int remainder = bigInt.mod(base62).intValue();
                result.insert(0, CHARSET.charAt(remainder));
                bigInt = bigInt.divide(base62);
            }

            // 4. 如果压缩结果不够理想，使用简单截取方法
            if (result.length() >= asin.length()) {
                // 对于长度超过6的ASIN，取前6个字符
                if (asin.length() > 6) {
                    return asin.substring(0, 6);
                }
                return asin;
            }

            return result.toString();

        } catch (Exception e) {
            // 如果出现任何异常，返回原始ASIN的前6个字符
            if (asin.length() > 6) {
                return asin.substring(0, 6);
            }
            return asin;
        }
    }

    /**
     * 将压缩后的字符串解压缩为原始ASIN
     * 注意：由于压缩过程中可能丢失信息，解压缩可能无法完全还原原始ASIN
     *
     * @param compressed 压缩后的字符串
     * @return 解压缩后的字符串
     */
    public static String decompress(String compressed) {
        if (compressed == null || compressed.isEmpty()) {
            return "";
        }

        try {
            // 将压缩字符串转换为BigInteger
            BigInteger value = BigInteger.ZERO;
            BigInteger base62 = BigInteger.valueOf(BASE);

            for (int i = 0; i < compressed.length(); i++) {
                char c = compressed.charAt(i);
                int charIndex = CHARSET.indexOf(c);
                if (charIndex == -1) {
                    charIndex = 0;
                }
                value = value.multiply(base62).add(BigInteger.valueOf(charIndex));
            }

            // 尝试将BigInteger转换回36进制表示（假设是标准ASIN格式）
            StringBuilder asin = new StringBuilder("B"); // 添加B前缀
            BigInteger base36 = BigInteger.valueOf(36);

            while (value.compareTo(BigInteger.ZERO) > 0) {
                int remainder = value.mod(base36).intValue();
                if (remainder < 10) {
                    asin.append((char)('0' + remainder));
                } else {
                    asin.append((char)('A' + remainder - 10));
                }
                value = value.divide(base36);
            }

            // 确保ASIN长度为10个字符（包括B前缀）
            while (asin.length() < 10) {
                asin.insert(1, '0');
            }

            // 如果长度超过10，截取前10个字符
            if (asin.length() > 10) {
                return asin.substring(0, 10);
            }

            return asin.toString();

        } catch (Exception e) {
            // 如果解压缩失败，返回原始压缩字符串
            return compressed;
        }
    }

    /**
     * 测试方法，用于验证压缩效果
     */
    public static void main(String[] args) {
        String[] testAsins = {
            "B09TEST113",
            "B07PXGQC1Q",
            "B01DFKC2SO",
            "B00X4WHP5E",
            "B000P0ZUVY"
        };

        System.out.println("ASIN压缩测试：");
        System.out.println("原始ASIN\t\t压缩后\t\t压缩率");
        System.out.println("----------------------------------------");

        for (String asin : testAsins) {
            String compressed = compress(asin);
            double ratio = (1.0 - (double)compressed.length() / asin.length()) * 100;
            System.out.printf("%s\t%s\t\t%.1f%%\n", asin, compressed, ratio);
        }
    }
}
