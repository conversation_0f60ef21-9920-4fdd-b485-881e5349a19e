-- 创建1688货源表
CREATE TABLE supplies (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '货源ID',
    event_id BIGINT NOT NULL COMMENT '关联的事件ID',
    amazon_asin VARCHAR(20) NOT NULL COMMENT '关联的Amazon商品ASIN',
    object_id VARCHAR(50) NOT NULL COMMENT '阿里巴巴商品ID',
    title VARCHAR(500) COMMENT '商品标题',
    price VARCHAR(50) COMMENT '商品价格',
    detail_url VARCHAR(1000) COMMENT '商品详情页URL',
    image_url VARCHAR(1000) COMMENT '商品图片URL',
    local_image_path VARCHAR(255) COMMENT '本地保存的图片路径',
    similarity DECIMAL(5,4) COMMENT '与Amazon商品的相似度',
    product_attributes TEXT COMMENT '商品属性信息',
    packaging_info TEXT COMMENT '包装信息',
    tp_year VARCHAR(50) COMMENT '开店年限',
    company_name VARCHAR(255) COMMENT '公司名称',
    tp_credit_url VARCHAR(1000) COMMENT '公司档案URL',
    sales_360_fuzzify VARCHAR(100) COMMENT '年销量',
    quantity_begin VARCHAR(50) COMMENT '几件起批',
    raw_data LONGTEXT COMMENT '原始JSON数据',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_event_id (event_id),
    INDEX idx_amazon_asin (amazon_asin),
    INDEX idx_object_id (object_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='1688货源信息表';
