package com.example.captain.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.example.captain.dto.GoodsCreateRequest;
import com.example.captain.dto.GoodsDTO;
import com.example.captain.dto.GoodsQueryParam;
import com.example.captain.service.GoodsService;
import com.example.common.model.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * Amazon待发布商品控制器
 */
@RestController
@RequestMapping("/captain/goods")
@Slf4j
public class GoodsController {

    private final GoodsService goodsService;

    @Autowired
    public GoodsController(GoodsService goodsService) {
        this.goodsService = goodsService;
    }

    /**
     * 根据主商品ASIN + supplies表的object_id创建Amazon商品信息
     *
     * @param request 创建请求
     * @return 创建的商品信息列表（包含主商品和子商品）
     */
    @PostMapping
    public ApiResponse<List<GoodsDTO>> createGoods(@Valid @RequestBody GoodsCreateRequest request) {
        log.info("创建Amazon商品信息: request={}", request);
        List<GoodsDTO> goodsList = goodsService.createGoods(request);
        return ApiResponse.success(goodsList);
    }

    /**
     * 分页查询商品列表
     * 支持按event_id、amazon_asin、status、item_sku过滤
     * 结果按id倒序排列
     *
     * @param param 查询参数
     * @return 分页结果
     */
    @GetMapping
    public ApiResponse<IPage<GoodsDTO>> getGoodsPage(GoodsQueryParam param) {
        log.info("分页查询商品列表: param={}", param);
        IPage<GoodsDTO> page = goodsService.getGoodsPage(param);
        return ApiResponse.success(page);
    }

    /**
     * 根据ID获取商品详情
     *
     * @param id 商品ID
     * @return 商品详情
     */
    @GetMapping("/{id}")
    public ApiResponse<GoodsDTO> getGoodsById(@PathVariable(name = "id") Long id) {
        log.info("获取商品详情: id={}", id);
        GoodsDTO goods = goodsService.getGoodsDTOById(id);
        return ApiResponse.success(goods);
    }

    /**
     * 根据卖家SKU获取商品详情
     *
     * @param itemSku 卖家SKU
     * @return 商品详情
     */
    @GetMapping("/sku/{itemSku}")
    public ApiResponse<GoodsDTO> getGoodsByItemSku(@PathVariable(name = "itemSku") String itemSku) {
        log.info("根据卖家SKU获取商品详情: itemSku={}", itemSku);
        GoodsDTO goods = goodsService.getGoodsByItemSku(itemSku);
        return ApiResponse.success(goods);
    }

    /**
     * 更新商品状态
     *
     * @param id 商品ID
     * @param status 新状态：0-待审核，1-已审核，2-已发布，3-已拒绝
     * @param reviewNotes 审核备注
     * @param reviewedBy 审核人ID
     * @return 是否更新成功
     */
    @PutMapping("/{id}/status")
    public ApiResponse<Boolean> updateGoodsStatus(
            @PathVariable(name = "id") Long id,
            @RequestParam Integer status,
            @RequestParam(required = false) String reviewNotes,
            @RequestParam(required = false) Long reviewedBy) {
        log.info("更新商品状态: id={}, status={}, reviewedBy={}", id, status, reviewedBy);
        boolean result = goodsService.updateGoodsStatus(id, status, reviewNotes, reviewedBy);
        return ApiResponse.success(result);
    }
}
