# 如何运行OpenAI Function Call测试

## 🔧 准备工作

### 1. 配置OpenAI API密钥

在 `captain-service/src/main/resources/application.yml` 中配置您的API密钥：

```yaml
openai:
  api-key: sk-your-actual-openai-api-key-here  # 替换为您的真实API密钥
  model: gpt-3.5-turbo
  timeout: 30
```

### 2. 确保依赖已安装

```bash
cd captain-service
mvn clean compile
```

## 🚀 运行测试的三种方式

### 方式1: 运行JUnit集成测试（推荐）

```bash
cd captain-service
mvn test -Dtest=OpenAIProductAnalysisServiceIntegrationTest
```

这个测试包含5个详细的测试案例，会输出完整的提取过程和结果。

### 方式2: 运行命令行测试工具

```bash
cd captain-service
mvn exec:java -Dexec.mainClass="com.example.captain.util.OpenAITestRunner"
```

这个工具会运行3个快速测试案例，输出简洁的结果。

### 方式3: 在IDE中直接运行

1. 打开 `OpenAITestRunner.java` 文件
2. 右键点击 `main` 方法
3. 选择 "Run 'OpenAITestRunner.main()'"

## 📝 测试案例说明

### 测试案例1: 蓝牙耳机
- **输入**: 包含重量(0.5kg)和尺寸(20x15x8cm)的商品信息
- **预期**: 成功提取重量和三维尺寸

### 测试案例2: 笔记本支架  
- **输入**: 包含重量(1.2kg)和尺寸(28x22x6cm)的商品信息
- **预期**: 成功提取重量和三维尺寸

### 测试案例3: 无线充电器
- **输入**: 包含重量(150g)和圆形尺寸(直径10cm, 高度0.8cm)的商品信息
- **预期**: 成功提取重量，尺寸可能需要转换处理

### 测试案例4: 游戏鼠标
- **输入**: 包含重量(95g)和尺寸(12.5x6.8x4.2cm)的商品信息
- **预期**: 成功提取重量和三维尺寸

### 测试案例5: 不完整数据
- **输入**: 缺少重量和尺寸信息的商品描述
- **预期**: 返回null或空值，测试错误处理

## 📊 预期输出示例

### 成功提取的输出：
```
🎧 测试案例1: 蓝牙耳机
----------------------------------------
📝 输入:
Title: Wireless Bluetooth Headphones - Over Ear Headphones with Microphone...
Feature: 【40H Playtime】: 40 hours wireless mode. Weight: 500g. Dimensions: 20cm x 15cm x 8cm...
Description: Premium wireless Bluetooth headphones. Package dimensions: 20 x 15 x 8 centimeters...

🔄 调用OpenAI API...

📊 提取结果:
🏋️ 重量: 0.5 KG
📏 尺寸: 长20.0CM × 宽15.0CM × 高8.0CM
✅ 提取成功
```

### 失败情况的输出：
```
❌ 未提取到有效数据
原因: 商品信息中缺少重量和尺寸信息
```

## ⚠️ 注意事项

1. **API密钥**: 确保配置了有效的OpenAI API密钥
2. **网络连接**: 需要能够访问OpenAI API的网络环境
3. **API配额**: 每次测试会消耗OpenAI API配额
4. **响应时间**: 每个API调用可能需要2-5秒

## 🔧 故障排除

### 问题1: API密钥错误
```
错误信息: OpenAI API密钥未配置或无效
解决方案: 检查application.yml中的api-key配置
```

### 问题2: 网络连接问题
```
错误信息: 连接超时或网络错误
解决方案: 检查网络连接，确保能访问api.openai.com
```

### 问题3: 编译错误
```
错误信息: 找不到符号或类
解决方案: 运行 mvn clean compile 重新编译
```

### 问题4: 测试无响应
```
可能原因: OpenAI API响应慢
解决方案: 等待更长时间或增加timeout配置
```

## 📈 自定义测试

您可以修改测试数据来测试不同的商品信息：

```java
// 在OpenAITestRunner.java中修改测试数据
String title = "您的商品标题";
String feature = "您的商品特性，包含重量和尺寸信息";
String description = "您的商品描述";
```

## 🎯 测试目标

通过这些测试，您可以验证：
- ✅ OpenAI Function Call是否正常工作
- ✅ 重量信息是否正确提取并转换为KG
- ✅ 尺寸信息是否正确提取并转换为CM
- ✅ 错误处理是否正常工作
- ✅ 不同格式的商品信息是否都能处理

运行测试后，您就可以确信OpenAI功能已经正确集成到系统中！
