-- 查询测试数据的SQL脚本
-- 使用方法: mysql -u lihui -p'Lihui@123!' -h localhost captain_service < script/query-test-data.sql

-- 1. 显示当前数据库和表信息
SELECT 'Current Database:' as info, DATABASE() as value
UNION ALL
SELECT 'Current Time:', NOW();

-- 2. 检查表结构，确认新字段是否存在
SELECT 'Table Structure Check:' as info, '' as value;
DESCRIBE products;

-- 3. 统计所有产品数据
SELECT 'Product Statistics:' as info, '' as value;
SELECT 
    'Total Products' as metric,
    COUNT(*) as count
FROM products
UNION ALL
SELECT 
    'Products with prod_details',
    COUNT(*)
FROM products 
WHERE prod_details IS NOT NULL AND prod_details != ''
UNION ALL
SELECT 
    'Products with product_overview',
    COUNT(*)
FROM products 
WHERE product_overview IS NOT NULL AND product_overview != '';

-- 4. 查找所有测试数据
SELECT 'Test Data Found:' as info, '' as value;
SELECT 
    id,
    asin,
    title,
    CASE 
        WHEN prod_details IS NOT NULL AND prod_details != '' THEN 'YES' 
        ELSE 'NO' 
    END as has_prod_details,
    CASE 
        WHEN product_overview IS NOT NULL AND product_overview != '' THEN 'YES' 
        ELSE 'NO' 
    END as has_product_overview,
    created_at
FROM products 
WHERE asin LIKE 'B08%' 
   OR title LIKE '%测试%' 
   OR title LIKE '%test%'
   OR title LIKE '%Test%'
ORDER BY created_at DESC;

-- 5. 查看新字段的具体内容（最近的5条记录）
SELECT 'Recent Products with New Fields:' as info, '' as value;
SELECT 
    asin,
    title,
    SUBSTRING(prod_details, 1, 200) as prod_details_preview,
    SUBSTRING(product_overview, 1, 200) as product_overview_preview,
    created_at
FROM products 
WHERE (prod_details IS NOT NULL AND prod_details != '')
   OR (product_overview IS NOT NULL AND product_overview != '')
ORDER BY created_at DESC 
LIMIT 5;

-- 6. 查找包含特定测试标识的数据
SELECT 'Data with Test Markers:' as info, '' as value;
SELECT 
    asin,
    title,
    prod_details,
    product_overview
FROM products 
WHERE prod_details LIKE '%修复验证%'
   OR prod_details LIKE '%新字段测试%'
   OR product_overview LIKE '%修复验证%'
   OR product_overview LIKE '%新字段测试%'
ORDER BY created_at DESC;

-- 7. 按事件ID分组统计
SELECT 'Statistics by Event ID:' as info, '' as value;
SELECT 
    event_id,
    COUNT(*) as total_products,
    COUNT(CASE WHEN prod_details IS NOT NULL AND prod_details != '' THEN 1 END) as with_prod_details,
    COUNT(CASE WHEN product_overview IS NOT NULL AND product_overview != '' THEN 1 END) as with_product_overview
FROM products 
GROUP BY event_id
ORDER BY event_id;

-- 8. 最近创建的产品（包含新字段状态）
SELECT 'Recent Products (Last 10):' as info, '' as value;
SELECT 
    id,
    asin,
    SUBSTRING(title, 1, 50) as title_preview,
    CASE 
        WHEN prod_details IS NOT NULL AND prod_details != '' THEN CONCAT('YES (', LENGTH(prod_details), ' chars)')
        ELSE 'NO' 
    END as prod_details_status,
    CASE 
        WHEN product_overview IS NOT NULL AND product_overview != '' THEN CONCAT('YES (', LENGTH(product_overview), ' chars)')
        ELSE 'NO' 
    END as product_overview_status,
    created_at
FROM products 
ORDER BY created_at DESC 
LIMIT 10;
