-- 创建Amazon待发布商品表
CREATE TABLE goods (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '商品ID',
    event_id BIGINT NOT NULL COMMENT '关联的事件ID',

    -- 关联信息
    amazon_asin VARCHAR(20) NOT NULL COMMENT '关联的Amazon商品ASIN',
    supply_id BIGINT COMMENT '关联的1688货源ID',

    -- 基本产品信息 (对应Excel字段)
    feed_product_type VARCHAR(50) DEFAULT 'PRODUCT' COMMENT '产品类型',
    item_sku VARCHAR(100) NOT NULL COMMENT '卖家SKU',
    brand_name VARCHAR(100) COMMENT '品牌名称',
    external_product_id VARCHAR(20) COMMENT '外部产品ID(ASIN)',
    external_product_id_type VARCHAR(10) DEFAULT 'ASIN' COMMENT '外部产品ID类型',
    item_name VARCHAR(500) COMMENT '商品名称',
    manufacturer VARCHAR(100) COMMENT '制造商',
    recommended_browse_nodes VARCHAR(100) COMMENT '推荐浏览节点',
    max_order_quantity INT DEFAULT 10 COMMENT '最大订购数量',
    country_of_origin VARCHAR(50) COMMENT '原产国',

    -- 价格和库存
    standard_price DECIMAL(10,2) COMMENT '标准价格',
    quantity INT DEFAULT 100 COMMENT '库存数量',

    -- 图片信息
    main_image_url TEXT COMMENT '主图片URL',
    other_image_url1 TEXT COMMENT '其他图片URL1',
    other_image_url2 TEXT COMMENT '其他图片URL2',
    other_image_url3 TEXT COMMENT '其他图片URL3',
    other_image_url4 TEXT COMMENT '其他图片URL4',
    other_image_url5 TEXT COMMENT '其他图片URL5',
    other_image_url6 TEXT COMMENT '其他图片URL6',
    other_image_url7 TEXT COMMENT '其他图片URL7',
    other_image_url8 TEXT COMMENT '其他图片URL8',
    swatch_image_url TEXT COMMENT '样本图片URL',

    -- 变体信息
    parent_child VARCHAR(10) COMMENT '父子关系(parent/child)',
    parent_sku VARCHAR(100) COMMENT '父SKU',
    relationship_type VARCHAR(50) COMMENT '关系类型',
    variation_theme VARCHAR(100) COMMENT '变体主题',

    -- 基本描述信息
    update_delete VARCHAR(10) DEFAULT 'Update' COMMENT '更新删除标志',
    product_description TEXT COMMENT '产品描述',
    part_number VARCHAR(100) COMMENT '制造商零件号',

    -- 产品特性
    bullet_point1 TEXT COMMENT '关键产品特性1',
    bullet_point2 TEXT COMMENT '关键产品特性2',
    bullet_point3 TEXT COMMENT '关键产品特性3',
    bullet_point4 TEXT COMMENT '关键产品特性4',
    bullet_point5 TEXT COMMENT '关键产品特性5',
    generic_keywords TEXT COMMENT '搜索关键词',

    -- 产品属性
    metal_type VARCHAR(50) COMMENT '金属类型',
    department_name VARCHAR(100) COMMENT '部门名称',
    gem_type VARCHAR(50) COMMENT '宝石类型',
    special_features TEXT COMMENT '特殊功能',
    color_name VARCHAR(50) COMMENT '颜色名称',
    color_map VARCHAR(50) COMMENT '颜色映射',
    size_name VARCHAR(50) COMMENT '尺寸名称',

    -- 尺寸和重量
    website_shipping_weight DECIMAL(8,3) COMMENT '网站运输重量',
    website_shipping_weight_unit_of_measure VARCHAR(10) COMMENT '网站运输重量单位',
    size_map VARCHAR(50) COMMENT '尺寸映射',
    unit_count INT COMMENT '单位数量',
    unit_count_type VARCHAR(50) COMMENT '单位数量类型',
    depth_front_to_back DECIMAL(8,3) COMMENT '深度(前后)',
    depth_front_to_back_unit_of_measure VARCHAR(10) COMMENT '深度单位',
    depth_width_side_to_side DECIMAL(8,3) COMMENT '宽度(左右)',
    depth_width_side_to_side_unit_of_measure VARCHAR(10) COMMENT '宽度单位',
    depth_height_floor_to_top DECIMAL(8,3) COMMENT '高度(底部到顶部)',
    depth_height_floor_to_top_unit_of_measure VARCHAR(10) COMMENT '高度单位',

    -- 履行和价格
    fulfillment_center_id VARCHAR(50) COMMENT '履行中心ID',
    currency VARCHAR(10) DEFAULT 'USD' COMMENT '货币',
    list_price DECIMAL(10,2) COMMENT '标价',
    list_price_with_tax DECIMAL(10,2) COMMENT '含税标价',
    condition_type VARCHAR(20) DEFAULT 'New' COMMENT '商品状态',

    -- 合规性信息
    warranty_description TEXT COMMENT '保修描述',
    are_batteries_included VARCHAR(10) COMMENT '是否包含电池',
    batteries_required VARCHAR(10) COMMENT '是否需要电池',
    supplier_declared_dg_hz_regulation1 VARCHAR(100) COMMENT '危险品法规声明',

    -- 状态和审核
    status TINYINT DEFAULT 0 COMMENT '状态：0-待审核，1-已审核，2-已发布，3-已拒绝',
    review_notes TEXT COMMENT '审核备注',
    reviewed_by BIGINT COMMENT '审核人ID',
    reviewed_at TIMESTAMP NULL COMMENT '审核时间',

    -- 数据来源信息
    source_type TINYINT DEFAULT 1 COMMENT '数据来源：1-自动生成，2-手工录入',
    generation_rules TEXT COMMENT '生成规则记录',

    -- 时间戳
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 索引
    INDEX idx_event_id (event_id),
    INDEX idx_amazon_asin (amazon_asin),
    INDEX idx_supply_id (supply_id),
    INDEX idx_item_sku (item_sku),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),

    -- 外键约束
    FOREIGN KEY (supply_id) REFERENCES supplies(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Amazon待发布商品表';
