package com.example.worker.service;

import com.example.worker.client.AmazonClient;
import com.example.worker.client.AlibabaClient;
import com.example.worker.config.AppConfig;
import com.example.worker.imagesimilarity.CustomSegmentationSimilarityService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.PreDestroy;
import java.io.InputStream;
import java.net.URI;
import java.net.URL;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.net.http.HttpResponse.BodyHandlers;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.Duration;
import java.util.ArrayList;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Amazon服务
 * 用于操作浏览器交互获取Amazon数据
 */
@Service
@Slf4j
public class AmazonService implements AutoCloseable {

    private AmazonClient amazonClient;
    private final AppConfig appConfig;
    private final ObjectMapper objectMapper;
    private final AuthService authService;

    // 回调计数器
    private final java.util.concurrent.atomic.AtomicInteger callbackCount = new java.util.concurrent.atomic.AtomicInteger(0);

    /**
     * 构造函数
     * 初始化AmazonClient
     * 默认使用无头模式，AmazonClient会在需要登录或验证码时自动切换为有头模式
     */
    @Autowired
    public AmazonService(AppConfig appConfig, ObjectMapper objectMapper, AuthService authService) {
        this.appConfig = appConfig;
        this.objectMapper = objectMapper;
        this.authService = authService;
        // 创建无头模式的AmazonClient
        this.amazonClient = new AmazonClient(true);
        log.info("AmazonService已初始化");
    }

    /**
     * 初始化Amazon会话
     * 确保AmazonClient已初始化会话
     *
     * @return 初始化是否成功
     */
    public boolean initSession() {
        return amazonClient.initSession();
    }

    /**
     * 获取Amazon产品链接
     * 使用AmazonClient获取产品链接和下一页链接
     *
     * @param url Amazon产品搜索页面URL
     * @return 产品链接列表
     */
    @SuppressWarnings("unchecked")
    public List<String> getAmazonProductLinks(String url) {
        log.info("正在获取Amazon产品链接: {}", url);

        // 确保AmazonClient已初始化会话
        if (!amazonClient.initSession()) {
            log.error("初始化Amazon会话失败");
            return List.of("初始化Amazon会话失败");
        }

        // 获取产品链接和下一页
        Map<String, Object> result = amazonClient.getProductLinksAndNextPage(url);

        if (result.containsKey("error")) {
            log.error("获取产品链接时出错: {}", result.get("error"));
            return List.of("获取产品链接时出错: " + result.get("error"));
        }

        return (List<String>) result.get("product_links");
    }

    /**
     * 获取Amazon商品详细信息
     * 使用AmazonClient获取商品详细信息
     *
     * @param url Amazon商品详情页URL
     * @return 商品详细信息Map
     */
    public Map<String, Object> getAmazonProductInfo(String url) {
        log.info("正在获取Amazon商品详细信息: {}", url);

        // 确保AmazonClient已初始化会话
        if (!amazonClient.initSession()) {
            log.error("初始化Amazon会话失败");
            return Map.of("error", "初始化Amazon会话失败");
        }

        // 获取商品详细信息
        return amazonClient.getProductInfo(url);
    }

    /**
     * 处理fetch通知
     * 1. 解析URL页面中的产品链接和下一页链接
     * 2. 解析商品详情和变体商品链接
     * 3. 处理变体商品详情
     * 4. 处理下一页，直到达到最大页面数或最大商品数
     *
     * @param eventId 事件ID
     * @param url Amazon产品搜索页面URL
     * @return 处理结果
     */
    @SuppressWarnings("unchecked")
    public Map<String, Object> processFetchNotification(long eventId, String url) {
        log.info("开始处理fetch通知: {}", url);

        // 确保AmazonClient已初始化会话
        if (!amazonClient.initSession()) {
            log.error("初始化Amazon会话失败");
            // 使用HashMap代替Map.of()，因为Map.of()不允许null值
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", "初始化Amazon会话失败");
            return errorResult;
        }

        // 用于存储所有处理过的商品链接，避免重复处理
        Set<String> processedLinks = new HashSet<>();
        // 用于存储所有获取到的商品信息
        List<Map<String, Object>> productInfoList = new ArrayList<>();
        // 当前页面URL
        String currentPageUrl = url;
        // 页面计数器
        int pageCount = 0;
        // 最大页面数和最大商品数限制
        final int MAX_PAGES = 1;
        final int MAX_PRODUCTS = 2;

        try {
            // 循环处理页面，直到没有下一页或达到限制
            while (currentPageUrl != null && pageCount < MAX_PAGES && productInfoList.size() < MAX_PRODUCTS) {
                log.info("处理第 {} 页: {}", pageCount + 1, currentPageUrl);

                // 处理当前页面
                Map<String, Object> pageResult = processPage(eventId, currentPageUrl, processedLinks, productInfoList, MAX_PRODUCTS);

                // 检查是否有错误
                if (pageResult.containsKey("error")) {
                    return pageResult;
                }

                // 获取下一页链接
                String nextPage = (String) pageResult.get("next_page");

                // 更新当前页面URL和计数器
                currentPageUrl = nextPage;
                pageCount++;

                log.info("完成第 {} 页处理，下一页: {}", pageCount, currentPageUrl);

                // 检查是否达到最大商品数
                if (isMaxProductsReached(productInfoList, MAX_PRODUCTS)) {
                    log.info("已达到最大商品数 {}，停止处理更多页面", MAX_PRODUCTS);
                    break;
                }
            }

            log.info("fetch通知处理完成，共处理 {} 页，获取 {} 个商品信息，回传 {} 次",
                pageCount, productInfoList.size(), callbackCount.get());

            // 使用HashMap代替Map.of()，因为Map.of()不允许null值
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("page_count", pageCount);
            result.put("product_count", productInfoList.size());
            result.put("callback_count", callbackCount.get());
            return result;

        } catch (Exception e) {
            log.error("处理fetch通知时发生错误", e);
            // 使用HashMap代替Map.of()，因为Map.of()不允许null值
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", "处理fetch通知时发生错误: " + (e.getMessage() != null ? e.getMessage() : "未知错误"));
            return errorResult;
        }
    }

    /**
     * 处理单个页面
     *
     * @param eventId 事件ID
     * @param pageUrl 页面URL
     * @param processedLinks 已处理的链接集合
     * @param productInfoList 商品信息列表
     * @param maxProducts 最大商品数
     * @return 处理结果
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> processPage(long eventId, String pageUrl, Set<String> processedLinks,
                                           List<Map<String, Object>> productInfoList, int maxProducts) {
        // 确保AmazonClient已初始化会话
        if (!amazonClient.initSession()) {
            log.error("初始化Amazon会话失败");
            // 使用HashMap代替Map.of()，因为Map.of()不允许null值
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", "初始化Amazon会话失败");
            return errorResult;
        }

        // 获取产品链接和下一页链接
        Map<String, Object> pageResult = amazonClient.getProductLinksAndNextPage(pageUrl);

        if (pageResult.containsKey("error")) {
            log.error("获取产品链接时出错: {}", pageResult.get("error"));
            // 使用HashMap代替Map.of()，因为Map.of()不允许null值
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", "获取产品链接时出错: " + pageResult.get("error"));
            return errorResult;
        }

        // 获取产品链接列表和下一页链接
        List<String> productLinks = (List<String>) pageResult.get("product_links");
        String nextPage = (String) pageResult.get("next_page");

        log.info("获取到 {} 个产品链接", productLinks.size());

        // 处理产品链接
        for (String productLink : productLinks) {
            // 如果已达到最大商品数，停止处理
            if (isMaxProductsReached(productInfoList, maxProducts)) {
                log.info("已达到最大商品数 {}，停止处理更多商品", maxProducts);
                break;
            }

            // 处理单个商品，非变体商品parentAsin为null
            processProduct(eventId, productLink, null, processedLinks, productInfoList, maxProducts);
        }

        // 返回处理结果
        Map<String, Object> result = new HashMap<>();
        result.put("next_page", nextPage);
        return result;
    }

    /**
     * 回传单个产品数据
     *
     * @param eventId 事件ID
     * @param productInfo 产品信息
     * @return 回调结果
     */
    private Map<String, Object> callbackProductInfo(long eventId, Map<String, Object> productInfo) {
        String asin = (String) productInfo.get("ASIN");
        log.info("正在回传单个产品数据: eventId={}, ASIN={}", eventId, asin);

        try {
            // 获取JWT令牌
            String token = authService.getToken();
            if (token == null) {
                log.error("获取JWT令牌失败，无法回传产品数据");
                Map<String, Object> errorResult = new HashMap<>();
                errorResult.put("code", -2);
                errorResult.put("message", "获取JWT令牌失败");
                return errorResult;
            }

            // 构建请求URL - 使用captain-service的ProductCallbackController路径
            String callbackUrl = appConfig.getGateway().getUrl() + "/api/captain/callback/" + eventId + "/product";
            log.info("回调URL: {}", callbackUrl);

            // 构建请求体 - 将单个商品放入列表中
            List<Map<String, Object>> productList = new ArrayList<>();
            productList.add(productInfo);
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("products", productList);

            // 发送POST请求
            HttpClient httpClient = HttpClient.newBuilder()
                .connectTimeout(Duration.ofSeconds(30))
                .build();

            HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(callbackUrl))
                .header("Content-Type", "application/json")
                .header("Authorization", "Bearer " + token)
                .POST(HttpRequest.BodyPublishers.ofString(objectMapper.writeValueAsString(requestBody)))
                .build();

            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());

            // 解析响应
            Map<String, Object> responseBody = objectMapper.readValue(response.body(), new TypeReference<>() {});

            return responseBody;
        } catch (Exception e) {
            log.error("回传产品数据时出错: {}", e.getMessage());
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("code", -2);
            errorResult.put("message", "回传产品数据时出错: " + (e.getMessage() != null ? e.getMessage() : "未知错误"));
            return errorResult;
        }
    }

    /**
     * 处理单个商品
     *
     * @param eventId 事件ID
     * @param productLink 商品链接
     * @param variant_attributes 变体属性，变体商品处理时传入
     * @param processedLinks 已处理的链接集合
     * @param productInfoList 商品信息列表
     * @param maxProducts 最大商品数
     */
    @SuppressWarnings("unchecked")
    private void processProduct(long eventId, String productLink, Map<String, Object> variant_attributes, Set<String> processedLinks,
                               List<Map<String, Object>> productInfoList, int maxProducts) {
        // 如果已经处理过该链接，则跳过
        if (processedLinks.contains(productLink)) {
            log.debug("跳过已处理的商品链接: {}", productLink);
            return;
        }

        // 添加到已处理集合
        processedLinks.add(productLink);

        // 确保AmazonClient已初始化会话
        if (!amazonClient.initSession()) {
            log.error("初始化Amazon会话失败");
            return;
        }

        // 获取商品详情
        Map<String, Object> productInfo = getAmazonProductInfo(productLink);

        if (productInfo.containsKey("error")) {
            log.error("获取商品详情时出错: {}", productInfo.get("error"));
            return;
        }

        // 添加商品链接到productInfo
        productInfo.put("productLink", productLink);

        // 设置变体属性信息
        if (variant_attributes == null && productInfo.get("productVariants") != null &&
            !((List<Map<String, Object>>) productInfo.get("productVariants")).isEmpty()) {
            // 如果是父变体商品（有变体但没有变体属性），设置自身ASIN为父ASIN
            productInfo.put("parentAsin", productInfo.get("ASIN"));
            productInfo.put("is_parent_variant", true);

            // 查找与当前商品ASIN匹配的变体属性
            List<Map<String, Object>> productVariants = (List<Map<String, Object>>) productInfo.get("productVariants");
            String currentAsin = (String) productInfo.get("ASIN");

            // 查找匹配的变体属性
            for (Map<String, Object> variant : productVariants) {
                String variantAsin = (String) variant.get("asin");
                if (variantAsin != null && variantAsin.equals(currentAsin)) {
                    // 找到匹配的变体属性，设置为父商品的variant_attributes
                    productInfo.put("variant_attributes", variant);
                    log.info("找到父商品的变体属性: ASIN={}", currentAsin);
                    break;
                }
            }

            log.info("设置父变体商品: ASIN={}, 变体数量={}",
                    productInfo.get("ASIN"),
                    productVariants.size());
        } else if (variant_attributes != null) {
            // 如果是子变体商品，设置传入的变体属性和父ASIN
            String parentAsin = (String) variant_attributes.get("parentAsin");
            productInfo.put("parentAsin", parentAsin);
            productInfo.put("is_parent_variant", false);
            productInfo.put("variant_attributes", variant_attributes);
            log.info("设置子变体商品: ASIN={}, 父ASIN={}",
                    productInfo.get("ASIN"), parentAsin);
        }

        // 货源采集任务
        // 在这里根据商品主图片获取1688货源，获取前20张商品图片、链接，并保存图片
        // 商品主图+1688图片进行相似度比较
        // 抓取相似度比较高可用货源链接，提取1688商品货源信息并入库

        // 调用独立函数处理Alibaba相似商品
        // 默认从API抓取5个商品，返回3个最相似的商品
        findSimilarAlibabaProducts(eventId, productInfo);

        // 添加到结果列表
        productInfoList.add(productInfo);

        // 立即回传单个商品数据
        Map<String, Object> callbackResult = callbackProductInfo(eventId, productInfo);
        log.info("单个商品数据回传结果: {}", callbackResult);

        // 增加回调计数
        callbackCount.incrementAndGet();

        // 如果已达到最大商品数，停止处理
        if (isMaxProductsReached(productInfoList, maxProducts)) {
            log.info("已达到最大商品数 {}，停止处理更多商品", maxProducts);
            return;
        }

        // 主商品开启变体商品处理流程
        if (variant_attributes == null && productInfo.get("productVariants") != null &&
            !((List<Map<String, Object>>) productInfo.get("productVariants")).isEmpty()) {
            // 处理变体商品链接
            processVariants(eventId, productInfo, processedLinks, productInfoList, maxProducts);
        }
        
    }

    /**
     * 处理变体商品
     *
     * @param eventId 事件ID
     * @param productInfo 商品信息
     * @param processedLinks 已处理的链接集合
     * @param productInfoList 商品信息列表
     * @param maxProducts 最大商品数
     */
    @SuppressWarnings("unchecked")
    private void processVariants(long eventId, Map<String, Object> productInfo, Set<String> processedLinks,
                                List<Map<String, Object>> productInfoList, int maxProducts) {
        Object variantLinks = productInfo.get("productVariants");

        // 如果没有变体商品链接，直接返回
        if (!(variantLinks instanceof List) || ((List<Map<String, Object>>) variantLinks).isEmpty()) {
            return;
        }

        List<Map<String, Object>> productVariants = (List<Map<String, Object>>) variantLinks;
        log.info("发现 {} 个变体商品链接", productVariants.size());

        // 遍历变体商品链接
        for (Map<String, Object> productVariant : productVariants) {
            // 如果已达到最大商品数，停止处理
            if (isMaxProductsReached(productInfoList, maxProducts)) {
                log.info("已达到最大商品数 {}，停止处理变体商品", maxProducts);
                break;
            }

            String variantLink = (String) productVariant.get("link");

            // 处理单个变体商品，传入父商品ASIN
            processProduct(eventId, variantLink, productVariant, processedLinks, productInfoList, maxProducts);
        }
    }

    /**
     * 查找相似的Alibaba商品
     * 根据Amazon商品主图搜索相似的Alibaba商品，并进行相似度比较
     *
     * @param eventId 事件ID
     * @param productInfo Amazon商品信息
     * @param maxProductsToExtract 要从API抓取的最大商品数量，默认为5
     * @param maxSimilarProductsToReturn 相似度排序后返回的最大商品数量，默认为3
     */
    public void findSimilarAlibabaProducts(Long eventId, Map<String, Object> productInfo, int maxProductsToExtract, int maxSimilarProductsToReturn) {
        // 1. 从productInfo对象中获取商品主图链接
        String mainImageUrl = null;
        if (productInfo.containsKey("mainImage")) {
            mainImageUrl = (String) productInfo.get("mainImage");
        } else if (productInfo.containsKey("images") && productInfo.get("images") instanceof List && !((List<?>) productInfo.get("images")).isEmpty()) {
            mainImageUrl = (String) ((List<?>) productInfo.get("images")).get(0);
        }

        if (mainImageUrl != null) {
            try {
                log.info("开始根据Amazon商品主图搜索Alibaba相似商品: {}", mainImageUrl);

                // 2. 使用AlibabaClient搜索相似商品，传入maxProductsToExtract参数和图片保存目录
                String asin = (String) productInfo.get("ASIN");
                String alibabaImageFolder = "images/amazon/" + asin + "/alibaba";
                Map<String, Object> alibabaProducts;

                // 不使用try-with-resources，避免在处理完响应前关闭资源
                AlibabaClient alibabaClient = new AlibabaClient(maxProductsToExtract);
                try {
                    alibabaProducts = alibabaClient.searchProductsByImage(mainImageUrl, alibabaImageFolder);
                } finally {
                    try {
                        // 确保资源被关闭
                        alibabaClient.close();
                        log.info("AlibabaClient已关闭");
                    } catch (Exception e) {
                        log.error("关闭AlibabaClient时出错: {}", e.getMessage());
                    }
                }

                if (alibabaProducts.containsKey("success") && (Boolean) alibabaProducts.get("success")) {
                    // 获取搜索结果
                    @SuppressWarnings("unchecked")
                    List<Map<String, Object>> results = (List<Map<String, Object>>) alibabaProducts.get("results");

                    if (results != null && !results.isEmpty()) {
                        log.info("找到 {} 个Alibaba相似商品", results.size());

                        // 3. 加载Amazon商品主图和Alibaba商品图片进行相似度比较
                        // 从productInfo对象中获取已下载的商品主图本地地址
                        String amazonImageLocalPath = null;

                        // 检查是否已经通过AmazonClient下载了图片
                        if (productInfo.containsKey("saved_images") && productInfo.get("saved_images") instanceof List) {
                            @SuppressWarnings("unchecked")
                            List<String> savedImages = (List<String>) productInfo.get("saved_images");
                            if (!savedImages.isEmpty()) {
                                // 使用第一张图片作为主图
                                amazonImageLocalPath = savedImages.get(0);
                                log.info("使用已下载的商品主图: {}", amazonImageLocalPath);
                            }
                        }

                        // 如果没有找到已下载的图片，无法继续进行相似度比较
                        if (amazonImageLocalPath == null) {
                            log.error("无法获取Amazon商品主图，无法继续进行相似度比较");
                            return; // 直接返回，不再继续执行
                        }

                        // 创建相似度服务，使用try-with-resources确保资源释放
                        try (CustomSegmentationSimilarityService similarityService = new CustomSegmentationSimilarityService()) {
                            // 存储相似度结果
                            List<Map<String, Object>> similarProducts = new ArrayList<>();

                            // 遍历Alibaba商品，计算相似度
                            for (Map<String, Object> alibabaProduct : results) {
                                String localImagePath = (String) alibabaProduct.get("localImagePath");

                                if (localImagePath != null && !localImagePath.isEmpty()) {
                                    try {
                                        // 计算相似度
                                        double similarity = similarityService.calculateTraditionalSimilarityOnly(
                                            amazonImageLocalPath, localImagePath);

                                        log.info("图片: {} 相似度: {}", localImagePath, similarity);

                                        // 添加相似度到结果中
                                        alibabaProduct.put("similarity", similarity);

                                        // 添加所有商品到相似商品列表，不再使用80%的相似度限制
                                        similarProducts.add(alibabaProduct);
                                    } catch (Exception e) {
                                        log.error("计算商品相似度时出错: {}", e.getMessage());
                                    }
                                }
                            }

                            // 4. 按相似度排序，取Top N (maxSimilarProductsToReturn)
                            similarProducts.sort((p1, p2) -> {
                                Double s1 = (Double) p1.get("similarity");
                                Double s2 = (Double) p2.get("similarity");
                                return s2.compareTo(s1); // 降序排序
                            });

                            // 取Top N
                            List<Map<String, Object>> topProducts = similarProducts.stream()
                                .limit(maxSimilarProductsToReturn)
                                .collect(Collectors.toList());

                            // 获取并填充阿里巴巴商品的详情信息
                            enrichAlibabaProductDetails(topProducts, eventId, (String) productInfo.get("ASIN"));

                            // 将结果添加到productInfo中
                            productInfo.put("alibabaProducts", topProducts);

                            log.info("已添加 {} 个最相似的Alibaba商品到结果中", topProducts.size());
                        } // 自动调用similarityService.close()
                    }
                } else {
                    log.error("搜索Alibaba相似商品失败: {}",
                        alibabaProducts.containsKey("error") ? alibabaProducts.get("error") : "未知错误");
                }
            } catch (Exception e) {
                log.error("处理Alibaba相似商品时出错", e);
            }
        }
    }

    /**
     * 查找相似的Alibaba商品（使用默认参数）
     *
     * @param eventId 事件ID
     * @param productInfo Amazon商品信息
     */
    public void findSimilarAlibabaProducts(Long eventId, Map<String, Object> productInfo) {
        // 默认从API抓取5个商品，返回3个最相似的商品
        findSimilarAlibabaProducts(eventId, productInfo, 5, 3);
    }

    /**
     * 查找相似的Alibaba商品（使用默认相似商品返回数量）
     *
     * @param eventId 事件ID
     * @param productInfo Amazon商品信息
     * @param maxProductsToExtract 要从API抓取的最大商品数量
     */
    public void findSimilarAlibabaProducts(Long eventId, Map<String, Object> productInfo, int maxProductsToExtract) {
        // 默认返回3个最相似的商品
        findSimilarAlibabaProducts(eventId, productInfo, maxProductsToExtract, 3);
    }

    /**
     * 查找相似的Alibaba商品（使用默认参数，无事件ID）
     *
     * @param productInfo Amazon商品信息
     * @deprecated 使用带有eventId参数的方法
     */
    @Deprecated
    public void findSimilarAlibabaProducts(Map<String, Object> productInfo) {
        // 使用null作为事件ID，这将导致不会回写数据到captain-service
        findSimilarAlibabaProducts(null, productInfo, 5, 3);
    }

    /**
     * 获取并填充阿里巴巴商品的详情信息，并回写到captain-service
     *
     * @param topProducts 排序后的阿里巴巴商品列表
     * @param eventId 事件ID
     * @param amazonAsin Amazon商品ASIN
     */
    private void enrichAlibabaProductDetails(List<Map<String, Object>> topProducts, Long eventId, String amazonAsin) {
        if (topProducts == null || topProducts.isEmpty()) {
            log.info("没有阿里巴巴商品需要填充详情信息");
            return;
        }

        log.info("开始获取并填充 {} 个阿里巴巴商品的详情信息", topProducts.size());

        // 不使用try-with-resources，避免在处理完响应前关闭资源
        AlibabaClient alibabaClient = new AlibabaClient();
        try {
            // 遍历商品列表
            for (Map<String, Object> product : topProducts) {
                try {
                    // 获取商品详情页URL
                    String detailUrl = (String) product.get("detailUrl");
                    if (detailUrl == null || detailUrl.isEmpty()) {
                        log.warn("商品缺少详情页URL，无法获取详情信息");
                        continue;
                    }

                    log.info("获取商品详情信息: {}", detailUrl);

                    // 调用AlibabaClient的openProductDetailPage方法获取商品详情
                    Map<String, String> productDetails = alibabaClient.openProductDetailPage(detailUrl);

                    // 检查是否获取成功
                    if (productDetails.containsKey("error")) {
                        log.error("获取商品详情信息失败: {}", productDetails.get("error"));
                        continue;
                    }

                    // 将商品属性和包装信息填充到商品对象中
                    product.put("productAttributes", productDetails.get("productAttributes"));
                    product.put("packagingInfo", productDetails.get("packagingInfo"));

                    log.info("成功填充商品详情信息: {}", detailUrl);

                    // 添加短暂延迟，避免请求过于频繁
                    Thread.sleep(1000);

                } catch (Exception e) {
                    log.error("处理商品详情信息时出错: {}", e.getMessage());
                }
            }

            // 填充完成后，调用captain-service的回写接口
            if (eventId != null && amazonAsin != null) {
                try {
                    callSupplyCallback(eventId, amazonAsin, topProducts);
                } catch (Exception e) {
                    log.error("调用1688货源回写接口时出错: {}", e.getMessage(), e);
                }
            }
        } catch (Exception e) {
            log.error("处理阿里巴巴商品详情时出错: {}", e.getMessage());
        } finally {
            try {
                // 确保资源被关闭
                alibabaClient.close();
                log.info("AlibabaClient已关闭");
            } catch (Exception e) {
                log.error("关闭AlibabaClient时出错: {}", e.getMessage());
            }
        }

        log.info("完成阿里巴巴商品详情信息填充");
    }

    /**
     * 调用captain-service的1688货源回写接口
     *
     * @param eventId 事件ID
     * @param amazonAsin Amazon商品ASIN
     * @param supplies 1688货源列表
     */
    private void callSupplyCallback(Long eventId, String amazonAsin, List<Map<String, Object>> supplies) {
        if (supplies == null || supplies.isEmpty()) {
            log.warn("没有1688货源数据需要回写");
            return;
        }

        log.info("开始回写1688货源数据: eventId={}, amazonAsin={}, 数量={}", eventId, amazonAsin, supplies.size());

        try {
            // 获取JWT令牌
            String token = authService.getToken();
            if (token == null) {
                log.error("获取JWT令牌失败，无法回写1688货源数据");
                return;
            }

            // 构建请求URL - 添加/api前缀以匹配网关路由配置
            String url = appConfig.getGateway().getUrl() + "/api/captain/callback/" + eventId + "/supply/" + amazonAsin;

            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("supplies", supplies);
            String requestBodyJson = objectMapper.writeValueAsString(requestBody);

            // 发送POST请求
            HttpClient client = HttpClient.newHttpClient();
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(url))
                    .header("Content-Type", "application/json")
                    .header("Authorization", "Bearer " + token)
                    .POST(HttpRequest.BodyPublishers.ofString(requestBodyJson))
                    .build();

            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());

            // 检查响应状态
            if (response.statusCode() >= 200 && response.statusCode() < 300) {
                log.info("1688货源数据回写成功: {}", response.body());
            } else {
                log.error("1688货源数据回写失败: 状态码={}, 响应={}", response.statusCode(), response.body());
            }
        } catch (Exception e) {
            log.error("回写1688货源数据时出错: {}", e.getMessage(), e);
        }
    }

    /**
     * 判断是否达到最大商品数
     * 通过收集唯一的ASIN或父ASIN来避免重复计数变体商品
     *
     * @param productInfoList 商品信息列表
     * @param maxProducts 最大商品数
     * @return 是否达到最大商品数
     */
    private boolean isMaxProductsReached(List<Map<String, Object>> productInfoList, int maxProducts) {
        Set<String> asinSet = new HashSet<>();

        // 遍历商品列表，收集唯一的ASIN或父ASIN
        for (Map<String, Object> productInfo : productInfoList) {
            String parentAsin = (String) productInfo.get("parentAsin");
            String asin = (String) productInfo.get("ASIN");

            if (parentAsin != null && !parentAsin.isEmpty()) {
                // 如果有父ASIN，使用父ASIN
                asinSet.add(parentAsin);
            } else if (asin != null) {
                // 否则使用商品自身的ASIN
                asinSet.add(asin);
            }
        }

        // 判断唯一ASIN数量是否达到最大商品数
        // 使用严格大于而不是大于等于，确保在达到最大数量时，还能处理完当前主商品的所有变体
        return asinSet.size() > maxProducts;
    }

    /**
     * 保存Amazon商品主图到本地
     * @param imageUrl 图片URL
     * @param productInfo 商品信息
     * @return 本地图片路径
     */
    private String saveAmazonProductImage(String imageUrl, Map<String, Object> productInfo) {
        try {
            // 获取商品ASIN作为文件名
            String asin = (String) productInfo.get("ASIN");
            if (asin == null || asin.isEmpty()) {
                log.error("无法获取商品ASIN");
                return null;
            }

            // 创建保存目录
            String folderPath = "images/amazon";
            Path folder = Paths.get(folderPath);
            if (!Files.exists(folder)) {
                Files.createDirectories(folder);
            }

            // 生成文件名
            String fileName = asin + ".jpg";
            Path imagePath = folder.resolve(fileName);

            // 下载图片
            URL url = new URL(imageUrl);
            try (InputStream in = url.openStream()) {
                Files.copy(in, imagePath, StandardCopyOption.REPLACE_EXISTING);
            }

            log.info("已保存Amazon商品图片: {}", imagePath);

            // 将图片路径添加到productInfo
            productInfo.put("localImagePath", imagePath.toString());

            return imagePath.toString();
        } catch (Exception e) {
            log.error("保存Amazon商品图片时出错: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 关闭服务
     * 释放AmazonClient资源
     */
    @Override
    @PreDestroy
    public void close() {
        if (amazonClient != null) {
            try {
                amazonClient.close();
                log.info("AmazonService已关闭");
            } catch (Exception e) {
                log.error("关闭AmazonService时发生错误", e);
            }
        }
    }

    /**
     * 主方法 - 测试processFetchNotification方法
     * 用于测试AmazonService的processFetchNotification功能
     *
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        System.out.println("=== AmazonService.main() 开始执行 ===");

        // 创建必要的依赖对象
        AppConfig appConfig = new AppConfig();
        AppConfig.Gateway gateway = new AppConfig.Gateway();
        gateway.setUrl("http://10.6.5.198:8080");
        appConfig.setGateway(gateway);

        AppConfig.Auth auth = new AppConfig.Auth();
        auth.setUsername("admin");
        auth.setPassword("Test@123");
        appConfig.setAuth(auth);

        ObjectMapper objectMapper = new ObjectMapper();
        AuthService authService = new AuthService();

        // 使用反射设置authService的依赖
        try {
            java.lang.reflect.Field appConfigField = AuthService.class.getDeclaredField("appConfig");
            appConfigField.setAccessible(true);
            appConfigField.set(authService, appConfig);

            java.lang.reflect.Field objectMapperField = AuthService.class.getDeclaredField("objectMapper");
            objectMapperField.setAccessible(true);
            objectMapperField.set(authService, objectMapper);
        } catch (Exception e) {
            System.err.println("设置AuthService依赖时出错: " + e.getMessage());
            e.printStackTrace();
            return;
        }

        // 测试获取JWT令牌
        System.out.println("\n开始测试获取JWT令牌...");
        String token = authService.getToken();
        if (token == null) {
            System.err.println("获取JWT令牌失败");
        } else {
            System.out.println("获取JWT令牌成功: " + token.substring(0, Math.min(token.length(), 20)) + "...");
        }

        // 如果需要继续测试processFetchNotification方法，取消下面的注释

        if (token != null) {
            System.out.println("\n开始测试AmazonService的processFetchNotification方法...");

            // 测试参数
            long eventId = 1;
            // 变体商品
            String url = "https://www.amazon.co.uk/s?k=Smoothie+Maker&me=AF8Y33GFMB0TG&ref=nb_sb_noss";

            // 创建AmazonService实例
            try (AmazonService amazonService = new AmazonService(appConfig, objectMapper, authService)) {
                System.out.println("初始化AmazonService成功");

                // 确保AmazonClient已初始化会话
                if (amazonService.amazonClient != null) {
                    boolean sessionInitialized = amazonService.amazonClient.initSession();
                    if (!sessionInitialized) {
                        System.err.println("初始化Amazon会话失败，无法继续测试");
                        return;
                    }
                    System.out.println("Amazon会话初始化成功");
                } else {
                    System.err.println("AmazonClient为null，无法继续测试");
                    return;
                }

                // 调用processFetchNotification方法
                System.out.println("开始处理fetch通知...");
                System.out.println("事件ID: " + eventId);
                System.out.println("URL: " + url);

                Map<String, Object> result = amazonService.processFetchNotification(eventId, url);

                // 打印处理结果
                System.out.println("\n处理结果:");
                if (result.containsKey("error")) {
                    System.err.println("处理出错: " + result.get("error"));
                } else {
                    System.out.println("处理成功!");
                    System.out.println("页面数: " + result.get("page_count"));
                    System.out.println("商品数: " + result.get("product_count"));
                    System.out.println("回调次数: " + result.get("callback_count"));
                }
            } catch (Exception e) {
                System.err.println("测试过程中发生异常: " + e.getMessage());
                e.printStackTrace();
            }
        }


        System.out.println("\nAmazonService测试完成");
    }
}
