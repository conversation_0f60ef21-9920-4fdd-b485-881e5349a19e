package com.example.captain.util;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * EAN编码工具类测试
 */
class EanCodeUtilTest {

    @Test
    void testGenerateEAN13_ValidInput() {
        // 测试有效输入
        String eanCode = EanCodeUtil.generateEAN13("945", "3926", "53829");
        assertNotNull(eanCode);
        assertEquals(13, eanCode.length());
        assertTrue(eanCode.matches("\\d{13}"));

        // 验证生成的EAN码
        assertTrue(EanCodeUtil.validateEAN13(eanCode));

        System.out.println("生成的EAN-13编码: " + eanCode);
    }

    @Test
    void testGenerateEAN13_NewZealandExample() {
        // 测试新西兰示例（参考JavaScript代码）
        String countryCode = "945";
        String manufacturerCode = "3926";
        String productCode = "53829";

        String eanCode = EanCodeUtil.generateEAN13(countryCode, manufacturerCode, productCode);
        assertNotNull(eanCode);
        assertEquals("9453926538293", eanCode); // 通过JavaScript验证的正确结果

        // 验证生成的EAN码
        assertTrue(EanCodeUtil.validateEAN13(eanCode));
    }

    @Test
    void testGenerateEAN13FromEAN12() {
        String ean12 = "945392653829";
        String ean13 = EanCodeUtil.generateEAN13FromEAN12(ean12);

        assertNotNull(ean13);
        assertEquals(13, ean13.length());
        assertEquals("9453926538293", ean13); // 修正为正确的校验码

        // 验证生成的EAN码
        assertTrue(EanCodeUtil.validateEAN13(ean13));
    }

    @Test
    void testValidateEAN13_ValidCode() {
        // 测试有效的EAN-13编码
        assertTrue(EanCodeUtil.validateEAN13("9453926538293"));
    }

    @Test
    void testValidateEAN13_InvalidCode() {
        // 测试无效的EAN-13编码
        assertFalse(EanCodeUtil.validateEAN13("9453926538297")); // 错误的校验码
        assertFalse(EanCodeUtil.validateEAN13("123456789012")); // 长度不对
        assertFalse(EanCodeUtil.validateEAN13("abcdefghijklm")); // 非数字
        assertFalse(EanCodeUtil.validateEAN13("")); // 空字符串
        assertFalse(EanCodeUtil.validateEAN13(null)); // null
    }

    @Test
    void testGenerateEAN13_InvalidInput() {
        // 测试无效输入
        assertNull(EanCodeUtil.generateEAN13("", "3926", "53829")); // 空国家代码
        assertNull(EanCodeUtil.generateEAN13("945", "", "53829")); // 空厂商代码
        assertNull(EanCodeUtil.generateEAN13("945", "3926", "")); // 空产品代码
        assertNull(EanCodeUtil.generateEAN13("94", "3926", "53829")); // 国家代码太短
        assertNull(EanCodeUtil.generateEAN13("9456", "3926", "53829")); // 国家代码太长
        assertNull(EanCodeUtil.generateEAN13("945", "392", "53829")); // 厂商代码太短
        assertNull(EanCodeUtil.generateEAN13("945", "392678", "53829")); // 厂商代码太长
        assertNull(EanCodeUtil.generateEAN13("945", "3926", "5382")); // 产品代码太短
        assertNull(EanCodeUtil.generateEAN13("945", "3926", "538296")); // 产品代码太长
    }

    @Test
    void testFormatNumber() {
        assertEquals("00001", EanCodeUtil.formatNumber(1, 5));
        assertEquals("00123", EanCodeUtil.formatNumber(123, 5));
        assertEquals("12345", EanCodeUtil.formatNumber(12345, 5));
        assertEquals("123456", EanCodeUtil.formatNumber(123456, 5)); // 超过长度不截断
    }

    @Test
    void testBatchGeneration() {
        String[] eanCodes = EanCodeUtil.generateBatchEAN13("945", "3926", 0, 5);

        assertEquals(5, eanCodes.length);

        for (int i = 0; i < eanCodes.length; i++) {
            assertNotNull(eanCodes[i]);
            assertEquals(13, eanCodes[i].length());
            assertTrue(EanCodeUtil.validateEAN13(eanCodes[i]));
            System.out.println("批量生成EAN-" + i + ": " + eanCodes[i]);
        }
    }

    @Test
    void testParseEAN13() {
        String eanCode = "9453926538293";
        EanCodeUtil.EanCodeInfo info = EanCodeUtil.parseEAN13(eanCode);

        assertNotNull(info);
        assertEquals("945", info.getCountryCode());
        assertEquals("39265", info.getManufacturerCode());
        assertEquals("3829", info.getProductCode());
        assertEquals("3", info.getCheckDigit());
        assertEquals(eanCode, info.getFullCode());

        System.out.println("解析结果: " + info.toString());
    }

    @Test
    void testParseEAN13_InvalidCode() {
        EanCodeUtil.EanCodeInfo info = EanCodeUtil.parseEAN13("1234567890123");
        assertNull(info); // 无效的EAN码应该返回null
    }

    @Test
    void testSequentialGeneration() {
        // 测试连续生成EAN编码（模拟实际使用场景）
        String countryCode = "690"; // 中国
        String manufacturerCode = "1234"; // 修正为4位厂商代码

        for (int i = 0; i < 10; i++) {
            String productCode = EanCodeUtil.formatNumber(i, 5);
            String eanCode = EanCodeUtil.generateEAN13(countryCode, manufacturerCode, productCode);

            assertNotNull(eanCode);
            assertTrue(EanCodeUtil.validateEAN13(eanCode));

            System.out.println("序列" + i + ": " + eanCode);
        }
    }

    @Test
    void testDifferentCountryCodes() {
        // 测试不同国家代码
        String[][] testCases = {
            {"690", "中国", "1234"},
            {"945", "新西兰", "3926"},
            {"400", "德国", "1234"},
            {"300", "法国", "1234"},
            {"500", "英国", "1234"} // 修正英国代码为3位
        };

        String productCode = "67890";

        for (String[] testCase : testCases) {
            String countryCode = testCase[0];
            String countryName = testCase[1];
            String manufacturerCode = testCase[2];

            String eanCode = EanCodeUtil.generateEAN13(countryCode, manufacturerCode, productCode);

            if (eanCode != null) {
                assertTrue(EanCodeUtil.validateEAN13(eanCode));
                System.out.println(countryName + "(" + countryCode + "): " + eanCode);
            } else {
                System.out.println(countryName + "(" + countryCode + "): 生成失败");
            }
        }
    }
}
