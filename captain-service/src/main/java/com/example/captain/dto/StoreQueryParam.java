package com.example.captain.dto;

import lombok.Data;

/**
 * 店铺查询参数对象
 */
@Data
public class StoreQueryParam {

    /**
     * 页码（从1开始）
     */
    private Integer page = 1;

    /**
     * 每页大小
     */
    private Integer size = 10;

    /**
     * 店铺名称（模糊查询）
     */
    private String storeName;

    /**
     * 用户名（模糊查询）
     */
    private String username;

    /**
     * 状态：1-正常，0-禁用
     */
    private Integer status;

    /**
     * 北美站品牌（模糊查询）
     */
    private String northAmericaBrand;

    /**
     * 欧洲站品牌（模糊查询）
     */
    private String europeBrand;

    /**
     * EAN国家代码
     */
    private String eanCountryCode;
}
