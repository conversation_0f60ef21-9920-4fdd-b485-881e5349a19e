package com.example.captain.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.captain.entity.Goods;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * Amazon待发布商品Mapper接口
 */
@Mapper
public interface GoodsMapper extends BaseMapper<Goods> {

    /**
     * 根据事件ID查询商品列表
     *
     * @param eventId 事件ID
     * @return 商品列表
     */
    @Select("SELECT * FROM goods WHERE event_id = #{eventId}")
    List<Goods> findByEventId(@Param("eventId") Long eventId);

    /**
     * 根据Amazon ASIN查询商品列表
     *
     * @param amazonAsin Amazon ASIN
     * @return 商品列表
     */
    @Select("SELECT * FROM goods WHERE amazon_asin = #{amazonAsin}")
    List<Goods> findByAmazonAsin(@Param("amazonAsin") String amazonAsin);

    /**
     * 根据事件ID和Amazon ASIN查询商品
     *
     * @param eventId 事件ID
     * @param amazonAsin Amazon ASIN
     * @return 商品
     */
    @Select("SELECT * FROM goods WHERE event_id = #{eventId} AND amazon_asin = #{amazonAsin} LIMIT 1")
    Goods findByEventIdAndAmazonAsin(@Param("eventId") Long eventId, @Param("amazonAsin") String amazonAsin);

    /**
     * 根据卖家SKU查询商品
     *
     * @param itemSku 卖家SKU
     * @return 商品
     */
    @Select("SELECT * FROM goods WHERE item_sku = #{itemSku} LIMIT 1")
    Goods findByItemSku(@Param("itemSku") String itemSku);

    /**
     * 根据事件ID、Amazon ASIN和parent_child类型查询子商品
     *
     * @param eventId 事件ID
     * @param amazonAsin Amazon ASIN
     * @return 子商品
     */
    @Select("SELECT * FROM goods WHERE event_id = #{eventId} AND amazon_asin = #{amazonAsin} AND parent_child = 'child' LIMIT 1")
    Goods findChildGoodsByEventIdAndAmazonAsin(@Param("eventId") Long eventId, @Param("amazonAsin") String amazonAsin);

    /**
     * 根据主商品ASIN查询相关的所有商品（主商品+子商品），主商品排在前面
     *
     * @param mainAsin 主商品ASIN
     * @return 商品列表，主商品在前，子商品在后
     */
    @Select("SELECT * FROM goods WHERE amazon_asin = #{mainAsin} OR " +
            "(parent_sku = (SELECT item_sku FROM goods WHERE amazon_asin = #{mainAsin} AND parent_child = 'parent' LIMIT 1)) " +
            "ORDER BY CASE WHEN parent_child = 'parent' THEN 0 ELSE 1 END, id")
    List<Goods> findGoodsByMainAsin(@Param("mainAsin") String mainAsin);
}
