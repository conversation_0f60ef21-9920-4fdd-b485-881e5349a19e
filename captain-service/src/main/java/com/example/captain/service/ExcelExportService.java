package com.example.captain.service;

import com.example.captain.converter.GoodsToExcelConverter;
import com.example.captain.converter.ProductToExcelConverter;
import com.example.captain.dto.AmazonProductExcelData;
import com.example.captain.entity.Goods;
import com.example.captain.entity.Product;
import com.example.captain.service.ProductService;
import com.example.captain.util.ExcelTemplateUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Excel导出服务
 * 提供产品数据的Excel导出功能
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ExcelExportService {

    private final ProductToExcelConverter productConverter;
    private final GoodsToExcelConverter goodsConverter;
    private final ProductService productService;

    /**
     * 导出产品列表到Excel文件
     * @param products 产品列表
     * @param outputPath 输出文件路径，如果为null则生成临时文件
     * @return 生成的Excel文件路径
     */
    public String exportProductsToExcel(List<Product> products, String outputPath) {
        if (products == null || products.isEmpty()) {
            throw new IllegalArgumentException("产品列表不能为空");
        }

        log.info("开始导出{}个产品到Excel文件", products.size());

        ExcelTemplateUtil excelUtil = new ExcelTemplateUtil();

        try {
            // 1. 加载Excel模板
            if (!excelUtil.loadTemplate()) {
                throw new RuntimeException("加载Excel模板失败");
            }

            // 2. 转换产品数据
            List<AmazonProductExcelData> excelDataList = productConverter.convertToExcelDataList(products);
            log.info("成功转换{}个产品数据", excelDataList.size());

            // 3. 写入Excel数据
            excelUtil.writeProductsData(excelDataList);

            // 4. 生成Excel文件
            String filePath = excelUtil.generateExcelFile(outputPath);

            log.info("Excel文件导出成功: {}", filePath);
            return filePath;

        } catch (Exception e) {
            log.error("导出Excel文件失败: {}", e.getMessage(), e);
            throw new RuntimeException("导出Excel文件失败: " + e.getMessage(), e);
        } finally {
            // 5. 关闭资源
            excelUtil.close();
        }
    }

    /**
     * 导出单个产品到Excel文件
     * @param product 产品
     * @param outputPath 输出文件路径，如果为null则生成临时文件
     * @return 生成的Excel文件路径
     */
    public String exportProductToExcel(Product product, String outputPath) {
        if (product == null) {
            throw new IllegalArgumentException("产品不能为空");
        }

        return exportProductsToExcel(List.of(product), outputPath);
    }

    /**
     * 根据事件ID导出产品到Excel文件
     * @param eventId 事件ID
     * @param outputPath 输出文件路径，如果为null则生成临时文件
     * @return 生成的Excel文件路径
     */
    public String exportProductsByEventId(Long eventId, String outputPath) {
        if (eventId == null) {
            throw new IllegalArgumentException("事件ID不能为空");
        }

        log.info("根据事件ID导出产品到Excel: eventId={}", eventId);

        // 获取事件相关的产品数据
        List<Product> products = productService.getProductsByEventId(eventId);
        if (products.isEmpty()) {
            throw new IllegalArgumentException("事件ID " + eventId + " 下没有找到产品数据");
        }

        return exportProductsToExcel(products, outputPath);
    }

    /**
     * 生成Excel模板文件（仅包含头部信息，无数据）
     * @param outputPath 输出文件路径，如果为null则生成临时文件
     * @return 生成的Excel文件路径
     */
    public String generateEmptyTemplate(String outputPath) {
        log.info("生成空的Excel模板文件");

        ExcelTemplateUtil excelUtil = new ExcelTemplateUtil();

        try {
            // 1. 加载Excel模板
            if (!excelUtil.loadTemplate()) {
                throw new RuntimeException("加载Excel模板失败");
            }

            // 2. 直接生成文件（不写入任何数据）
            String filePath = excelUtil.generateExcelFile(outputPath);

            log.info("空Excel模板文件生成成功: {}", filePath);
            return filePath;

        } catch (Exception e) {
            log.error("生成空Excel模板文件失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成空Excel模板文件失败: " + e.getMessage(), e);
        } finally {
            // 3. 关闭资源
            excelUtil.close();
        }
    }

    /**
     * 验证产品数据是否适合导出到Excel
     * @param product 产品
     * @return 验证结果消息，如果为null表示验证通过
     */
    public String validateProductForExport(Product product) {
        if (product == null) {
            return "产品不能为空";
        }

        if (product.getAsin() == null || product.getAsin().trim().isEmpty()) {
            return "产品ASIN不能为空";
        }

        if (product.getTitle() == null || product.getTitle().trim().isEmpty()) {
            return "产品标题不能为空";
        }

        // 可以添加更多验证规则
        return null; // 验证通过
    }

    /**
     * 批量验证产品数据
     * @param products 产品列表
     * @return 验证失败的产品信息列表
     */
    public List<String> validateProductsForExport(List<Product> products) {
        if (products == null || products.isEmpty()) {
            return List.of("产品列表不能为空");
        }

        return products.stream()
                .map(this::validateProductForExport)
                .filter(message -> message != null)
                .toList();
    }

    // ========== 基于Goods的导出方法 ==========

    /**
     * 导出商品列表到Excel文件
     * @param goodsList 商品列表
     * @param outputPath 输出文件路径，如果为null则生成临时文件
     * @return 生成的Excel文件路径
     */
    public String exportGoodsToExcel(List<Goods> goodsList, String outputPath) {
        if (goodsList == null || goodsList.isEmpty()) {
            throw new IllegalArgumentException("商品列表不能为空");
        }

        log.info("开始导出{}个商品到Excel文件", goodsList.size());

        ExcelTemplateUtil excelUtil = new ExcelTemplateUtil();

        try {
            // 1. 加载Excel模板
            if (!excelUtil.loadTemplate()) {
                throw new RuntimeException("加载Excel模板失败");
            }

            // 2. 转换商品数据
            List<AmazonProductExcelData> excelDataList = goodsConverter.convertToExcelDataList(goodsList);
            log.info("成功转换{}个商品数据", excelDataList.size());

            // 3. 写入Excel数据
            excelUtil.writeProductsData(excelDataList);

            // 4. 生成Excel文件
            String filePath = excelUtil.generateExcelFile(outputPath);

            log.info("Excel文件导出成功: {}", filePath);
            return filePath;

        } catch (Exception e) {
            log.error("导出Excel文件失败: {}", e.getMessage(), e);
            throw new RuntimeException("导出Excel文件失败: " + e.getMessage(), e);
        } finally {
            // 5. 关闭资源
            excelUtil.close();
        }
    }

    /**
     * 验证商品数据是否适合导出到Excel
     * @param goods 商品
     * @return 验证结果消息，如果为null表示验证通过
     */
    public String validateGoodsForExport(Goods goods) {
        if (goods == null) {
            return "商品不能为空";
        }

        if (goods.getAmazonAsin() == null || goods.getAmazonAsin().trim().isEmpty()) {
            return "商品ASIN不能为空";
        }

        if (goods.getItemName() == null || goods.getItemName().trim().isEmpty()) {
            return "商品名称不能为空";
        }

        if (goods.getItemSku() == null || goods.getItemSku().trim().isEmpty()) {
            return "商品SKU不能为空";
        }

        // 可以添加更多验证规则
        return null; // 验证通过
    }

    /**
     * 批量验证商品数据
     * @param goodsList 商品列表
     * @return 验证失败的商品信息列表
     */
    public List<String> validateGoodsForExport(List<Goods> goodsList) {
        if (goodsList == null || goodsList.isEmpty()) {
            return List.of("商品列表不能为空");
        }

        return goodsList.stream()
                .map(this::validateGoodsForExport)
                .filter(message -> message != null)
                .toList();
    }
}
