#!/bin/bash

# 进入当前目录
cd "$(dirname "$0")"

# 显示脚本说明
echo "====================================================="
echo "  构建并运行支持中文终端的worker-service Docker容器"
echo "====================================================="
echo "此脚本将："
echo "1. 构建一个完全支持中文的Docker镜像，包含更好的终端"
echo "2. 运行容器并挂载当前目录"
echo "3. 启动VNC服务器和noVNC Web界面"
echo "4. 自动启动支持中文的终端"
echo "====================================================="

# 检查是否在worker-service目录
if [[ "$(basename $(pwd))" != "worker-service" && ! -d "worker-service" ]]; then
  echo "错误：请在worker-service目录或其父目录中运行此脚本"
  exit 1
fi

# 如果在父目录中，进入worker-service目录
if [[ -d "worker-service" ]]; then
  cd worker-service
  echo "已进入worker-service目录"
fi

# 构建Docker镜像
echo "正在构建Docker镜像..."
docker build -t worker-service-chinese-terminal -f Dockerfile.chinese-support-terminal .

# 检查构建是否成功
if [ $? -ne 0 ]; then
  echo "错误：Docker镜像构建失败！"
  exit 1
fi

echo "Docker镜像构建成功！"

# 获取当前目录的绝对路径
CURRENT_DIR=$(pwd)

# 运行Docker容器
echo "正在运行Docker容器..."
docker run --rm -it --privileged \
  -p 5900:5900 -p 7070:8080 \
  -e DISPLAY_WIDTH=1900 \
  -e DISPLAY_HEIGHT=1000 \
  -e VNC_PASSWORD=password \
  -e TZ=Asia/Shanghai \
  --shm-size=2g \
  -v "${CURRENT_DIR}:/app" \
  --name worker-service-chinese-terminal \
  worker-service-chinese-terminal

echo "容器已停止运行"
