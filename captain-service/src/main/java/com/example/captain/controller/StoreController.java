package com.example.captain.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.example.captain.dto.*;
import com.example.captain.service.StoreService;
import com.example.common.model.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 店铺信息控制器
 */
@RestController
@RequestMapping("/captain/stores")
@Slf4j
public class StoreController {

    private final StoreService storeService;

    @Autowired
    public StoreController(StoreService storeService) {
        this.storeService = storeService;
    }

    /**
     * 分页查询店铺列表
     * 支持按店铺名称、用户名、状态、品牌、EAN国家代码等条件过滤
     * 结果按id倒序排列
     *
     * @param param 查询参数
     * @return 分页结果
     */
    @GetMapping
    public ApiResponse<IPage<StoreDTO>> getStorePage(StoreQueryParam param) {
        log.info("分页查询店铺列表: param={}", param);
        IPage<StoreDTO> page = storeService.getStorePage(param);
        return ApiResponse.success(page);
    }

    /**
     * 根据ID获取店铺详情
     *
     * @param id 店铺ID
     * @return 店铺详情
     */
    @GetMapping("/{id}")
    public ApiResponse<StoreDTO> getStoreById(@PathVariable Long id) {
        log.info("根据ID获取店铺详情: id={}", id);
        StoreDTO store = storeService.getStoreDTOById(id);
        return ApiResponse.success(store);
    }

    /**
     * 根据店铺名称获取店铺详情
     *
     * @param storeName 店铺名称
     * @return 店铺详情
     */
    @GetMapping("/by-name/{storeName}")
    public ApiResponse<StoreDTO> getStoreByName(@PathVariable String storeName) {
        log.info("根据店铺名称获取店铺详情: storeName={}", storeName);
        StoreDTO store = storeService.getStoreByName(storeName);
        return ApiResponse.success(store);
    }

    /**
     * 创建店铺
     *
     * @param request 创建请求
     * @return 创建的店铺信息
     */
    @PostMapping
    public ApiResponse<StoreDTO> createStore(@Valid @RequestBody StoreCreateRequest request) {
        log.info("创建店铺: request={}", request);
        StoreDTO store = storeService.createStore(request);
        return ApiResponse.success(store);
    }

    /**
     * 更新店铺信息
     *
     * @param id 店铺ID
     * @param request 更新请求
     * @return 更新后的店铺信息
     */
    @PutMapping("/{id}")
    public ApiResponse<StoreDTO> updateStore(@PathVariable Long id, 
                                           @Valid @RequestBody StoreUpdateRequest request) {
        log.info("更新店铺信息: id={}, request={}", id, request);
        StoreDTO store = storeService.updateStore(id, request);
        return ApiResponse.success(store);
    }

    /**
     * 更新店铺状态
     *
     * @param id 店铺ID
     * @param request 状态更新请求
     * @return 操作结果
     */
    @PutMapping("/{id}/status")
    public ApiResponse<Void> updateStoreStatus(@PathVariable Long id, 
                                             @Valid @RequestBody StoreStatusUpdateRequest request) {
        log.info("更新店铺状态: id={}, status={}", id, request.getStatus());
        storeService.updateStoreStatus(id, request);
        return ApiResponse.success();
    }

    /**
     * 删除店铺（软删除）
     *
     * @param id 店铺ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteStore(@PathVariable Long id) {
        log.info("删除店铺: id={}", id);
        storeService.deleteStore(id);
        return ApiResponse.success();
    }

    /**
     * 获取下一个EAN产品代码
     *
     * @param id 店铺ID
     * @return 下一个EAN产品代码
     */
    @GetMapping("/{id}/ean-next-code")
    public ApiResponse<String> getNextEanProductCode(@PathVariable Long id) {
        log.info("获取下一个EAN产品代码: id={}", id);
        String nextCode = storeService.getNextEanProductCode(id);
        return ApiResponse.success(nextCode);
    }
}
