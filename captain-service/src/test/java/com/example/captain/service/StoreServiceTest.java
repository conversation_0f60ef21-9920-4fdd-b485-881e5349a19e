package com.example.captain.service;

import com.example.captain.dto.StoreCreateRequest;
import com.example.captain.dto.StoreDTO;
import com.example.captain.dto.StoreQueryParam;
import com.example.captain.entity.Store;
import com.example.captain.mapper.StoreMapper;
import com.example.captain.service.impl.StoreServiceImpl;
import com.example.common.exception.BusinessException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 店铺服务测试类
 */
@ExtendWith(MockitoExtension.class)
class StoreServiceTest {

    @Mock
    private StoreMapper storeMapper;

    @InjectMocks
    private StoreServiceImpl storeService;

    private StoreCreateRequest createRequest;
    private Store store;

    @BeforeEach
    void setUp() {
        createRequest = new StoreCreateRequest();
        createRequest.setStoreName("测试店铺");
        createRequest.setUsername("testuser");
        createRequest.setPassword("password123");
        createRequest.setStoreUrl("https://amazon.com/stores/test");
        createRequest.setNorthAmericaBrand("TestBrand US");
        createRequest.setEuropeBrand("TestBrand EU");
        createRequest.setEanCountryCode("690");
        createRequest.setEanManufacturerCode("12345");
        createRequest.setEanProductCodeCurrent("67890");

        store = new Store();
        store.setId(1L);
        store.setStoreName("测试店铺");
        store.setUsername("testuser");
        store.setPassword("$2a$10$encoded_password");
        store.setStoreUrl("https://amazon.com/stores/test");
        store.setNorthAmericaBrand("TestBrand US");
        store.setEuropeBrand("TestBrand EU");
        store.setEanCountryCode("690");
        store.setEanManufacturerCode("12345");
        store.setEanProductCodeCurrent("67890");
        store.setStatus(1);
        store.setDeleted(0);
    }

    @Test
    void testCreateStore_Success() {
        // Given
        when(storeMapper.findByStoreName(createRequest.getStoreName())).thenReturn(null);
        when(storeMapper.findByUsername(createRequest.getUsername())).thenReturn(null);
        when(storeMapper.insert(any(Store.class))).thenReturn(1);

        // When
        StoreDTO result = storeService.createStore(createRequest);

        // Then
        assertNotNull(result);
        assertEquals(createRequest.getStoreName(), result.getStoreName());
        assertEquals(createRequest.getUsername(), result.getUsername());
        assertEquals(createRequest.getStoreUrl(), result.getStoreUrl());
        
        verify(storeMapper).findByStoreName(createRequest.getStoreName());
        verify(storeMapper).findByUsername(createRequest.getUsername());
        verify(storeMapper).insert(any(Store.class));
    }

    @Test
    void testCreateStore_StoreNameExists() {
        // Given
        when(storeMapper.findByStoreName(createRequest.getStoreName())).thenReturn(store);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> storeService.createStore(createRequest));
        
        assertEquals("店铺名称已存在", exception.getMessage());
        verify(storeMapper).findByStoreName(createRequest.getStoreName());
        verify(storeMapper, never()).insert(any(Store.class));
    }

    @Test
    void testCreateStore_UsernameExists() {
        // Given
        when(storeMapper.findByStoreName(createRequest.getStoreName())).thenReturn(null);
        when(storeMapper.findByUsername(createRequest.getUsername())).thenReturn(store);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> storeService.createStore(createRequest));
        
        assertEquals("用户名已存在", exception.getMessage());
        verify(storeMapper).findByStoreName(createRequest.getStoreName());
        verify(storeMapper).findByUsername(createRequest.getUsername());
        verify(storeMapper, never()).insert(any(Store.class));
    }

    @Test
    void testGetNextEanProductCode_Success() {
        // Given
        Long storeId = 1L;
        store.setEanProductCodeCurrent("12345");
        when(storeMapper.selectOne(any())).thenReturn(store);
        when(storeMapper.updateEanProductCodeCurrent(storeId, "12346")).thenReturn(1);

        // When
        String nextCode = storeService.getNextEanProductCode(storeId);

        // Then
        assertEquals("12346", nextCode);
        verify(storeMapper).selectOne(any());
        verify(storeMapper).updateEanProductCodeCurrent(storeId, "12346");
    }

    @Test
    void testGetNextEanProductCode_InvalidFormat() {
        // Given
        Long storeId = 1L;
        store.setEanProductCodeCurrent("invalid");
        when(storeMapper.selectOne(any())).thenReturn(store);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> storeService.getNextEanProductCode(storeId));
        
        assertEquals("当前EAN产品代码格式不正确，必须为数字", exception.getMessage());
        verify(storeMapper).selectOne(any());
        verify(storeMapper, never()).updateEanProductCodeCurrent(any(), any());
    }
}
