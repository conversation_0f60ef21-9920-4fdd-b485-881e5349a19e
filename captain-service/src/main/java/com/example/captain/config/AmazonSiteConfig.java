package com.example.captain.config;

import com.example.captain.enums.AmazonSite;
import com.example.captain.enums.SiteRegion;
import com.example.captain.model.CountryInfo;
import com.example.captain.model.CurrencyInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * Amazon站点配置类
 * 提供轻量级的站点配置管理功能
 */
@Component
@Slf4j
public class AmazonSiteConfig {
    
    /**
     * 运行时汇率缓存（支持动态更新）
     */
    private final Map<String, BigDecimal> runtimeExchangeRates = new ConcurrentHashMap<>();
    
    /**
     * 汇率更新时间缓存
     */
    private final Map<String, LocalDateTime> exchangeRateUpdateTimes = new ConcurrentHashMap<>();
    
    /**
     * 货币名称映射
     */
    private static final Map<String, String> CURRENCY_NAMES = Map.of(
        "USD", "美元",
        "CAD", "加拿大元", 
        "MXN", "墨西哥比索",
        "GBP", "英镑",
        "EUR", "欧元",
        "SEK", "瑞典克朗",
        "PLN", "波兰兹罗提"
    );
    
    /**
     * 货币符号映射
     */
    private static final Map<String, String> CURRENCY_SYMBOLS = Map.of(
        "USD", "$",
        "CAD", "C$",
        "MXN", "$",
        "GBP", "£",
        "EUR", "€",
        "SEK", "kr",
        "PLN", "zł"
    );
    
    /**
     * 获取所有国家配置
     */
    public List<CountryInfo> getAllCountries() {
        return Arrays.stream(AmazonSite.values())
                .map(this::convertToCountryInfo)
                .sorted(Comparator.comparing(CountryInfo::getSortOrder)
                        .thenComparing(CountryInfo::getCountryName))
                .collect(Collectors.toList());
    }
    
    /**
     * 根据国家代码获取国家配置
     */
    public CountryInfo getCountryByCode(String countryCode) {
        try {
            AmazonSite site = AmazonSite.fromCountryCode(countryCode);
            return convertToCountryInfo(site);
        } catch (IllegalArgumentException e) {
            log.warn("未找到国家代码: {}", countryCode);
            return null;
        }
    }
    
    /**
     * 根据站点区域获取国家列表
     */
    public List<CountryInfo> getCountriesByRegion(SiteRegion siteRegion) {
        return Arrays.stream(AmazonSite.fromSiteRegion(siteRegion))
                .map(this::convertToCountryInfo)
                .sorted(Comparator.comparing(CountryInfo::getCountryName))
                .collect(Collectors.toList());
    }
    
    /**
     * 根据站点区域名称获取国家列表
     */
    public List<CountryInfo> getCountriesByRegionName(String regionName) {
        try {
            SiteRegion region = SiteRegion.fromChineseName(regionName);
            return getCountriesByRegion(region);
        } catch (IllegalArgumentException e) {
            log.warn("未找到站点区域: {}", regionName);
            return Collections.emptyList();
        }
    }
    
    /**
     * 根据货币代码获取国家列表
     */
    public List<CountryInfo> getCountriesByCurrency(String currencyCode) {
        return Arrays.stream(AmazonSite.fromCurrencyCode(currencyCode))
                .map(this::convertToCountryInfo)
                .sorted(Comparator.comparing(CountryInfo::getCountryName))
                .collect(Collectors.toList());
    }
    
    /**
     * 获取所有站点区域
     */
    public List<String> getAllRegions() {
        return Arrays.stream(SiteRegion.values())
                .map(SiteRegion::getChineseName)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取所有货币信息
     */
    public List<CurrencyInfo> getAllCurrencies() {
        Map<String, List<AmazonSite>> currencyGroups = Arrays.stream(AmazonSite.values())
                .collect(Collectors.groupingBy(AmazonSite::getCurrencyCode));
        
        return currencyGroups.entrySet().stream()
                .map(entry -> {
                    String currencyCode = entry.getKey();
                    List<AmazonSite> sites = entry.getValue();
                    
                    // 使用第一个站点的汇率作为该货币的汇率
                    BigDecimal exchangeRate = getCurrentExchangeRate(sites.get(0).getCountryCode());
                    
                    return CurrencyInfo.builder()
                            .currencyCode(currencyCode)
                            .currencyName(CURRENCY_NAMES.getOrDefault(currencyCode, currencyCode))
                            .currencySymbol(CURRENCY_SYMBOLS.getOrDefault(currencyCode, currencyCode))
                            .exchangeRate(exchangeRate)
                            .countryCount(sites.size())
                            .countries(sites.stream()
                                    .map(AmazonSite::getCountryName)
                                    .collect(Collectors.toList()))
                            .decimalPlaces(2)
                            .isMajor(Arrays.asList("USD", "EUR", "GBP").contains(currencyCode))
                            .build();
                })
                .sorted(Comparator.comparing(CurrencyInfo::getCurrencyCode))
                .collect(Collectors.toList());
    }
    
    /**
     * 根据货币代码获取货币信息
     */
    public CurrencyInfo getCurrencyInfo(String currencyCode) {
        return getAllCurrencies().stream()
                .filter(currency -> currency.getCurrencyCode().equalsIgnoreCase(currencyCode))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 更新汇率（运行时）
     */
    public void updateExchangeRate(String countryCode, BigDecimal newRate) {
        if (newRate == null || newRate.compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("汇率必须大于0");
        }
        
        // 验证国家代码是否存在
        try {
            AmazonSite.fromCountryCode(countryCode);
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("未知的国家代码: " + countryCode);
        }
        
        runtimeExchangeRates.put(countryCode.toUpperCase(), newRate);
        exchangeRateUpdateTimes.put(countryCode.toUpperCase(), LocalDateTime.now());
        
        log.info("更新汇率成功: {} = {}", countryCode, newRate);
    }
    
    /**
     * 批量更新汇率
     */
    public void batchUpdateExchangeRates(Map<String, BigDecimal> exchangeRates) {
        for (Map.Entry<String, BigDecimal> entry : exchangeRates.entrySet()) {
            updateExchangeRate(entry.getKey(), entry.getValue());
        }
    }
    
    /**
     * 获取当前汇率（优先使用运行时更新的汇率）
     */
    public BigDecimal getCurrentExchangeRate(String countryCode) {
        String upperCountryCode = countryCode.toUpperCase();
        
        // 优先返回运行时更新的汇率
        if (runtimeExchangeRates.containsKey(upperCountryCode)) {
            return runtimeExchangeRates.get(upperCountryCode);
        }
        
        // 返回默认汇率
        try {
            AmazonSite site = AmazonSite.fromCountryCode(countryCode);
            return site.getDefaultExchangeRate();
        } catch (IllegalArgumentException e) {
            return BigDecimal.ZERO;
        }
    }
    
    /**
     * 重置汇率到默认值
     */
    public void resetExchangeRate(String countryCode) {
        String upperCountryCode = countryCode.toUpperCase();
        runtimeExchangeRates.remove(upperCountryCode);
        exchangeRateUpdateTimes.remove(upperCountryCode);
        
        log.info("重置汇率到默认值: {}", countryCode);
    }
    
    /**
     * 重置所有汇率到默认值
     */
    public void resetAllExchangeRates() {
        runtimeExchangeRates.clear();
        exchangeRateUpdateTimes.clear();
        
        log.info("重置所有汇率到默认值");
    }
    
    /**
     * 转换AmazonSite枚举为CountryInfo对象
     */
    private CountryInfo convertToCountryInfo(AmazonSite site) {
        String countryCode = site.getCountryCode();
        
        return CountryInfo.builder()
                .countryCode(countryCode)
                .countryName(site.getCountryName())
                .currencyCode(site.getCurrencyCode())
                .language(site.getLanguage())
                .siteRegion(site.getSiteRegion())
                .siteRegionName(site.getSiteRegion().getChineseName())
                .exchangeRate(getCurrentExchangeRate(countryCode))
                .amazonDomain(site.getAmazonDomain())
                .marketplaceId(site.getMarketplaceId())
                .exchangeRateUpdatedAt(exchangeRateUpdateTimes.get(countryCode.toUpperCase()))
                .enabled(true)
                .sortOrder(getSortOrder(site))
                .build();
    }
    
    /**
     * 获取排序顺序
     */
    private Integer getSortOrder(AmazonSite site) {
        // 按站点区域和国家重要性排序
        switch (site) {
            case US: return 1;
            case CA: return 2;
            case MX: return 3;
            case UK: return 4;
            case DE: return 5;
            case FR: return 6;
            case IT: return 7;
            case ES: return 8;
            case NL: return 9;
            case SE: return 10;
            case PL: return 11;
            default: return 99;
        }
    }
}
