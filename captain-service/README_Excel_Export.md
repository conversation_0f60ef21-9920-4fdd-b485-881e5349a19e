# Excel导出功能使用说明

## 概述

本功能基于Amazon产品模板（template.xls）实现了商品数据的Excel导出功能，支持将数据库中的Goods表数据转换为Amazon标准格式的Excel文件。

## 重要更新

**注意：此功能已从基于Product表改为基于Goods表实现。**

- 移除了所有基于Product表的API
- 新增基于Goods表的API，支持根据主商品ASIN导出
- 主商品和子商品会按正确顺序排列（主商品在前，子商品在后）

## 功能特性

- **基于官方模板**：使用Amazon官方产品上传模板，确保格式正确
- **自动数据转换**：将数据库产品数据自动转换为Excel格式
- **批量导出**：支持批量导出多个产品
- **灵活映射**：通过配置映射关系，易于维护和扩展
- **文件下载**：支持直接下载生成的Excel文件

## 核心组件

### 1. ExcelFieldMapping
字段映射配置类，定义Amazon产品模板中字段名到列索引的映射关系。

### 2. AmazonProductExcelData
Amazon产品Excel导出数据传输对象，包含所有需要导出到Excel模板的字段。

### 3. ExcelTemplateUtil
Excel模板工具类，提供模板加载、数据写入和文件生成功能。

### 4. GoodsToExcelConverter
商品数据转换器，将数据库中的Goods实体转换为Excel导出格式。

### 5. ExcelExportService
Excel导出服务，提供高级的导出功能。

### 6. ExcelExportController
Excel导出控制器，提供REST API接口。

## API接口

### 1. 根据主商品ASIN导出Excel文件
```
GET /captain/excel/export/asin/{mainAsin}
```
根据亚马逊主商品ASIN导出Excel文件。系统会：
- 查询goods表中与该ASIN相关的所有商品（主商品+子商品）
- 主商品排在前面，子商品排在后面
- 映射数据字段到template.xls模板文件
- 生成对应的待发布商品文件并返回文件流

### 2. 下载空模板
```
GET /captain/excel/template
```
下载空的Excel模板文件。

### 3. 验证商品数据
```
GET /captain/excel/validate/asin/{mainAsin}
```
验证指定主商品ASIN相关的商品数据是否适合导出。

## 使用示例

### 1. 编程方式使用

```java
@Autowired
private ExcelExportService excelExportService;
@Autowired
private GoodsMapper goodsMapper;

// 导出商品列表
List<Goods> goodsList = goodsMapper.findGoodsByMainAsin("B09TEST123");
String filePath = excelExportService.exportGoodsToExcel(goodsList, "/tmp/goods.xls");

// 验证商品数据
List<String> errors = excelExportService.validateGoodsForExport(goodsList);
```

### 2. REST API使用

```bash
# 导出主商品ASIN相关的所有商品
curl -X GET "http://localhost:9095/captain/excel/export/asin/B09TEST123" \
     -H "Accept: application/vnd.ms-excel" \
     -o "amazon_goods_B09TEST123.xls"

# 验证商品数据
curl -X GET "http://localhost:9095/captain/excel/validate/asin/B09TEST123" \
     -H "Accept: application/json"

# 下载模板
curl -X GET "http://localhost:9095/captain/excel/template" \
     -H "Accept: application/vnd.ms-excel" \
     -o "template.xls"
```

## 数据映射说明

### 支持的字段

Excel模板包含62个字段，主要分为以下几类：

1. **基本产品信息**：产品类型、SKU、品牌、ASIN、标题等
2. **价格和库存**：标准价格、库存数量等
3. **图片信息**：主图片、其他图片（最多8张）
4. **变体信息**：父子关系、变体主题等
5. **产品描述**：产品描述、关键特性（最多5个）
6. **产品属性**：颜色、尺寸、材质等
7. **尺寸和重量**：运输重量、产品尺寸等
8. **履行和价格**：履行中心、货币、价格等
9. **合规性信息**：保修、电池、危险品等

### 数据转换规则

- **品牌名称**：从产品标题中提取第一个单词作为品牌
- **产品类型**：根据产品类型映射到Amazon标准类型
- **图片处理**：支持JSON格式的图片URL列表，第一张作为主图
- **特性解析**：支持多种分隔符的产品特性解析
- **SKU生成**：基于ASIN生成标准SKU格式

## 配置说明

### 模板文件位置
```
captain-service/src/main/resources/csv/template.xls
```

### 依赖配置
项目已添加Apache POI依赖：
```xml
<dependency>
    <groupId>org.apache.poi</groupId>
    <artifactId>poi</artifactId>
    <version>5.2.4</version>
</dependency>
<dependency>
    <groupId>org.apache.poi</groupId>
    <artifactId>poi-ooxml</artifactId>
    <version>5.2.4</version>
</dependency>
<dependency>
    <groupId>org.apache.poi</groupId>
    <artifactId>poi-scratchpad</artifactId>
    <version>5.2.4</version>
</dependency>
```

## 错误处理

### 常见错误

1. **模板加载失败**：检查模板文件是否存在
2. **数据转换错误**：检查产品数据完整性
3. **文件生成失败**：检查磁盘空间和权限
4. **字段映射错误**：检查字段名是否正确

### 验证功能

使用验证API可以检查产品数据是否适合导出：
- 检查必填字段（ASIN、标题）
- 统计有效和无效产品数量
- 返回详细的验证错误信息

## 扩展说明

### 添加新字段

1. 在`ExcelFieldMapping`中添加字段映射
2. 在`AmazonProductExcelData`中添加对应属性
3. 在`ProductToExcelConverter`中添加转换逻辑
4. 在`ExcelTemplateUtil`中添加写入逻辑

### 自定义转换规则

可以通过修改`ProductToExcelConverter`中的转换方法来自定义数据转换规则，例如：
- 品牌名称提取规则
- 产品类型映射规则
- 图片URL处理规则
- 特性解析规则

## 测试

项目包含单元测试：
```
captain-service/src/test/java/com/example/captain/util/ExcelTemplateUtilTest.java
```

运行测试：
```bash
cd captain-service
mvn test -Dtest=ExcelTemplateUtilTest
```

## 注意事项

1. **内存使用**：大量产品导出时注意内存使用
2. **文件清理**：临时文件需要定期清理
3. **并发安全**：多线程环境下注意资源竞争
4. **数据验证**：导出前建议先验证数据完整性
5. **模板更新**：Amazon模板更新时需要同步更新字段映射

## 性能优化建议

1. **批量处理**：使用批量导出而非单个导出
2. **异步处理**：大量数据导出建议使用异步处理
3. **缓存机制**：对于重复导出可以考虑缓存
4. **流式处理**：超大数据集可以考虑流式处理
