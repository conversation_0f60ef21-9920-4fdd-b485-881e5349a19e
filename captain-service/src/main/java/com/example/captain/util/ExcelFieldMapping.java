package com.example.captain.util;

import java.util.HashMap;
import java.util.Map;

/**
 * Excel字段映射配置类
 * 定义Amazon产品模板中字段名到列索引的映射关系
 */
public class ExcelFieldMapping {

    /**
     * 字段名到列索引的映射（基于0的索引）
     * 根据template.xls第3行的API字段名建立映射
     */
    public static final Map<String, Integer> FIELD_COLUMN_MAP = new HashMap<>();

    static {
        // 基本产品信息
        FIELD_COLUMN_MAP.put("feed_product_type", 0);           // Product Type
        FIELD_COLUMN_MAP.put("item_sku", 1);                    // Seller SKU
        FIELD_COLUMN_MAP.put("brand_name", 2);                  // Brand Name
        FIELD_COLUMN_MAP.put("external_product_id", 3);         // Product ID (ASIN)
        FIELD_COLUMN_MAP.put("external_product_id_type", 4);    // Product ID Type
        FIELD_COLUMN_MAP.put("item_name", 5);                   // Item Name
        FIELD_COLUMN_MAP.put("manufacturer", 6);                // Manufacturer
        FIELD_COLUMN_MAP.put("recommended_browse_nodes", 7);    // Recommended Browse Nodes
        FIELD_COLUMN_MAP.put("max_order_quantity", 8);          // Max Order Quantity
        FIELD_COLUMN_MAP.put("country_of_origin", 9);           // Country Of Origin
        
        // 价格和库存
        FIELD_COLUMN_MAP.put("standard_price", 10);             // Standard Price
        FIELD_COLUMN_MAP.put("quantity", 11);                   // Quantity
        
        // 图片信息
        FIELD_COLUMN_MAP.put("main_image_url", 12);             // Main Image URL
        FIELD_COLUMN_MAP.put("other_image_url1", 13);           // Other Image Url1
        FIELD_COLUMN_MAP.put("other_image_url2", 14);           // Other Image Url2
        FIELD_COLUMN_MAP.put("other_image_url3", 15);           // Other Image Url3
        FIELD_COLUMN_MAP.put("other_image_url4", 16);           // Other Image Url4
        FIELD_COLUMN_MAP.put("other_image_url5", 17);           // Other Image Url5
        FIELD_COLUMN_MAP.put("other_image_url6", 18);           // Other Image Url6
        FIELD_COLUMN_MAP.put("other_image_url7", 19);           // Other Image Url7
        FIELD_COLUMN_MAP.put("other_image_url8", 20);           // Other Image Url8
        FIELD_COLUMN_MAP.put("swatch_image_url", 21);           // Swatch Image URL
        
        // 变体信息
        FIELD_COLUMN_MAP.put("parent_child", 22);               // Parentage
        FIELD_COLUMN_MAP.put("parent_sku", 23);                 // Parent Sku
        FIELD_COLUMN_MAP.put("relationship_type", 24);          // Relationship Type
        FIELD_COLUMN_MAP.put("variation_theme", 25);            // Variation Theme
        
        // 基本描述信息
        FIELD_COLUMN_MAP.put("update_delete", 26);              // Update Delete
        FIELD_COLUMN_MAP.put("product_description", 27);        // Product Description
        FIELD_COLUMN_MAP.put("part_number", 28);                // Manufacturer Part Number
        
        // 产品特性
        FIELD_COLUMN_MAP.put("bullet_point1", 29);              // Key Product Features 1
        FIELD_COLUMN_MAP.put("bullet_point2", 30);              // Key Product Features 2
        FIELD_COLUMN_MAP.put("bullet_point3", 31);              // Key Product Features 3
        FIELD_COLUMN_MAP.put("bullet_point4", 32);              // Key Product Features 4
        FIELD_COLUMN_MAP.put("bullet_point5", 33);              // Key Product Features 5
        FIELD_COLUMN_MAP.put("generic_keywords", 34);           // Search Terms
        
        // 产品属性
        FIELD_COLUMN_MAP.put("metal_type", 35);                 // Metal Type
        FIELD_COLUMN_MAP.put("department_name", 36);            // Department
        FIELD_COLUMN_MAP.put("gem_type", 37);                   // Gem Type
        FIELD_COLUMN_MAP.put("special_features", 38);           // Additional Features
        FIELD_COLUMN_MAP.put("color_name", 39);                 // Colour
        FIELD_COLUMN_MAP.put("color_map", 40);                  // Colour Map
        FIELD_COLUMN_MAP.put("size_name", 41);                  // Size
        
        // 尺寸和重量
        FIELD_COLUMN_MAP.put("website_shipping_weight", 42);    // Shipping Weight
        FIELD_COLUMN_MAP.put("website_shipping_weight_unit_of_measure", 43); // Website Shipping Weight Unit Of Measure
        FIELD_COLUMN_MAP.put("size_map", 44);                   // Size Map
        FIELD_COLUMN_MAP.put("unit_count", 45);                 // Unit Count
        FIELD_COLUMN_MAP.put("unit_count_type", 46);            // Unit Count Type
        FIELD_COLUMN_MAP.put("depth_front_to_back", 47);        // Item Depth Front To Back
        FIELD_COLUMN_MAP.put("depth_front_to_back_unit_of_measure", 48); // Item depth Unit
        FIELD_COLUMN_MAP.put("depth_width_side_to_side", 49);   // Item Width Side To Side
        FIELD_COLUMN_MAP.put("depth_width_side_to_side_unit_of_measure", 50); // Item Width Unit
        FIELD_COLUMN_MAP.put("depth_height_floor_to_top", 51);  // Item Height Floor To Top
        FIELD_COLUMN_MAP.put("depth_height_floor_to_top_unit_of_measure", 52); // Item Height Unit of Measure
        
        // 履行和价格
        FIELD_COLUMN_MAP.put("fulfillment_center_id", 53);      // Fulfillment Centre ID
        FIELD_COLUMN_MAP.put("currency", 54);                   // Currency
        FIELD_COLUMN_MAP.put("list_price", 55);                 // List Price
        FIELD_COLUMN_MAP.put("list_price_with_tax", 56);        // List Price With Tax
        FIELD_COLUMN_MAP.put("condition_type", 57);             // Item Condition
        
        // 合规性信息
        FIELD_COLUMN_MAP.put("warranty_description", 58);       // Manufacturer Warranty Description
        FIELD_COLUMN_MAP.put("are_batteries_included", 59);     // Batteries are Included
        FIELD_COLUMN_MAP.put("batteries_required", 60);         // Is this product a battery or does it utilize batteries?
        FIELD_COLUMN_MAP.put("supplier_declared_dg_hz_regulation1", 61); // Dangerous Goods Regulations
    }

    /**
     * 根据字段名获取列索引
     * @param fieldName 字段名
     * @return 列索引，如果字段不存在返回-1
     */
    public static int getColumnIndex(String fieldName) {
        return FIELD_COLUMN_MAP.getOrDefault(fieldName, -1);
    }

    /**
     * 检查字段是否存在
     * @param fieldName 字段名
     * @return 是否存在
     */
    public static boolean hasField(String fieldName) {
        return FIELD_COLUMN_MAP.containsKey(fieldName);
    }

    /**
     * 获取所有支持的字段名
     * @return 字段名集合
     */
    public static java.util.Set<String> getAllFieldNames() {
        return FIELD_COLUMN_MAP.keySet();
    }
}
