package com.example.captain.controller;

import com.example.captain.service.SupplyService;
import com.example.common.model.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 1688货源回调控制器
 */
@RestController
@RequestMapping("/captain/callback")
@Slf4j
public class SupplyCallbackController {

    private final SupplyService supplyService;

    @Autowired
    public SupplyCallbackController(SupplyService supplyService) {
        this.supplyService = supplyService;
    }

    /**
     * 处理1688货源数据回调
     *
     * @param eventId 事件ID
     * @param amazonAsin Amazon商品ASIN
     * @param data 1688货源数据
     * @return 处理结果
     */
    @PostMapping("/{eventId}/supply/{amazonAsin}")
    public ApiResponse<Map<String, Object>> handleSupplyCallback(
            @PathVariable(name = "eventId") Long eventId,
            @PathVariable(name = "amazonAsin") String amazonAsin,
            @RequestHeader(value = "Authorization", required = false) String authorization,
            @RequestBody Map<String, Object> data) {

        log.info("收到1688货源数据回调: eventId={}, amazonAsin={}", eventId, amazonAsin);

        // 验证JWT令牌
        if (authorization == null || !authorization.startsWith("Bearer ")) {
            log.warn("无效的JWT令牌");
            return ApiResponse.error(401, "无效的JWT令牌");
        }

        // 获取1688货源数据列表
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> supplyInfoList = (List<Map<String, Object>>) data.get("supplies");

        if (supplyInfoList == null || supplyInfoList.isEmpty()) {
            log.warn("1688货源数据为空");
            return ApiResponse.error(400, "1688货源数据为空");
        }

        // 保存1688货源数据
        int count = supplyService.saveSupplies(eventId, amazonAsin, supplyInfoList);

        log.info("成功保存{}个1688货源", count);

        return ApiResponse.success(Map.of(
            "count", count,
            "message", "1688货源数据处理成功"
        ));
    }
}
