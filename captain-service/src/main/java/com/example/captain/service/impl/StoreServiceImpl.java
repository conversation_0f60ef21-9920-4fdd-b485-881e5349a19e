package com.example.captain.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.captain.dto.*;
import com.example.captain.entity.Store;
import com.example.captain.mapper.StoreMapper;
import com.example.captain.service.StoreService;
import com.example.common.exception.BusinessException;
import com.example.common.model.ErrorCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;

/**
 * 店铺信息服务实现类
 */
@Service
@Slf4j
public class StoreServiceImpl implements StoreService {

    private final StoreMapper storeMapper;
    private final PasswordEncoder passwordEncoder;

    @Autowired
    public StoreServiceImpl(StoreMapper storeMapper) {
        this.storeMapper = storeMapper;
        this.passwordEncoder = new BCryptPasswordEncoder();
    }

    @Override
    public IPage<StoreDTO> getStorePage(StoreQueryParam param) {
        log.info("分页查询店铺列表: param={}", param);

        // 创建分页对象
        Page<Store> page = new Page<>(param.getPage(), param.getSize());

        // 构建查询条件
        LambdaQueryWrapper<Store> queryWrapper = new LambdaQueryWrapper<>();
        
        // 排除已删除的记录
        queryWrapper.eq(Store::getDeleted, 0);
        
        // 店铺名称模糊查询
        if (StringUtils.hasText(param.getStoreName())) {
            queryWrapper.like(Store::getStoreName, param.getStoreName());
        }
        
        // 用户名模糊查询
        if (StringUtils.hasText(param.getUsername())) {
            queryWrapper.like(Store::getUsername, param.getUsername());
        }
        
        // 状态精确查询
        if (param.getStatus() != null) {
            queryWrapper.eq(Store::getStatus, param.getStatus());
        }
        
        // 北美站品牌模糊查询
        if (StringUtils.hasText(param.getNorthAmericaBrand())) {
            queryWrapper.like(Store::getNorthAmericaBrand, param.getNorthAmericaBrand());
        }
        
        // 欧洲站品牌模糊查询
        if (StringUtils.hasText(param.getEuropeBrand())) {
            queryWrapper.like(Store::getEuropeBrand, param.getEuropeBrand());
        }
        
        // EAN国家代码精确查询
        if (StringUtils.hasText(param.getEanCountryCode())) {
            queryWrapper.eq(Store::getEanCountryCode, param.getEanCountryCode());
        }

        // 按ID倒序排序
        queryWrapper.orderByDesc(Store::getId);

        // 执行查询
        IPage<Store> storePage = storeMapper.selectPage(page, queryWrapper);

        // 转换为DTO对象
        IPage<StoreDTO> dtoPage = storePage.convert(this::convertToDTO);

        return dtoPage;
    }

    @Override
    public StoreDTO getStoreDTOById(Long id) {
        log.info("根据ID获取店铺详情: id={}", id);

        Store store = getStoreEntityById(id);
        return convertToDTO(store);
    }

    @Override
    public StoreDTO getStoreByName(String storeName) {
        log.info("根据店铺名称获取店铺详情: storeName={}", storeName);

        Store store = storeMapper.findByStoreName(storeName);
        if (store == null) {
            throw new BusinessException(ErrorCodeEnum.RESOURCE_NOT_FOUND, "店铺不存在");
        }

        return convertToDTO(store);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public StoreDTO createStore(StoreCreateRequest request) {
        log.info("创建店铺: storeName={}, username={}", request.getStoreName(), request.getUsername());

        // 检查店铺名称是否已存在
        Store existingStoreByName = storeMapper.findByStoreName(request.getStoreName());
        if (existingStoreByName != null) {
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR, "店铺名称已存在");
        }

        // 检查用户名是否已存在
        Store existingStoreByUsername = storeMapper.findByUsername(request.getUsername());
        if (existingStoreByUsername != null) {
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR, "用户名已存在");
        }

        // 创建店铺实体
        Store store = new Store();
        BeanUtils.copyProperties(request, store);
        
        // 加密密码
        store.setPassword(passwordEncoder.encode(request.getPassword()));
        
        // 设置默认值
        store.setStatus(1); // 默认正常状态
        store.setDeleted(0); // 默认未删除
        
        // 设置时间
        LocalDateTime now = LocalDateTime.now();
        store.setCreatedAt(now);
        store.setUpdatedAt(now);

        // 保存到数据库
        storeMapper.insert(store);
        log.info("店铺创建成功: id={}, storeName={}", store.getId(), store.getStoreName());

        return convertToDTO(store);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public StoreDTO updateStore(Long id, StoreUpdateRequest request) {
        log.info("更新店铺信息: id={}, request={}", id, request);

        // 获取现有店铺
        Store store = getStoreEntityById(id);

        // 检查店铺名称是否与其他店铺冲突
        if (StringUtils.hasText(request.getStoreName()) && 
            !request.getStoreName().equals(store.getStoreName())) {
            Store existingStore = storeMapper.findByStoreName(request.getStoreName());
            if (existingStore != null && !existingStore.getId().equals(id)) {
                throw new BusinessException(ErrorCodeEnum.PARAM_ERROR, "店铺名称已存在");
            }
        }

        // 检查用户名是否与其他店铺冲突
        if (StringUtils.hasText(request.getUsername()) && 
            !request.getUsername().equals(store.getUsername())) {
            Store existingStore = storeMapper.findByUsername(request.getUsername());
            if (existingStore != null && !existingStore.getId().equals(id)) {
                throw new BusinessException(ErrorCodeEnum.PARAM_ERROR, "用户名已存在");
            }
        }

        // 更新字段
        if (StringUtils.hasText(request.getStoreName())) {
            store.setStoreName(request.getStoreName());
        }
        if (request.getStoreUrl() != null) {
            store.setStoreUrl(request.getStoreUrl());
        }
        if (StringUtils.hasText(request.getUsername())) {
            store.setUsername(request.getUsername());
        }
        if (StringUtils.hasText(request.getPassword())) {
            store.setPassword(passwordEncoder.encode(request.getPassword()));
        }
        if (request.getNorthAmericaBrand() != null) {
            store.setNorthAmericaBrand(request.getNorthAmericaBrand());
        }
        if (request.getEuropeBrand() != null) {
            store.setEuropeBrand(request.getEuropeBrand());
        }
        if (request.getEanCountryCode() != null) {
            store.setEanCountryCode(request.getEanCountryCode());
        }
        if (request.getEanManufacturerCode() != null) {
            store.setEanManufacturerCode(request.getEanManufacturerCode());
        }
        if (request.getEanProductCodeCurrent() != null) {
            store.setEanProductCodeCurrent(request.getEanProductCodeCurrent());
        }

        // 更新时间
        store.setUpdatedAt(LocalDateTime.now());

        // 保存到数据库
        storeMapper.updateById(store);
        log.info("店铺更新成功: id={}", id);

        return convertToDTO(store);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStoreStatus(Long id, StoreStatusUpdateRequest request) {
        log.info("更新店铺状态: id={}, status={}", id, request.getStatus());

        // 检查店铺是否存在
        Store store = getStoreEntityById(id);

        // 更新状态
        int updated = storeMapper.updateStatus(id, request.getStatus());
        if (updated == 0) {
            throw new BusinessException(ErrorCodeEnum.SYSTEM_ERROR, "更新店铺状态失败");
        }

        log.info("店铺状态更新成功: id={}, status={}", id, request.getStatus());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteStore(Long id) {
        log.info("删除店铺: id={}", id);

        // 检查店铺是否存在
        Store store = getStoreEntityById(id);

        // 软删除
        int deleted = storeMapper.softDeleteById(id);
        if (deleted == 0) {
            throw new BusinessException(ErrorCodeEnum.SYSTEM_ERROR, "删除店铺失败");
        }

        log.info("店铺删除成功: id={}", id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String getNextEanProductCode(Long id) {
        log.info("获取下一个EAN产品代码: id={}", id);

        // 获取店铺信息
        Store store = getStoreEntityById(id);

        String currentCode = store.getEanProductCodeCurrent();
        if (!StringUtils.hasText(currentCode)) {
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR, "当前EAN产品代码为空，无法生成下一个代码");
        }

        try {
            // 将当前代码转换为数字并加1
            long currentNumber = Long.parseLong(currentCode);
            long nextNumber = currentNumber + 1;
            String nextCode = String.valueOf(nextNumber);

            // 更新数据库中的当前代码
            int updated = storeMapper.updateEanProductCodeCurrent(id, nextCode);
            if (updated == 0) {
                throw new BusinessException(ErrorCodeEnum.SYSTEM_ERROR, "更新EAN产品代码失败");
            }

            log.info("生成下一个EAN产品代码成功: id={}, nextCode={}", id, nextCode);
            return nextCode;
        } catch (NumberFormatException e) {
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR, "当前EAN产品代码格式不正确，必须为数字");
        }
    }

    @Override
    public Store getStoreEntityById(Long id) {
        log.info("根据ID获取店铺实体: id={}", id);

        LambdaQueryWrapper<Store> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Store::getId, id)
                   .eq(Store::getDeleted, 0);

        Store store = storeMapper.selectOne(queryWrapper);
        if (store == null) {
            throw new BusinessException(ErrorCodeEnum.RESOURCE_NOT_FOUND, "店铺不存在");
        }

        return store;
    }

    /**
     * 将实体转换为DTO
     */
    private StoreDTO convertToDTO(Store store) {
        StoreDTO dto = new StoreDTO();
        BeanUtils.copyProperties(store, dto);
        // 不返回密码字段
        return dto;
    }
}
