# 测试新字段的Curl命令

## 1. 获取JWT令牌

```bash
# 登录获取JWT令牌
TOKEN=$(curl -s -X POST "http://localhost:8080/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}' | \
  grep -o '"token":"[^"]*' | cut -d'"' -f4)

echo "JWT Token: $TOKEN"
```

## 2. 创建测试事件（可选）

```bash
# 创建测试事件
EVENT_RESPONSE=$(curl -s -X POST \
  "http://localhost:8080/api/captain/event/send/1/fetch?topic=test-shop" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "data": {
      "url": "https://www.amazon.co.uk/test-product",
      "method": "GET",
      "timeout": "30000"
    }
  }')

echo "Event Response: $EVENT_RESPONSE"

# 提取事件ID
EVENT_ID=$(echo $EVENT_RESPONSE | grep -o '"id":[0-9]*' | cut -d':' -f2)
echo "Event ID: $EVENT_ID"
```

## 3. 测试产品回调API（包含新字段）

```bash
# 使用固定事件ID（如果上面创建失败）
EVENT_ID=1

# 发送包含新字段的产品数据
curl -X POST \
  "http://localhost:8080/api/captain/callback/${EVENT_ID}/product" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "products": [
      {
        "ASIN": "B08TESTFIELDS",
        "title": "测试新字段商品",
        "price": "£25.99",
        "productLink": "https://www.amazon.co.uk/dp/B08TESTFIELDS",
        "country": "UK",
        "Product_type": "Electronics",
        "recommended_browse_nodes": "123456789",
        "feature": "• 高质量\n• 快速充电\n• 兼容性强",
        "description": "这是商品的详细描述信息",
        "prod_details": "【新字段】产品详细信息：\n- 尺寸：15cm x 10cm x 3cm\n- 重量：200g\n- 材质：铝合金\n- 颜色：银色\n- 保修：2年",
        "product_overview": "【新字段】产品概览：\n这是一款高品质的电子产品，采用先进技术制造，具有出色的性能和可靠性。适合日常使用，是您的理想选择。",
        "images": [
          "https://example.com/image1.jpg",
          "https://example.com/image2.jpg"
        ],
        "productVariants": [],
        "saved_images": [
          "images/amazon/B08TESTFIELDS/B08TESTFIELDS_0.jpg"
        ]
      }
    ]
  }'
```

## 4. 验证数据是否正确保存

### 4.1 通过ASIN查询产品

```bash
# 查询刚才保存的产品
curl -X GET \
  "http://localhost:8080/api/captain/products/asin/B08TESTFIELDS" \
  -H "Authorization: Bearer $TOKEN"
```

### 4.2 查询事件相关的所有产品

```bash
# 查询事件相关的所有产品
curl -X GET \
  "http://localhost:8080/api/captain/products?eventId=${EVENT_ID}" \
  -H "Authorization: Bearer $TOKEN"
```

## 5. 验证新字段的完整测试脚本

```bash
#!/bin/bash

# 完整的测试脚本
GATEWAY_URL="http://localhost:8080"

# 1. 获取令牌
echo "1. 获取JWT令牌..."
TOKEN=$(curl -s -X POST "${GATEWAY_URL}/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}' | \
  grep -o '"token":"[^"]*' | cut -d'"' -f4)

if [ -z "$TOKEN" ]; then
  echo "❌ 获取令牌失败"
  exit 1
fi
echo "✅ 令牌获取成功: ${TOKEN:0:20}..."

# 2. 发送产品数据
echo "2. 发送包含新字段的产品数据..."
RESPONSE=$(curl -s -X POST \
  "${GATEWAY_URL}/api/captain/callback/1/product" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "products": [
      {
        "ASIN": "B08NEWTEST",
        "title": "新字段测试商品",
        "price": "£30.00",
        "productLink": "https://www.amazon.co.uk/dp/B08NEWTEST",
        "country": "UK",
        "Product_type": "Test",
        "recommended_browse_nodes": "999888777",
        "feature": "测试特性",
        "description": "测试描述",
        "prod_details": "【测试】产品详细信息字段内容",
        "product_overview": "【测试】产品概览字段内容",
        "images": ["https://test.com/img.jpg"],
        "productVariants": []
      }
    ]
  }')

if [[ $RESPONSE == *"\"code\":0"* ]]; then
  echo "✅ 产品数据发送成功"
else
  echo "❌ 产品数据发送失败: $RESPONSE"
  exit 1
fi

# 3. 验证数据
echo "3. 验证新字段是否正确保存..."
VERIFY=$(curl -s -X GET \
  "${GATEWAY_URL}/api/captain/products/asin/B08NEWTEST" \
  -H "Authorization: Bearer $TOKEN")

if [[ $VERIFY == *"\"prodDetails\""* ]] && [[ $VERIFY == *"\"productOverview\""* ]]; then
  echo "✅ 新字段验证成功！"
  echo "✅ prodDetails 字段存在"
  echo "✅ productOverview 字段存在"
else
  echo "❌ 新字段验证失败"
  echo "响应: $VERIFY"
fi

echo "测试完成！"
```

## 预期结果

如果新字段正确实现，您应该看到：

1. **产品回调成功**：返回 `{"code":0,"message":"success","data":{"count":1,"message":"产品数据处理成功"}}`

2. **查询结果包含新字段**：
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 123,
    "asin": "B08TESTFIELDS",
    "title": "测试新字段商品",
    "prodDetails": "【新字段】产品详细信息：...",
    "productOverview": "【新字段】产品概览：...",
    // ... 其他字段
  }
}
```

## 故障排除

如果测试失败，请检查：

1. **数据库迁移**：确保 V5 迁移脚本已执行
2. **服务状态**：确保 captain-service 和 gateway-service 正在运行
3. **JWT令牌**：确保能正确获取和使用JWT令牌
4. **字段映射**：确保 Product 实体类和 ProductDTO 包含新字段
