-- 为products表添加重量和尺寸字段
-- 这些字段用于存储从OpenAI提取的商品重量和包装尺寸信息

ALTER TABLE products 
ADD COLUMN shipping_weight DECIMAL(8,3) COMMENT '运输重量',
ADD COLUMN shipping_weight_unit VARCHAR(10) COMMENT '运输重量单位',
ADD COLUMN item_depth_front_to_back DECIMAL(8,3) COMMENT '商品深度(前后)',
ADD COLUMN item_depth_unit VARCHAR(10) COMMENT '深度单位',
ADD COLUMN item_width_side_to_side DECIMAL(8,3) COMMENT '商品宽度(左右)',
ADD COLUMN item_width_unit VARCHAR(10) COMMENT '宽度单位',
ADD COLUMN item_height_floor_to_top DECIMAL(8,3) COMMENT '商品高度(底部到顶部)',
ADD COLUMN item_height_unit VARCHAR(10) COMMENT '高度单位';

-- 为新字段创建索引（可选，根据查询需求）
CREATE INDEX idx_shipping_weight ON products(shipping_weight);
