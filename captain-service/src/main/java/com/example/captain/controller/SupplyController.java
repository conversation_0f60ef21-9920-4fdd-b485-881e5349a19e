package com.example.captain.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.example.captain.dto.SupplyDTO;
import com.example.captain.dto.SupplyQueryParam;
import com.example.captain.service.SupplyService;
import com.example.common.model.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 1688货源控制器
 */
@RestController
@RequestMapping("/captain/supplies")
@Slf4j
public class SupplyController {

    private final SupplyService supplyService;

    @Autowired
    public SupplyController(SupplyService supplyService) {
        this.supplyService = supplyService;
    }

    /**
     * 分页查询1688货源列表
     * 支持按amazon_asin过滤
     * 结果按id倒序排列
     *
     * @param param 查询参数
     * @return 分页结果
     */
    @GetMapping
    public ApiResponse<IPage<SupplyDTO>> getSupplyPage(SupplyQueryParam param) {
        log.info("分页查询1688货源列表: param={}", param);
        IPage<SupplyDTO> page = supplyService.getSupplyPage(param);
        return ApiResponse.success(page);
    }

    /**
     * 根据ID获取1688货源详情
     *
     * @param id 货源ID
     * @return 货源详情
     */
    @GetMapping("/{id}")
    public ApiResponse<SupplyDTO> getSupplyById(@PathVariable(name = "id") Long id) {
        log.info("获取1688货源详情: id={}", id);
        SupplyDTO supply = supplyService.getSupplyDTOById(id);
        return ApiResponse.success(supply);
    }

    /**
     * 根据阿里巴巴商品ID获取1688货源详情
     *
     * @param objectId 阿里巴巴商品ID
     * @return 货源详情
     */
    @GetMapping("/object/{objectId}")
    public ApiResponse<SupplyDTO> getSupplyByObjectId(@PathVariable(name = "objectId") String objectId) {
        log.info("获取1688货源详情: objectId={}", objectId);
        SupplyDTO supply = supplyService.getSupplyDTOByObjectId(objectId);
        return ApiResponse.success(supply);
    }
}
