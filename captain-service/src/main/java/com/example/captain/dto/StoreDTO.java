package com.example.captain.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 店铺信息数据传输对象
 */
@Data
public class StoreDTO {
    /**
     * 店铺ID
     */
    private Long id;

    /**
     * 店铺名称
     */
    private String storeName;

    /**
     * 店铺链接
     */
    private String storeUrl;

    /**
     * 用户名
     */
    private String username;

    /**
     * 北美站品牌
     */
    private String northAmericaBrand;

    /**
     * 欧洲站品牌
     */
    private String europeBrand;

    /**
     * EAN国家代码
     */
    private String eanCountryCode;

    /**
     * EAN厂商代码
     */
    private String eanManufacturerCode;

    /**
     * EAN产品代码当前值
     */
    private String eanProductCodeCurrent;

    /**
     * 状态：1-正常，0-禁用
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
