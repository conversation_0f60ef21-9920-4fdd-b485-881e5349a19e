# 店铺信息管理API文档

## 概述

店铺信息管理API提供了完整的店铺CRUD操作，包括创建、查询、更新、删除店铺信息，以及特殊功能如EAN产品代码管理等。

## 基础信息

- **基础路径**: `/captain/stores`
- **认证方式**: 通过网关统一认证
- **响应格式**: 统一使用 `ApiResponse<T>` 格式

## API接口列表

### 1. 分页查询店铺列表

**接口地址**: `GET /captain/stores`

**功能描述**: 分页查询店铺列表，支持多种条件过滤

**请求参数**:
```json
{
  "page": 1,                    // 页码，从1开始，默认1
  "size": 10,                   // 每页大小，默认10
  "storeName": "我的店铺",       // 店铺名称（模糊查询）
  "username": "seller",         // 用户名（模糊查询）
  "status": 1,                  // 状态：1-正常，0-禁用
  "northAmericaBrand": "品牌",   // 北美站品牌（模糊查询）
  "europeBrand": "品牌",        // 欧洲站品牌（模糊查询）
  "eanCountryCode": "690"       // EAN国家代码（精确查询）
}
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "records": [
      {
        "id": 1,
        "storeName": "我的Amazon店铺",
        "storeUrl": "https://amazon.com/stores/mystore",
        "username": "seller123",
        "northAmericaBrand": "MyBrand US",
        "europeBrand": "MyBrand EU",
        "eanCountryCode": "690",
        "eanManufacturerCode": "12345",
        "eanProductCodeCurrent": "67890",
        "status": 1,
        "createdAt": "2024-01-15T10:30:00",
        "updatedAt": "2024-01-15T10:30:00"
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

### 2. 根据ID获取店铺详情

**接口地址**: `GET /captain/stores/{id}`

**功能描述**: 根据店铺ID获取店铺详细信息

**路径参数**:
- `id`: 店铺ID

**响应示例**:
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "id": 1,
    "storeName": "我的Amazon店铺",
    "storeUrl": "https://amazon.com/stores/mystore",
    "username": "seller123",
    "northAmericaBrand": "MyBrand US",
    "europeBrand": "MyBrand EU",
    "eanCountryCode": "690",
    "eanManufacturerCode": "12345",
    "eanProductCodeCurrent": "67890",
    "status": 1,
    "createdAt": "2024-01-15T10:30:00",
    "updatedAt": "2024-01-15T10:30:00"
  }
}
```

### 3. 根据店铺名称获取店铺详情

**接口地址**: `GET /captain/stores/by-name/{storeName}`

**功能描述**: 根据店铺名称获取店铺详细信息

**路径参数**:
- `storeName`: 店铺名称

### 4. 创建店铺

**接口地址**: `POST /captain/stores`

**功能描述**: 创建新的店铺信息

**请求体**:
```json
{
  "storeName": "我的Amazon店铺",           // 必填，店铺名称
  "storeUrl": "https://amazon.com/stores/mystore",  // 可选，店铺链接
  "username": "seller123",               // 必填，用户名
  "password": "password123",             // 必填，密码（6-50字符）
  "northAmericaBrand": "MyBrand US",     // 可选，北美站品牌
  "europeBrand": "MyBrand EU",           // 可选，欧洲站品牌
  "eanCountryCode": "690",               // 可选，EAN国家代码
  "eanManufacturerCode": "12345",        // 可选，EAN厂商代码
  "eanProductCodeCurrent": "67890"       // 可选，EAN产品代码当前值
}
```

**字段验证规则**:
- `storeName`: 必填，最大100字符
- `storeUrl`: 可选，最大500字符
- `username`: 必填，最大50字符
- `password`: 必填，6-50字符
- 其他字段均为可选

### 5. 更新店铺信息

**接口地址**: `PUT /captain/stores/{id}`

**功能描述**: 更新店铺信息，只更新提供的字段

**路径参数**:
- `id`: 店铺ID

**请求体**: 与创建接口相同，但所有字段都是可选的

### 6. 更新店铺状态

**接口地址**: `PUT /captain/stores/{id}/status`

**功能描述**: 更新店铺的启用/禁用状态

**路径参数**:
- `id`: 店铺ID

**请求体**:
```json
{
  "status": 1  // 必填，状态：1-正常，0-禁用
}
```

### 7. 删除店铺

**接口地址**: `DELETE /captain/stores/{id}`

**功能描述**: 软删除店铺（逻辑删除）

**路径参数**:
- `id`: 店铺ID

### 8. 获取下一个EAN产品代码

**接口地址**: `GET /captain/stores/{id}/ean-next-code`

**功能描述**: 获取并自动递增EAN产品代码

**路径参数**:
- `id`: 店铺ID

**响应示例**:
```json
{
  "code": 0,
  "msg": "success",
  "data": "67891"  // 下一个EAN产品代码
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 400 | 参数错误（如店铺名称已存在、用户名已存在等） |
| 404 | 资源不存在（如店铺不存在） |
| 500 | 系统内部错误 |

## 特殊功能说明

### 1. 密码安全
- 密码使用BCrypt算法加密存储
- API响应中不返回密码字段
- 更新时如果不提供密码则不更新密码

### 2. EAN代码管理
- 支持自动递增EAN产品代码
- 当前代码必须为数字格式
- 每次调用会自动将当前值+1并更新数据库

### 3. 软删除
- 删除操作为逻辑删除，不会物理删除数据
- 查询时自动过滤已删除的记录

### 4. 唯一性约束
- 店铺名称在系统中必须唯一
- 用户名在系统中必须唯一
- 创建和更新时会自动检查重复
