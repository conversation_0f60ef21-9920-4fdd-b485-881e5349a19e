package com.example.worker.imagesimilarity;

import org.opencv.core.*;
import org.opencv.imgproc.Imgproc;

/**
 * 纹理特征提取器 - 实现LBP（局部二值模式）特征提取
 */
public class TextureFeatureExtractor {

    /**
     * 计算图像的LBP特征
     * @param image 输入图像 (灰度图)
     * @return LBP特征图像
     */
    public Mat computeLBP(Mat image) {
        // 确保输入图像是灰度图
        Mat grayImage = new Mat();
        if (image.channels() > 1) {
            Imgproc.cvtColor(image, grayImage, Imgproc.COLOR_BGR2GRAY);
        } else {
            grayImage = image.clone();
        }

        // 创建LBP结果图像
        Mat lbpImage = Mat.zeros(grayImage.rows(), grayImage.cols(), CvType.CV_8UC1);

        // 对每个像素计算LBP值 (忽略边界像素)
        for (int i = 1; i < grayImage.rows() - 1; i++) {
            for (int j = 1; j < grayImage.cols() - 1; j++) {
                // 获取中心像素值
                double centerPixel = grayImage.get(i, j)[0];
                
                // 计算LBP值
                int lbpValue = 0;
                
                // 顺时针方向的8个邻居
                if (grayImage.get(i-1, j-1)[0] >= centerPixel) lbpValue += 1;
                if (grayImage.get(i-1, j)[0] >= centerPixel) lbpValue += 2;
                if (grayImage.get(i-1, j+1)[0] >= centerPixel) lbpValue += 4;
                if (grayImage.get(i, j+1)[0] >= centerPixel) lbpValue += 8;
                if (grayImage.get(i+1, j+1)[0] >= centerPixel) lbpValue += 16;
                if (grayImage.get(i+1, j)[0] >= centerPixel) lbpValue += 32;
                if (grayImage.get(i+1, j-1)[0] >= centerPixel) lbpValue += 64;
                if (grayImage.get(i, j-1)[0] >= centerPixel) lbpValue += 128;
                
                // 设置LBP值
                lbpImage.put(i, j, lbpValue);
            }
        }

        return lbpImage;
    }

    /**
     * 计算LBP直方图
     * @param lbpImage LBP特征图像
     * @return LBP直方图
     */
    public Mat computeLBPHistogram(Mat lbpImage) {
        // 计算直方图
        Mat histogram = new Mat();
        MatOfInt histSize = new MatOfInt(256); // LBP值范围是0-255
        MatOfFloat ranges = new MatOfFloat(0, 256);
        MatOfInt channels = new MatOfInt(0);
        
        Imgproc.calcHist(
            java.util.Arrays.asList(lbpImage),
            channels,
            new Mat(),
            histogram,
            histSize,
            ranges
        );
        
        // 归一化直方图
        Core.normalize(histogram, histogram, 0, 1, Core.NORM_MINMAX);
        
        return histogram;
    }
    
    /**
     * 计算均匀LBP (Uniform LBP)
     * 均匀LBP只考虑模式中0/1转换次数不超过2次的模式
     * @param image 输入图像 (灰度图)
     * @return 均匀LBP特征图像
     */
    public Mat computeUniformLBP(Mat image) {
        // 计算标准LBP
        Mat lbpImage = computeLBP(image);
        
        // 创建均匀LBP映射表 (预计算)
        int[] uniformMap = new int[256];
        int uniformPatterns = 0;
        
        for (int i = 0; i < 256; i++) {
            int transitions = 0;
            int pattern = i;
            
            // 计算位模式中的0/1转换次数
            for (int j = 0; j < 8; j++) {
                int bit1 = (pattern >> j) & 1;
                int bit2 = (pattern >> ((j + 1) % 8)) & 1;
                if (bit1 != bit2) {
                    transitions++;
                }
            }
            
            // 如果转换次数不超过2，则为均匀模式
            if (transitions <= 2) {
                uniformMap[i] = uniformPatterns;
                uniformPatterns++;
            } else {
                uniformMap[i] = uniformPatterns; // 非均匀模式映射到同一个值
            }
        }
        
        // 应用均匀LBP映射
        Mat uniformLBPImage = Mat.zeros(lbpImage.size(), lbpImage.type());
        
        for (int i = 0; i < lbpImage.rows(); i++) {
            for (int j = 0; j < lbpImage.cols(); j++) {
                int lbpValue = (int) lbpImage.get(i, j)[0];
                uniformLBPImage.put(i, j, uniformMap[lbpValue]);
            }
        }
        
        return uniformLBPImage;
    }
}
