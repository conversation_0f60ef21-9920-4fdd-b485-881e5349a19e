package com.example.captain.converter;

import com.example.captain.dto.AmazonProductExcelData;
import com.example.captain.entity.Product;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 产品数据到Excel数据的转换器
 * 将数据库中的Product实体转换为Excel导出格式
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ProductToExcelConverter {

    private final ObjectMapper objectMapper;

    /**
     * 将Product实体转换为AmazonProductExcelData
     * @param product 产品实体
     * @return Excel数据对象
     */
    public AmazonProductExcelData convertToExcelData(Product product) {
        if (product == null) {
            return null;
        }

        AmazonProductExcelData.AmazonProductExcelDataBuilder builder = AmazonProductExcelData.builder();

        // 基本产品信息
        builder.feedProductType(getDefaultProductType(product.getProductType()))
               .itemSku(product.getProductSku())
               .brandName(extractBrandFromTitle(product.getTitle()))
               .externalProductId(product.getAsin())
               .itemName(product.getTitle())
               .manufacturer(extractBrandFromTitle(product.getTitle()))
               .recommendedBrowseNodes(product.getRecommendedBrowseNodes())
               .countryOfOrigin(product.getCountry());

        // 价格信息
        if (StringUtils.hasText(product.getPrice())) {
            try {
                BigDecimal price = new BigDecimal(product.getPrice().replaceAll("[^0-9.]", ""));
                builder.standardPrice(price)
                       .listPrice(price)
                       .currency("USD");
            } catch (NumberFormatException e) {
                log.warn("价格格式错误: {}, ASIN: {}", product.getPrice(), product.getAsin());
            }
        }

        // 图片信息
        List<String> imageUrls = parseImageUrls(product.getImages());
        if (!imageUrls.isEmpty()) {
            builder.mainImageUrl(imageUrls.get(0)); // 第一张作为主图

            // 其他图片（从第二张开始）
            if (imageUrls.size() > 1) {
                List<String> otherImages = imageUrls.subList(1, Math.min(imageUrls.size(), 9)); // 最多8张其他图片
                builder.otherImageUrls(otherImages);
            }
        }

        // 变体信息
        if (product.getIsParentVariant() != null && product.getIsParentVariant()) {
            builder.parentChild("parent");
        } else if (StringUtils.hasText(product.getParentAsin())) {
            builder.parentChild("child")
                   .parentSku(generateSkuFromAsin(product.getParentAsin()));
        }

        // 产品描述和特性
        builder.productDescription(product.getDescription());

        // 处理产品特性
        List<String> bulletPoints = parseFeatures(product.getFeature());
        if (!bulletPoints.isEmpty()) {
            builder.bulletPoints(bulletPoints);
        }

        // 产品属性
        builder.departmentName(product.getProductType())
               .conditionType("New");

        // 默认值设置
        builder.updateDelete("Update")
               .quantity(100) // 默认库存
               .maxOrderQuantity(10); // 默认最大订购数量

        return builder.build();
    }

    /**
     * 批量转换产品列表
     * @param products 产品列表
     * @return Excel数据列表
     */
    public List<AmazonProductExcelData> convertToExcelDataList(List<Product> products) {
        if (products == null || products.isEmpty()) {
            return new ArrayList<>();
        }

        return products.stream()
                .map(this::convertToExcelData)
                .filter(data -> data != null)
                .collect(Collectors.toList());
    }

    /**
     * 获取默认产品类型
     * @param productType 原始产品类型
     * @return 标准化的产品类型
     */
    private String getDefaultProductType(String productType) {
        if (!StringUtils.hasText(productType)) {
            return "PRODUCT";
        }

        // 根据产品类型映射到Amazon标准类型
        String upperType = productType.toUpperCase();

        if (upperType.contains("ELECTRONIC") || upperType.contains("GADGET")) {
            return "CE";
        } else if (upperType.contains("CLOTHING") || upperType.contains("APPAREL")) {
            return "CLOTHING";
        } else if (upperType.contains("HOME") || upperType.contains("KITCHEN")) {
            return "HOME";
        } else if (upperType.contains("BOOK")) {
            return "BOOKS";
        } else if (upperType.contains("TOY")) {
            return "TOYS_AND_GAMES";
        } else if (upperType.contains("BEAUTY") || upperType.contains("COSMETIC")) {
            return "BEAUTY";
        } else if (upperType.contains("SPORT") || upperType.contains("FITNESS")) {
            return "SPORTING_GOODS";
        } else {
            return "PRODUCT"; // 默认类型
        }
    }

    /**
     * 解析图片URL字符串，提取多个图片URL
     * @param imageUrlString 图片URL字符串（可能是JSON格式）
     * @return 图片URL列表
     */
    private List<String> parseImageUrls(String imageUrlString) {
        List<String> imageUrls = new ArrayList<>();

        if (!StringUtils.hasText(imageUrlString)) {
            return imageUrls;
        }

        try {
            // 首先尝试解析为JSON数组
            if (imageUrlString.trim().startsWith("[")) {
                List<String> jsonUrls = objectMapper.readValue(imageUrlString, new TypeReference<List<String>>() {});
                for (String url : jsonUrls) {
                    if (StringUtils.hasText(url) && isValidImageUrl(url.trim())) {
                        imageUrls.add(url.trim());
                    }
                }
                return imageUrls.stream().limit(9).collect(Collectors.toList()); // 主图+8张其他图片
            }
        } catch (Exception e) {
            log.debug("解析JSON图片URL失败，尝试其他方式: {}", e.getMessage());
        }

        // 尝试多种分隔符
        String[] separators = {",", ";", "|", "\n"};

        for (String separator : separators) {
            if (imageUrlString.contains(separator)) {
                String[] urls = imageUrlString.split(separator);
                for (String url : urls) {
                    String trimmedUrl = url.trim();
                    if (StringUtils.hasText(trimmedUrl) && isValidImageUrl(trimmedUrl)) {
                        imageUrls.add(trimmedUrl);
                    }
                }
                break;
            }
        }

        // 如果没有找到分隔符，且是有效的URL，则作为单个URL处理
        if (imageUrls.isEmpty() && isValidImageUrl(imageUrlString.trim())) {
            imageUrls.add(imageUrlString.trim());
        }

        // 限制最多9张图片（主图+8张其他图片）
        return imageUrls.stream().limit(9).collect(Collectors.toList());
    }

    /**
     * 解析产品特性字符串
     * @param featureString 特性字符串
     * @return 特性列表
     */
    private List<String> parseFeatures(String featureString) {
        List<String> features = new ArrayList<>();

        if (!StringUtils.hasText(featureString)) {
            return features;
        }

        // 尝试多种分隔符
        String[] separators = {"\n", "•", "·", "-", "*", "|"};

        for (String separator : separators) {
            if (featureString.contains(separator)) {
                String[] featureArray = featureString.split(separator);
                for (String feature : featureArray) {
                    String trimmedFeature = feature.trim();
                    if (StringUtils.hasText(trimmedFeature) && trimmedFeature.length() > 5) {
                        features.add(trimmedFeature);
                    }
                }
                break;
            }
        }

        // 如果没有找到分隔符，且内容足够长，则作为单个特性处理
        if (features.isEmpty() && featureString.trim().length() > 10) {
            features.add(featureString.trim());
        }

        // 限制最多5个特性
        return features.stream().limit(5).collect(Collectors.toList());
    }

    /**
     * 验证是否为有效的图片URL
     * @param url URL字符串
     * @return 是否有效
     */
    private boolean isValidImageUrl(String url) {
        if (!StringUtils.hasText(url)) {
            return false;
        }

        String lowerUrl = url.toLowerCase();
        return lowerUrl.startsWith("http") &&
               (lowerUrl.contains(".jpg") || lowerUrl.contains(".jpeg") ||
                lowerUrl.contains(".png") || lowerUrl.contains(".gif") ||
                lowerUrl.contains(".webp"));
    }

    /**
     * 从ASIN生成SKU
     * @param asin ASIN
     * @return SKU
     */
    private String generateSkuFromAsin(String asin) {
        if (!StringUtils.hasText(asin)) {
            return "";
        }

        // 简单的SKU生成规则：移除非字母数字字符，并添加前缀
        String cleanAsin = asin.replaceAll("[^a-zA-Z0-9]", "");
        return "SKU_" + cleanAsin;
    }

    /**
     * 从商品标题中提取品牌名称
     * @param title 商品标题
     * @return 品牌名称
     */
    private String extractBrandFromTitle(String title) {
        if (!StringUtils.hasText(title)) {
            return "Unknown";
        }

        // 简单的品牌提取逻辑：取标题的第一个单词作为品牌
        String[] words = title.trim().split("\\s+");
        if (words.length > 0) {
            String firstWord = words[0];
            // 移除特殊字符，只保留字母和数字
            firstWord = firstWord.replaceAll("[^a-zA-Z0-9]", "");
            if (firstWord.length() > 0) {
                return firstWord;
            }
        }

        return "Unknown";
    }
}
