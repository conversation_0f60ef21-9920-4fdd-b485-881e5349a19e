# EAN编码集成文档

## 概述

本文档描述了在captain-service中集成的EAN-13编码生成功能。该功能允许在创建Amazon商品时自动生成唯一的EAN-13条形码。

## EAN-13编码结构

EAN-13编码由13位数字组成：
- **国家代码**：前2-3位，标识产品注册国家
- **厂商代码**：后续4-5位，由各国编码机构分配
- **产品代码**：厂商代码后的5位，标识具体商品
- **校验码**：最后1位，用于校验整个编码的正确性

示例：`9453926538293`
- 国家代码：`945` (新西兰)
- 厂商代码：`3926`
- 产品代码：`53829`
- 校验码：`3`

## 核心组件

### 1. EanCodeUtil工具类

位置：`com.example.captain.util.EanCodeUtil`

主要方法：
- `generateEAN13(countryCode, manufacturerCode, productCode)` - 生成EAN-13编码
- `validateEAN13(ean13)` - 验证EAN-13编码
- `parseEAN13(ean13)` - 解析EAN-13编码
- `generateBatchEAN13(countryCode, manufacturerCode, startCode, count)` - 批量生成

### 2. 数据库集成

#### 店铺表(stores)字段：
- `ean_country_code` - EAN国家代码
- `ean_manufacturer_code` - EAN厂商代码
- `ean_product_code_current` - EAN产品代码当前值

#### 商品表(goods)字段：
- `external_product_id` - 外部产品ID，存储生成的EAN-13编码
- `external_product_id_type` - 外部产品ID类型，设置为"EAN"

### 3. 服务集成

#### StoreService
- `getNextEanProductCode(storeId)` - 获取下一个产品代码并自动递增

#### GoodsService
- 在创建商品时自动生成EAN编码

## 使用流程

### 1. 店铺EAN配置

创建或更新店铺时，需要配置EAN相关信息：

```json
{
  "storeName": "我的店铺",
  "eanCountryCode": "690",
  "eanManufacturerCode": "1234",
  "eanProductCodeCurrent": "0"
}
```

### 2. 自动EAN生成

当创建商品时，系统会：
1. 从product中获取product_type设置为feedProductType
2. 检查店铺EAN配置是否完整
3. 获取下一个产品代码
4. 生成EAN-13编码
5. 将EAN编码设置为externalProductId，类型设置为"EAN"
6. 如果EAN生成失败，则使用ASIN作为externalProductId，类型设置为"ASIN"

### 3. 手动EAN生成

```java
// 基本生成
String eanCode = EanCodeUtil.generateEAN13("690", "1234", "56789");

// 批量生成
String[] eanCodes = EanCodeUtil.generateBatchEAN13("690", "1234", 0, 100);

// 验证
boolean isValid = EanCodeUtil.validateEAN13("6901234567892");

// 解析
EanCodeUtil.EanCodeInfo info = EanCodeUtil.parseEAN13("6901234567892");
```

## 常见国家代码

| 国家 | 代码 | 示例 |
|------|------|------|
| 中国 | 690-695 | 690 |
| 美国 | 000-019, 030-039, 060-139 | 012 |
| 德国 | 400-440 | 400 |
| 法国 | 300-379 | 300 |
| 英国 | 500-509 | 500 |
| 新西兰 | 940-949 | 945 |

## 错误处理

### 常见错误情况：
1. **店铺EAN配置不完整** - 跳过EAN生成，记录警告日志
2. **产品代码格式错误** - 抛出业务异常
3. **EAN编码生成失败** - 记录错误日志，继续创建商品

### 日志示例：
```
WARN - 店铺EAN配置不完整，跳过EAN编码生成: storeId=1
INFO - 成功生成EAN编码: eanCode=6901234567892, storeId=1, productCode=56789
ERROR - 生成EAN编码时发生异常: storeId=1, error=产品代码格式不正确
```

## 测试

运行EAN编码测试：
```bash
mvn test -Dtest=EanCodeUtilTest
```

测试覆盖：
- 基本EAN编码生成和验证
- 批量生成
- 错误输入处理
- 不同国家代码支持
- 编码解析功能

## 注意事项

1. **产品代码唯一性**：每个店铺的产品代码会自动递增，确保唯一性
2. **校验码计算**：严格按照EAN-13标准计算校验码
3. **格式验证**：所有输入参数都会进行格式验证
4. **性能考虑**：EAN生成是轻量级操作，不会影响商品创建性能
5. **向后兼容**：现有商品如果没有EAN编码，不会影响正常功能

## 扩展功能

### 未来可能的扩展：
1. 支持EAN-8编码
2. 支持UPC编码
3. 条形码图片生成
4. EAN编码重复检查
5. 批量导入EAN编码

## 相关文件

- `EanCodeUtil.java` - 核心工具类
- `EanCodeUtilTest.java` - 单元测试
- `GoodsServiceImpl.java` - 服务集成
- `Store.java` - 店铺实体
- `Goods.java` - 商品实体
