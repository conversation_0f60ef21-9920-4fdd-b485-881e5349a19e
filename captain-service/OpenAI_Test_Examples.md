# OpenAI Function Call 测试示例

本文档提供了详细的测试示例，展示如何使用OpenAI Function Call从商品信息中提取重量和尺寸数据。

## 🚀 快速开始

### 1. 配置API密钥

在 `application.yml` 中配置您的OpenAI API密钥：
```yaml
openai:
  api-key: sk-your-actual-openai-api-key-here  # 替换为真实密钥
  model: gpt-3.5-turbo
  timeout: 30
```

### 2. 运行测试

有两种方式运行测试：

#### 方式1: 运行JUnit测试
```bash
cd captain-service
mvn test -Dtest=OpenAIProductAnalysisServiceIntegrationTest
```

#### 方式2: 运行命令行测试工具
```bash
cd captain-service
mvn exec:java -Dexec.mainClass="com.example.captain.util.OpenAITestRunner"
```

## 📝 测试案例

### 案例1: 蓝牙耳机

**输入数据:**
```
Title: Wireless Bluetooth Headphones - Over Ear Headphones with Microphone, 40H Playtime, Deep Bass, Soft Earmuffs, Foldable, Wired and Wireless Modes for PC/Cell Phones/TV/Travel

Feature: 【40H Playtime & Fast Charging】: Wireless headphones can be used for 40 hours in wireless mode (at 50% volume). Weight: 0.5 kg. Dimensions: 20cm x 15cm x 8cm. 【Premium Sound Quality】: Advanced 40mm drivers deliver rich, detailed sound with deep bass.

Description: Premium wireless Bluetooth headphones with superior sound quality. Package dimensions: 20 x 15 x 8 centimeters. Shipping weight: 500 grams. Perfect for music lovers and professionals. The headphones feature comfortable over-ear design with soft padding.
```

**预期输出:**
```json
{
  "shippingWeight": 0.5,
  "shippingWeightUnitOfMeasure": "KG",
  "itemDepthFrontToBack": 20.0,
  "itemDepthUnit": "CM",
  "itemWidthSideToSide": 15.0,
  "itemWidthUnit": "CM",
  "itemHeightFloorToTop": 8.0,
  "itemHeightUnitOfMeasure": "CM"
}
```

### 案例2: 笔记本电脑支架

**输入数据:**
```
Title: Adjustable Laptop Stand for Desk, Aluminum Laptop Riser with Heat-Vent, Ergonomic Computer Stand for MacBook Air Pro, Dell, HP, Lenovo More 10-17.3 Laptops

Feature: 【Ergonomic Design】: 6 adjustable height levels. 【Sturdy & Stable】: Made of premium aluminum alloy. Weight: 1.2kg. Size: 28cm(L) x 22cm(W) x 6cm(H). 【Heat Dissipation】: Open design for better airflow.

Description: This laptop stand is made of high-quality aluminum alloy with excellent heat dissipation. Product dimensions: 28 x 22 x 6 cm. Net weight: 1200g. Supports laptops from 10 to 17.3 inches. Perfect for office and home use.
```

**预期输出:**
```json
{
  "shippingWeight": 1.2,
  "shippingWeightUnitOfMeasure": "KG",
  "itemDepthFrontToBack": 28.0,
  "itemDepthUnit": "CM",
  "itemWidthSideToSide": 22.0,
  "itemWidthUnit": "CM",
  "itemHeightFloorToTop": 6.0,
  "itemHeightUnitOfMeasure": "CM"
}
```

### 案例3: 无线充电器

**输入数据:**
```
Title: Wireless Charger, 15W Max Fast Wireless Charging Pad Compatible with iPhone 14/13/12/11 Series, Samsung Galaxy S23/S22/S21, AirPods Pro

Feature: 【15W Fast Charging】: Supports up to 15W fast charging for compatible devices. 【Compact Design】: Ultra-slim profile at just 8mm thick. Dimensions: 10cm diameter x 0.8cm height. Weight: 150 grams. 【Safety Features】: Over-current, over-voltage, and over-temperature protection.

Description: Sleek and compact wireless charging pad with 15W fast charging capability. Product specifications: Diameter 10cm, Height 0.8cm, Weight 150g. Compatible with Qi-enabled devices. LED indicator shows charging status.
```

**预期输出:**
```json
{
  "shippingWeight": 0.15,
  "shippingWeightUnitOfMeasure": "KG",
  "itemDepthFrontToBack": 10.0,
  "itemDepthUnit": "CM",
  "itemWidthSideToSide": 10.0,
  "itemWidthUnit": "CM",
  "itemHeightFloorToTop": 0.8,
  "itemHeightUnitOfMeasure": "CM"
}
```

### 案例4: 游戏鼠标

**输入数据:**
```
Title: Gaming Mouse, RGB Wired Gaming Mouse with 12000 DPI, 7 Programmable Buttons, Ergonomic Design for PC Laptop Computer Games

Feature: 【High Precision】: Up to 12000 DPI for precise tracking. 【Ergonomic Design】: Comfortable grip for extended gaming sessions. Product size: 12.5cm x 6.8cm x 4.2cm. Net weight: 95g. 【RGB Lighting】: Customizable RGB backlighting with multiple effects.

Description: Professional gaming mouse with high-precision sensor and customizable RGB lighting. Dimensions: 125mm x 68mm x 42mm. Weight: 95 grams. Features 7 programmable buttons and ergonomic design for comfortable gaming.
```

**预期输出:**
```json
{
  "shippingWeight": 0.095,
  "shippingWeightUnitOfMeasure": "KG",
  "itemDepthFrontToBack": 12.5,
  "itemDepthUnit": "CM",
  "itemWidthSideToSide": 6.8,
  "itemWidthUnit": "CM",
  "itemHeightFloorToTop": 4.2,
  "itemHeightUnitOfMeasure": "CM"
}
```

### 案例5: 数据不完整的情况

**输入数据:**
```
Title: Smartphone Case, Clear Protective Case with Shock Absorption

Feature: 【Crystal Clear】: Transparent design shows off your phone's original beauty. 【Drop Protection】: Military-grade protection against drops and impacts.

Description: High-quality transparent phone case with excellent protection. Made from premium TPU material. Easy installation and precise cutouts for all ports.
```

**预期输出:**
```json
{
  "shippingWeight": null,
  "shippingWeightUnitOfMeasure": null,
  "itemDepthFrontToBack": null,
  "itemDepthUnit": null,
  "itemWidthSideToSide": null,
  "itemWidthUnit": null,
  "itemHeightFloorToTop": null,
  "itemHeightUnitOfMeasure": null
}
```

## 🔍 测试结果分析

### 成功提取的特征
- **重量信息**: 能够识别各种重量单位（kg, g, grams）并转换为KG
- **尺寸信息**: 能够识别长宽高信息并转换为CM单位
- **单位转换**: 自动将不同单位转换为标准单位（KG, CM）
- **多种表达**: 能够处理不同的尺寸表达方式（如 "20cm x 15cm x 8cm", "20 x 15 x 8 centimeters"）

### 处理的边界情况
- **缺失信息**: 当某些信息缺失时，对应字段返回null
- **单位识别**: 能够识别mm、cm、g、kg等不同单位
- **格式变化**: 处理不同的数字和单位表达格式

## 🛠️ 自定义测试

您可以创建自己的测试案例：

```java
@Test
public void testCustomProduct() {
    String title = "您的商品标题";
    String feature = "您的商品特性描述";
    String description = "您的商品详细描述";
    
    ProductDimensionsAndWeight result = openAIService.extractDimensionsAndWeight(
        title, feature, description);
    
    // 验证结果
    assertNotNull(result);
    // 添加您的断言...
}
```

## 📊 性能指标

- **平均响应时间**: 2-5秒（取决于网络和OpenAI API响应时间）
- **成功率**: 对于包含明确重量和尺寸信息的商品，成功率约90%+
- **准确率**: 对于标准格式的重量和尺寸信息，准确率接近100%

## ⚠️ 注意事项

1. **API密钥**: 确保配置了有效的OpenAI API密钥
2. **网络连接**: 需要稳定的网络连接访问OpenAI API
3. **API配额**: 每次调用会消耗OpenAI API配额
4. **数据质量**: 提取效果取决于输入数据的质量和完整性

## 🔧 故障排除

### 常见问题

1. **API密钥错误**
   ```
   错误: OpenAI API密钥未配置或无效
   解决: 检查application.yml中的api-key配置
   ```

2. **网络超时**
   ```
   错误: 连接超时
   解决: 检查网络连接，增加timeout配置
   ```

3. **提取结果为空**
   ```
   原因: 商品信息中缺少重量和尺寸信息
   解决: 确保输入数据包含明确的重量和尺寸描述
   ```
