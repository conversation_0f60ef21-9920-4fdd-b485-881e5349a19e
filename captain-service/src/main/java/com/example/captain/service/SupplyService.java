package com.example.captain.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.example.captain.dto.SupplyDTO;
import com.example.captain.dto.SupplyQueryParam;
import com.example.captain.entity.Supply;

import java.util.List;
import java.util.Map;

/**
 * 1688货源服务接口
 */
public interface SupplyService {

    /**
     * 保存1688货源数据
     *
     * @param eventId 事件ID
     * @param amazonAsin 关联的Amazon商品ASIN
     * @param supplyInfoList 1688货源信息列表
     * @return 保存的货源数量
     */
    int saveSupplies(Long eventId, String amazonAsin, List<Map<String, Object>> supplyInfoList);

    /**
     * 获取事件相关的所有1688货源
     *
     * @param eventId 事件ID
     * @return 货源列表
     */
    List<Supply> getSuppliesByEventId(Long eventId);

    /**
     * 获取特定Amazon商品的1688货源
     *
     * @param eventId 事件ID
     * @param amazonAsin Amazon商品ASIN
     * @return 货源列表
     */
    List<Supply> getSuppliesByEventIdAndAmazonAsin(Long eventId, String amazonAsin);

    /**
     * 分页查询1688货源列表
     *
     * @param param 查询参数
     * @return 分页结果
     */
    IPage<SupplyDTO> getSupplyPage(SupplyQueryParam param);

    /**
     * 根据ID获取1688货源详情
     *
     * @param id 货源ID
     * @return 货源详情
     */
    SupplyDTO getSupplyDTOById(Long id);

    /**
     * 根据阿里巴巴商品ID获取1688货源详情
     *
     * @param objectId 阿里巴巴商品ID
     * @return 货源详情
     */
    SupplyDTO getSupplyDTOByObjectId(String objectId);

    /**
     * 根据ID获取1688货源实体
     *
     * @param id 货源ID
     * @return 货源实体
     */
    Supply getSupplyById(Long id);
}
