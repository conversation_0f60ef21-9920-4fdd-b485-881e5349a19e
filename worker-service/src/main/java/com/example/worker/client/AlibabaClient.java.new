package com.example.worker.client;

import com.microsoft.playwright.*;
import com.microsoft.playwright.options.BoundingBox;
import com.microsoft.playwright.options.LoadState;
import com.microsoft.playwright.options.WaitForSelectorState;
import com.microsoft.playwright.options.WaitUntilState;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 阿里巴巴客户端
 * 用于管理阿里巴巴会话和浏览器操作，实现图片搜索功能
 */
@Service
public class AlibabaClient implements AutoCloseable {

    // 人类操作延迟范围（毫秒）
    private static final int MIN_DELAY = 500;  // 最小延迟
    private static final int MAX_DELAY = 2000; // 最大延迟

    // 默认提取的商品数量
    private static final int DEFAULT_MAX_PRODUCTS = 5;

    // 提取的最大商品数量
    private int maxProductsToExtract = DEFAULT_MAX_PRODUCTS;

    // 会话文件路径常量
    private static final String SESSION_FILE_PATH = "src/main/resources/session/alibaba_session.json";

    // 会话初始化状态标志
    private boolean sessionInitialized = false;

    // 基础用户数据目录
    private static final String BASE_USER_DATA_DIR = System.getProperty("user.home") +
            (System.getProperty("os.name").toLowerCase().contains("win") ?
            "\\AppData\\Local\\Temp\\PlaywrightUserData" :
            "/tmp/PlaywrightUserData");
            
    // 固定的插件目录，用于存储和加载插件
    private static final String EXTENSIONS_DIR = System.getProperty("user.home") +
            (System.getProperty("os.name").toLowerCase().contains("win") ?
            "\\AppData\\Local\\Temp\\PlaywrightExtensions" :
            "/tmp/PlaywrightExtensions");

    // 当前会话的用户数据目录
    private String sessionUserDataDir;

    // 临时目录列表，用于在会话结束时清理
    private final List<String> tempDirectories = new ArrayList<>();
