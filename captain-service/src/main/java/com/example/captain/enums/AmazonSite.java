package com.example.captain.enums;

import lombok.Getter;
import java.math.BigDecimal;

/**
 * Amazon站点枚举
 * 包含所有Amazon站点的基础配置信息
 */
@Getter
public enum AmazonSite {
    
    // 北美站
    US("US", "美国", "USD", "英语", SiteRegion.NORTH_AMERICA, 
       new BigDecimal("7.1810"), "amazon.com", "ATVPDKIKX0DER"),
    
    CA("CA", "加拿大", "CAD", "英语", SiteRegion.NORTH_AMERICA, 
       new BigDecimal("5.2400"), "amazon.ca", "A2EUQ1WTGCTBG2"),
    
    MX("MX", "墨西哥", "MXN", "西班牙语", SiteRegion.NORTH_AMERICA, 
       new BigDecimal("0.3739"), "amazon.com.mx", "A1AM78C64UM0Y8"),
    
    // 欧洲站
    UK("GB", "英国", "GBP", "英语", SiteRegion.EUROPE, 
       new BigDecimal("9.6270"), "amazon.co.uk", "A1F83G8C2ARO7P"),
    
    DE("DE", "德国", "EUR", "德语", SiteRegion.EUROPE, 
       new BigDecimal("8.1520"), "amazon.de", "A1PA6795UKMFR9"),
    
    FR("FR", "法国", "EUR", "法语", SiteRegion.EUROPE, 
       new BigDecimal("8.1520"), "amazon.fr", "A13V1IB3VIYZZH"),
    
    SE("SE", "瑞典", "SEK", "瑞典语", SiteRegion.EUROPE, 
       new BigDecimal("0.7562"), "amazon.se", "A2NODRKZP88ZB9"),
    
    NL("NL", "荷兰", "EUR", "荷兰语", SiteRegion.EUROPE, 
       new BigDecimal("8.1520"), "amazon.nl", "A1805IZSGTT6HS"),
    
    IT("IT", "意大利", "EUR", "意大利语", SiteRegion.EUROPE, 
       new BigDecimal("8.1520"), "amazon.it", "APJ6JRA9NG5V4"),
    
    ES("ES", "西班牙", "EUR", "西班牙语", SiteRegion.EUROPE, 
       new BigDecimal("8.1520"), "amazon.es", "A1RKKUPIHCS9HS"),
    
    PL("PL", "波兰", "PLN", "波兰语", SiteRegion.EUROPE, 
       new BigDecimal("1.9244"), "amazon.pl", "A1C3SOZRARQ6R3");
    
    private final String countryCode;
    private final String countryName;
    private final String currencyCode;
    private final String language;
    private final SiteRegion siteRegion;
    private final BigDecimal defaultExchangeRate;
    private final String amazonDomain;
    private final String marketplaceId;
    
    AmazonSite(String countryCode, String countryName, String currencyCode, 
               String language, SiteRegion siteRegion, BigDecimal defaultExchangeRate,
               String amazonDomain, String marketplaceId) {
        this.countryCode = countryCode;
        this.countryName = countryName;
        this.currencyCode = currencyCode;
        this.language = language;
        this.siteRegion = siteRegion;
        this.defaultExchangeRate = defaultExchangeRate;
        this.amazonDomain = amazonDomain;
        this.marketplaceId = marketplaceId;
    }
    
    /**
     * 根据国家代码获取站点
     */
    public static AmazonSite fromCountryCode(String countryCode) {
        for (AmazonSite site : values()) {
            if (site.getCountryCode().equalsIgnoreCase(countryCode)) {
                return site;
            }
        }
        throw new IllegalArgumentException("未知的国家代码: " + countryCode);
    }
    
    /**
     * 根据货币代码获取站点列表
     */
    public static AmazonSite[] fromCurrencyCode(String currencyCode) {
        return java.util.Arrays.stream(values())
                .filter(site -> site.getCurrencyCode().equalsIgnoreCase(currencyCode))
                .toArray(AmazonSite[]::new);
    }
    
    /**
     * 根据站点区域获取站点列表
     */
    public static AmazonSite[] fromSiteRegion(SiteRegion siteRegion) {
        return java.util.Arrays.stream(values())
                .filter(site -> site.getSiteRegion() == siteRegion)
                .toArray(AmazonSite[]::new);
    }
}
