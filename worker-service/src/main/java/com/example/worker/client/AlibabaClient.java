package com.example.worker.client;

import com.microsoft.playwright.*;
import com.microsoft.playwright.options.BoundingBox;
import com.microsoft.playwright.options.LoadState;
import com.microsoft.playwright.options.WaitForSelectorState;
import com.microsoft.playwright.options.WaitUntilState;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 阿里巴巴客户端
 * 用于管理阿里巴巴会话和浏览器操作，实现图片搜索功能
 */
@Service
public class AlibabaClient implements AutoCloseable {

    // 人类操作延迟范围（毫秒）
    private static final int MIN_DELAY = 500;  // 最小延迟
    private static final int MAX_DELAY = 2000; // 最大延迟

    // 默认提取的商品数量
    private static final int DEFAULT_MAX_PRODUCTS = 5;

    // 提取的最大商品数量
    private int maxProductsToExtract = DEFAULT_MAX_PRODUCTS;

    // 会话文件路径常量
    private static final String SESSION_FILE_PATH = "src/main/resources/session/alibaba_session.json";

    // 会话初始化状态标志
    private boolean sessionInitialized = false;

    // 固定的用户数据目录
    private static final String BASE_USER_DATA_DIR = System.getProperty("user.home") +
            (System.getProperty("os.name").toLowerCase().contains("win") ?
            "\\AppData\\Local\\PlaywrightUserData" :
            "/tmp/PlaywrightUserData");

    private Playwright playwright;
    private Browser browser;
    private BrowserContext context;
    private Random random = new Random();

    /**
     * 默认构造函数，使用默认的最大商品提取数量
     */
    public AlibabaClient() {
        // 使用默认值
    }

    /**
     * 带参数的构造函数，设置最大商品提取数量
     * @param maxProductsToExtract 要提取的最大商品数量
     */
    public AlibabaClient(int maxProductsToExtract) {
        this.maxProductsToExtract = maxProductsToExtract;
    }

    /**
     * 设置要提取的最大商品数量
     * @param maxProductsToExtract 要提取的最大商品数量
     */
    public void setMaxProductsToExtract(int maxProductsToExtract) {
        this.maxProductsToExtract = maxProductsToExtract;
    }

    /**
     * 获取当前设置的最大商品提取数量
     * @return 最大商品提取数量
     */
    public int getMaxProductsToExtract() {
        return maxProductsToExtract;
    }

    /**
     * 打开URL并等待用户操作
     * @param url 要打开的URL
     */
    public void openUrlAndWait(String url) {
        System.out.println("正在启动浏览器...");

        try {
            // 创建Playwright实例
            playwright = Playwright.create();

            // 确保用户数据目录存在
            File userDataDirFile = new File(BASE_USER_DATA_DIR);
            if (!userDataDirFile.exists()) {
                userDataDirFile.mkdirs();
                System.out.println("创建用户数据目录: " + BASE_USER_DATA_DIR);
            } else {
                System.out.println("使用现有用户数据目录: " + BASE_USER_DATA_DIR);
            }

            System.out.println("用户数据目录: " + BASE_USER_DATA_DIR);

            // 创建浏览器上下文选项，添加更多权限
            BrowserType.LaunchPersistentContextOptions persistentContextOptions = new BrowserType.LaunchPersistentContextOptions()
                .setHeadless(false)
                .setViewportSize(null)  // 使用默认视口大小
                .setIgnoreHTTPSErrors(false)
                .setChromiumSandbox(false)
                // 关键：启用扩展功能，忽略多个默认参数
                .setIgnoreDefaultArgs(Arrays.asList(
                    "--disable-extensions",  // 允许扩展
                    "--disable-component-extensions-with-background-pages",  // 允许后台扩展
                    "--disable-default-apps"  // 允许默认应用
                ))
                .setArgs(Arrays.asList(
                    "--start-maximized",
                    "--disable-features=InPrivate,PrivateBrowsing",
                    "--no-first-run",
                    "--no-default-browser-check",
                    "--disable-incognito",
                    "--disable-private-mode",
                    "--no-sandbox",
                    "--disable-web-security",
                    "--allow-running-insecure-content",
                    "--disable-site-isolation-trials",
                    "--enable-extensions"  // 启用扩展
                ));

            // 启动持久化浏览器上下文
            context = playwright.chromium().launchPersistentContext(
                Paths.get(BASE_USER_DATA_DIR),
                persistentContextOptions
            );

            // 创建新的页面
            Page page = context.newPage();

            // 添加人类操作延迟
            humanDelay();

            // 导航到指定URL
            System.out.println("正在打开URL: " + url);
            page.navigate(url);

            // 添加人类操作延迟
            humanDelay();

            page.waitForSelector("img", new Page.WaitForSelectorOptions()
                .setState(WaitForSelectorState.VISIBLE)
                .setTimeout(2000));
            System.out.println("图片已加载");

            // 添加人类操作延迟
            humanDelay();

            ElementHandle mainImage = page.querySelector("img");
            BoundingBox boundingBox = mainImage.boundingBox();

            double x = boundingBox.x;
            double y = boundingBox.y;
            double width = boundingBox.width;
            double height = boundingBox.height;

            // 计算图片中心位置
            double centerX = x + width / 2;
            double centerY = y + height / 2;

            // 添加人类操作延迟
            humanDelay();

            // 悬停在图片上
            System.out.println("悬停在图片上");
            page.mouse().move(centerX, centerY);

            // 添加人类操作延迟
            humanDelay();

            // 检查搜索图标并处理
            ElementHandle searchIcon = checkAndHandleSearchIcon(page, centerX, centerY);

            // 如果没有找到搜索图标，退出
            if (searchIcon == null) {
                System.err.println("无法继续执行，退出程序。");
                return;
            }

            BoundingBox iconBox = searchIcon.boundingBox();
            double iconX = iconBox.x + iconBox.width / 2;
            double iconY = iconBox.y + iconBox.height / 2;

            // 添加人类操作延迟
            humanDelay();

            // 创建一个单独的方法来处理点击操作，避免在lambda中使用非final变量
            final double finalIconX = iconX;
            final double finalIconY = iconY;

            try {
                // 在点击前设置网络请求监听
                System.out.println("设置网络请求监听...");
                extractProductData(page);

                // 添加人类操作延迟
                humanDelay();

                // 点击搜索图标
                System.out.println("点击搜索图标...");
                page.mouse().click(finalIconX, finalIconY);

                // 添加人类操作延迟
                humanDelay();

                // 等待页面加载完成
                System.out.println("等待搜索结果页面加载...");
                page.waitForLoadState(LoadState.NETWORKIDLE);

                // 添加人类操作延迟
                humanDelay();

                // 给API请求足够的时间完成
                System.out.println("等待API请求完成...");
                page.waitForTimeout(5000);

                // 添加人类操作延迟
                humanDelay();

            } catch(Exception e) {
                System.err.println("点击搜索图标或等待页面加载时出错: " + e.getMessage());
                e.printStackTrace();
            }

            // 添加人类操作延迟
            humanDelay();

            // 等待用户操作
            System.out.println("浏览器已打开，按Enter键关闭浏览器...");
            try {
                // 使用System.in.read()代替Scanner，更可靠地等待用户输入
                System.in.read();
                // 清空输入缓冲区
                while (System.in.available() > 0) {
                    System.in.read();
                }
            } catch (Exception e) {
                System.err.println("等待用户输入时出错: " + e.getMessage());
            }

            // 关闭浏览器
            System.out.println("正在关闭浏览器...");
            context.close();

        } catch (Exception e) {
            System.err.println("浏览器操作出错: " + e.getMessage());
            e.printStackTrace();
        } finally {
            if (playwright != null) {
                playwright.close();
            }
        }

        System.out.println("浏览器已关闭");
    }

    /**
     * 通过拦截API请求提取商品数据
     * @param page Playwright页面对象
     */
    private void extractProductData(Page page) {
        try {
            System.out.println("开始监听1688图片搜索API请求...");

            // 创建一个标志，表示是否已经找到并处理了API响应
            final boolean[] apiResponseProcessed = {false};

            // 监听网络请求
            page.onResponse(response -> {
                if (apiResponseProcessed[0]) {
                    return; // 如果已经处理过API响应，则不再处理
                }

                String url = response.url();
                // 检查URL是否匹配1688图片搜索API
                if (url.contains("h5api.m.1688.com/h5/mtop.1688.pc.plugin.imagesearch.plugin.search")) {
                    System.out.println("捕获到1688图片搜索API响应: " + url);

                    try {
                        // 获取响应内容
                        String responseBody = response.text();
                        System.out.println("API响应内容长度: " + responseBody.length() + " 字节");

                        // 解析JSON响应
                        try {
                            page.evaluate("json => { window.apiResponse = JSON.parse(json); }", responseBody);

                            String imageSearchOfferResultViewService =
                                "() => {\n" +
                                "  try {\n" +
                                "    return window.apiResponse.data.responseInfo.imageSearchOfferResultViewService;\n" +
                                "  } catch (e) {\n" +
                                "    return '直接获取data失败: ' + e.message;\n" +
                                "  }\n" +
                                "}";

                            Object imageSearchOfferResult = page.evaluate(imageSearchOfferResultViewService);

                            page.evaluate("json => { window.apiResponse = JSON.parse(json); }", imageSearchOfferResult.toString());

                            System.out.println("响应内容解析成功");

                        } catch (Exception e) {
                            System.err.println("解析响应内容失败: " + e.getMessage());
                            e.printStackTrace();

                            // 设置一个空对象，以便后续代码可以继续执行
                            page.evaluate("window.apiResponse = {};");
                        }

                        // 然后，使用单独的脚本提取商品数据
                        String extractProductsScript = getExtractProductsScript();

                        // 执行提取商品数据的脚本
                        Object result = page.evaluate(extractProductsScript);

                        // 打印结果
                        System.out.println("商品数据提取结果：");
                        System.out.println(result.toString());

                        // 标记为已处理
                        apiResponseProcessed[0] = true;

                        // 如果解析成功，下载商品图片
                        if (!result.toString().contains("error")) {
                            try {
                                // 获取结果对象中的商品列表
                                String getResultsScript =
                                    "() => {\n" +
                                    "  try {\n" +
                                    "    // 假设result是一个全局变量，包含了提取的商品数据\n" +
                                    "    return window.apiResponse.data.offerList.slice(0, " + maxProductsToExtract + ");\n" +
                                    "  } catch (e) {\n" +
                                    "    return [];\n" +
                                    "  }\n" +
                                    "}";

                                Object offerListObj = page.evaluate(getResultsScript);

                                if (offerListObj instanceof List) {
                                    @SuppressWarnings("unchecked")
                                    List<Map<String, Object>> offerList = (List<Map<String, Object>>) offerListObj;

                                    System.out.println("开始下载商品图片...");

                                    // 遍历商品列表，下载图片
                                    for (int i = 0; i < offerList.size(); i++) {
                                        Map<String, Object> offer = offerList.get(i);

                                        // 提取商品ID并确保正确的格式
                                        Object idObj = offer.get("id");
                                        String objectId = "未知";
                                        if (idObj != null) {
                                            String idStr = String.valueOf(idObj);
                                            // 处理可能的科学计数法表示
                                            if (idStr.contains("E") || idStr.contains("e") || idStr.contains(".")) {
                                                try {
                                                    // 使用BigDecimal处理科学计数法，更可靠
                                                    java.math.BigDecimal bd = new java.math.BigDecimal(idStr);
                                                    objectId = bd.toPlainString();

                                                    // 移除可能的小数点和小数部分
                                                    if (objectId.contains(".")) {
                                                        objectId = objectId.substring(0, objectId.indexOf('.'));
                                                    }

                                                    System.out.println("将科学计数法ID " + idStr + " 转换为标准格式: " + objectId);
                                                } catch (Exception e) {
                                                    // 如果解析失败，使用原始字符串
                                                    objectId = idStr;
                                                    System.out.println("无法将ID转换为标准格式: " + idStr + ", 错误: " + e.getMessage());
                                                }
                                            } else {
                                                objectId = idStr;
                                            }
                                        }

                                        // 提取图片URL
                                        String imageUrl = "未知";
                                        if (offer.containsKey("image") && offer.get("image") instanceof Map) {
                                            @SuppressWarnings("unchecked")
                                            Map<String, Object> image = (Map<String, Object>) offer.get("image");
                                            if (image.containsKey("imgUrl")) {
                                                imageUrl = String.valueOf(image.get("imgUrl"));
                                            }
                                        } else if (offer.containsKey("imageUrl")) {
                                            imageUrl = String.valueOf(offer.get("imageUrl"));
                                        }

                                        // 下载图片
                                        if (!"未知".equals(objectId) && !"未知".equals(imageUrl)) {
                                            String savedPath = saveProductImage(objectId, imageUrl, page);

                                            // 更新结果对象中的本地图片路径
                                            if (savedPath != null) {
                                                final int index = i;
                                                final String path = savedPath;

                                                // 准备更新本地图片路径

                                                // 直接更新结果对象中的localImagePath字段
                                                String updateLocalPathScript =
                                                    "params => {\n" +
                                                    "  try {\n" +
                                                    "    const index = params.index;\n" +
                                                    "    const path = params.path;\n" +
                                                    "    \n" +
                                                    "    // 获取当前页面上的结果对象\n" +
                                                    "    const data = window.apiResponse;\n" +
                                                    "    if (!data || !data.data || !data.data.offerList) return false;\n" +
                                                    "    \n" +
                                                    "    // 更新对应索引的商品对象，添加本地图片路径\n" +
                                                    "    const offer = data.data.offerList[index];\n" +
                                                    "    if (offer) {\n" +
                                                    "      if (!offer.localImagePath) {\n" +
                                                    "        offer.localImagePath = path;\n" +
                                                    "      }\n" +
                                                    "      return true;\n" +
                                                    "    }\n" +
                                                    "    return false;\n" +
                                                    "  } catch (e) {\n" +
                                                    "    console.error('更新本地图片路径时出错:', e);\n" +
                                                    "    return false;\n" +
                                                    "  }\n" +
                                                    "}";

                                                // 创建参数对象
                                                java.util.Map<String, Object> params = new java.util.HashMap<>();
                                                params.put("index", index);
                                                params.put("path", path);

                                                // 更新本地图片路径
                                                Object updated = page.evaluate(updateLocalPathScript, params);
                                                if (Boolean.TRUE.equals(updated)) {
                                                    System.out.println("已更新商品 #" + (index + 1) + " 的本地图片路径: " + path);
                                                }
                                            }
                                        }
                                    }

                                    // 获取更新后的结果对象
                                    Object updatedResult = page.evaluate("() => {\n" +
                                        "  try {\n" +
                                        "    const data = window.apiResponse;\n" +
                                        "    if (!data || !data.data || !data.data.offerList) return { error: '无法获取更新后的结果' };\n" +
                                        "    \n" +
                                        "    // 提取前" + maxProductsToExtract + "个商品的数据，包含本地图片路径\n" +
                                        "    const results = [];\n" +
                                        "    for (let i = 0; i < Math.min(" + maxProductsToExtract + ", data.data.offerList.length); i++) {\n" +
                                        "      const offer = data.data.offerList[i];\n" +
                                        "      if (offer) {\n" +
                                        "        results.push({\n" +
                                        "          index: i + 1,\n" +
                                        "          objectId: offer.id ? (function(id) {\n" +
                                        "            // 确保ID是字符串格式\n" +
                                        "            let idStr = typeof id === 'number' ? id.toString() : String(id);\n" +
                                        "            // 处理科学计数法\n" +
                                        "            if (idStr.includes('e') || idStr.includes('E') || idStr.includes('.')) {\n" +
                                        "              try {\n" +
                                        "                // 使用BigInt处理大整数（如果浏览器支持）\n" +
                                        "                if (typeof BigInt !== 'undefined') {\n" +
                                        "                  return BigInt(Math.floor(Number(idStr))).toString();\n" +
                                        "                }\n" +
                                        "                // 备选方案：移除科学计数法\n" +
                                        "                let parts = idStr.split(/[eE]/);\n" +
                                        "                if (parts.length === 2) {\n" +
                                        "                  let mantissa = parts[0].replace('.', '');\n" +
                                        "                  let exponent = parseInt(parts[1]);\n" +
                                        "                  if (exponent > 0) {\n" +
                                        "                    idStr = mantissa + '0'.repeat(exponent - (mantissa.length - parts[0].indexOf('.') + 1));\n" +
                                        "                  }\n" +
                                        "                }\n" +
                                        "                // 移除小数部分\n" +
                                        "                if (idStr.includes('.')) {\n" +
                                        "                  idStr = idStr.substring(0, idStr.indexOf('.'));\n" +
                                        "                }\n" +
                                        "              } catch (e) {\n" +
                                        "                console.error('处理ID时出错:', e);\n" +
                                        "              }\n" +
                                        "            }\n" +
                                        "            return idStr;\n" +
                                        "          })(offer.id) : '未知',\n" +
                                        "          title: offer.information?.subject || offer.subject || offer.title || '未知',\n" +
                                        "          price: offer.tradePrice?.offerPrice?.caigouPriceYuan || offer.price || '未知',\n" +
                                        "          imageUrl: offer.image?.imgUrl || offer.imageUrl || '未知',\n" +
                                        "          detailUrl: offer.information?.detailUrl || `https://detail.1688.com/offer/${offer.id}.html`,\n" +
                                        "          localImagePath: offer.localImagePath || ''\n" +
                                        "        });\n" +
                                        "      }\n" +
                                        "    }\n" +
                                        "    \n" +
                                        "    return {\n" +
                                        "      success: true,\n" +
                                        "      totalCount: data.data.offerList.length,\n" +
                                        "      results: results\n" +
                                        "    };\n" +
                                        "  } catch (e) {\n" +
                                        "    return { error: '获取更新后的结果时出错: ' + e.message };\n" +
                                        "  }\n" +
                                        "}");
                                    System.out.println("图片下载完成，更新后的结果对象：");
                                    System.out.println(updatedResult.toString());
                                }
                            } catch (Exception e) {
                                System.err.println("下载商品图片时出错: " + e.getMessage());
                                e.printStackTrace();
                            }
                        }

                        // 如果解析失败，尝试进一步分析问题
                        if (result.toString().contains("error")) {
                            System.out.println("商品数据提取失败，尝试进一步分析问题...");

                            // 尝试获取原始JSON的一部分
                            String getJsonSampleScript =
                                "() => {\n" +
                                "  try {\n" +
                                "    const data = window.apiResponse;\n" +
                                "    return JSON.stringify(data, null, 2).substring(0, 2000) + '...';\n" +
                                "  } catch (e) {\n" +
                                "    return '无法获取JSON样本: ' + e.message;\n" +
                                "  }\n" +
                                "}";

                            Object jsonSample = page.evaluate(getJsonSampleScript);
                            System.out.println("JSON样本（前2000字符）：");
                            System.out.println(jsonSample.toString());
                        }

                    } catch (Exception e) {
                        System.err.println("处理API响应时出错: " + e.getMessage());
                        e.printStackTrace();
                    }
                }
            });

            // 等待一段时间，确保网络监听器有机会捕获响应
            System.out.println("等待API响应...");

        } catch (Exception e) {
            System.err.println("提取商品数据时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 获取提取商品数据的脚本
     * @return 提取商品数据的JavaScript脚本
     */
    private String getExtractProductsScript() {
        return "() => {\n" +
            "  try {\n" +
            "    const data = window.apiResponse;\n" +
            "    \n" +
            "    // 检查API响应结构\n" +
            "    if (!data || !data.data) {\n" +
            "      return { error: '无效的API响应结构', responseStructure: JSON.stringify(data).substring(0, 200) };\n" +
            "    }\n" +
            "    \n" +
            "    // 根据示例数据，offerList在data.data.offerList中\n" +
            "    const offerList = data.data.offerList;\n" +
            "    if (!offerList || !Array.isArray(offerList) || offerList.length === 0) {\n" +
            "      return { \n" +
            "        error: '未找到商品列表', \n" +
            "        dataKeys: data.data ? Object.keys(data.data) : '无data属性',\n" +
            "        fullData: JSON.stringify(data.data).substring(0, 500) + '...'\n" +
            "      };\n" +
            "    }\n" +
            "    \n" +
            "    // 提取前" + maxProductsToExtract + "个商品的数据\n" +
            "    const results = [];\n" +
            "    for (let i = 0; i < Math.min(" + maxProductsToExtract + ", offerList.length); i++) {\n" +
            "      const offer = offerList[i];\n" +
            "      \n" +
            "      // 提取商品ID并确保是字符串类型，避免科学计数法\n" +
            "      const offerId = offer.id ? (function(id) {\n" +
            "        // 确保ID是字符串格式\n" +
            "        let idStr = typeof id === 'number' ? id.toString() : String(id);\n" +
            "        // 处理科学计数法\n" +
            "        if (idStr.includes('e') || idStr.includes('E') || idStr.includes('.')) {\n" +
            "          try {\n" +
            "            // 使用BigInt处理大整数（如果浏览器支持）\n" +
            "            if (typeof BigInt !== 'undefined') {\n" +
            "              return BigInt(Math.floor(Number(idStr))).toString();\n" +
            "            }\n" +
            "            // 备选方案：移除科学计数法\n" +
            "            let parts = idStr.split(/[eE]/);\n" +
            "            if (parts.length === 2) {\n" +
            "              let mantissa = parts[0].replace('.', '');\n" +
            "              let exponent = parseInt(parts[1]);\n" +
            "              if (exponent > 0) {\n" +
            "                idStr = mantissa + '0'.repeat(exponent - (mantissa.length - parts[0].indexOf('.') + 1));\n" +
            "              }\n" +
            "            }\n" +
            "            // 移除小数部分\n" +
            "            if (idStr.includes('.')) {\n" +
            "              idStr = idStr.substring(0, idStr.indexOf('.'));\n" +
            "            }\n" +
            "          } catch (e) {\n" +
            "            console.error('处理ID时出错:', e);\n" +
            "          }\n" +
            "        }\n" +
            "        return idStr;\n" +
            "      })(offer.id) : '未知';\n" +
            "      \n" +
            "      // 提取商品图片 - image.imgUrl (根据示例数据)\n" +
            "      let imageUrl = '未知';\n" +
            "      if (offer.image && offer.image.imgUrl) {\n" +
            "        imageUrl = offer.image.imgUrl;\n" +
            "      } else if (offer.imageUrl) {\n" +
            "        imageUrl = offer.imageUrl;\n" +
            "      }\n" +
            "      \n" +
            "      // 提取商品标题 - information.subject (根据示例数据)\n" +
            "      let title = '未知';\n" +
            "      if (offer.information && offer.information.subject) {\n" +
            "        title = offer.information.subject;\n" +
            "      } else if (offer.subject) {\n" +
            "        title = offer.subject;\n" +
            "      } else if (offer.title) {\n" +
            "        title = offer.title;\n" +
            "      }\n" +
            "      \n" +
            "      // 提取商品价格 - tradePrice.offerPrice.caigouPriceYuan (根据示例数据)\n" +
            "      let price = '未知';\n" +
            "      if (offer.tradePrice && offer.tradePrice.offerPrice && offer.tradePrice.offerPrice.caigouPriceYuan) {\n" +
            "        price = offer.tradePrice.offerPrice.caigouPriceYuan;\n" +
            "      } else if (offer.price) {\n" +
            "        price = offer.price;\n" +
            "      }\n" +
            "      \n" +
            "      // 提取商品链接 - information.detailUrl (根据示例数据)\n" +
            "      let detailUrl = `https://detail.1688.com/offer/${offerId}.html`;\n" +
            "      if (offer.information && offer.information.detailUrl) {\n" +
            "        detailUrl = offer.information.detailUrl;\n" +
            "      }\n" +
            "      \n" +
            "      // 提取开店年限 - tradeService.tpYear\n" +
            "      let tpYear = '未知';\n" +
            "      if (offer.tradeService && offer.tradeService.tpYear) {\n" +
            "        tpYear = offer.tradeService.tpYear;\n" +
            "      }\n" +
            "      \n" +
            "      // 提取公司名称 - company.name\n" +
            "      let companyName = '未知';\n" +
            "      if (offer.company && offer.company.name) {\n" +
            "        companyName = offer.company.name;\n" +
            "      }\n" +
            "      \n" +
            "      // 提取公司档案 - tradeService.tpCreditUrl\n" +
            "      let tpCreditUrl = '未知';\n" +
            "      if (offer.tradeService && offer.tradeService.tpCreditUrl) {\n" +
            "        tpCreditUrl = offer.tradeService.tpCreditUrl;\n" +
            "      }\n" +
            "      \n" +
            "      // 提取年销量 - tradeQuantity.sales360Fuzzify\n" +
            "      let sales360Fuzzify = '未知';\n" +
            "      if (offer.tradeQuantity && offer.tradeQuantity.sales360Fuzzify) {\n" +
            "        sales360Fuzzify = offer.tradeQuantity.sales360Fuzzify;\n" +
            "      }\n" +
            "      \n" +
            "      // 提取几件起批 - tradeQuantity.quantityBegin\n" +
            "      let quantityBegin = '未知';\n" +
            "      if (offer.tradeQuantity && offer.tradeQuantity.quantityBegin) {\n" +
            "        quantityBegin = offer.tradeQuantity.quantityBegin;\n" +
            "      }\n" +
            "      \n" +
            "      // 构建结果对象\n" +
            "      results.push({\n" +
            "        index: i + 1,\n" +
            "        objectId: offerId,\n" +
            "        title: title,\n" +
            "        price: price,\n" +
            "        imageUrl: imageUrl,\n" +
            "        detailUrl: detailUrl,\n" +
            "        tpYear: tpYear,                     // 开店年限\n" +
            "        companyName: companyName,           // 公司名称\n" +
            "        tpCreditUrl: tpCreditUrl,           // 公司档案\n" +
            "        sales360Fuzzify: sales360Fuzzify,   // 年销量\n" +
            "        quantityBegin: quantityBegin,       // 几件起批\n" +
            "        localImagePath: '' // 本地保存的图片路径，将在下载图片后更新\n" +
            "      });\n" +
            "    }\n" +
            "    \n" +
            "    return {\n" +
            "      success: true,\n" +
            "      totalCount: offerList.length,\n" +
            "      results: results\n" +
            "    };\n" +
            "    \n" +
            "  } catch (e) {\n" +
            "    return { error: '解析API响应失败: ' + e.message, message: e.message };\n" +
            "  }\n" +
            "}";
    }

    /**
     * 添加人类操作延迟
     * 在操作之间添加随机延迟，模拟人类操作
     */
    private void humanDelay() {
        try {
            int delay = MIN_DELAY + random.nextInt(MAX_DELAY - MIN_DELAY);
            Thread.sleep(delay);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 检查搜索图标并处理
     * 如果没有找到搜索图标，提示用户安装浏览器插件
     *
     * @param page 页面对象
     * @param centerX 图片中心X坐标
     * @param centerY 图片中心Y坐标
     * @return 搜索图标元素，如果未找到则返回null
     */
    private ElementHandle checkAndHandleSearchIcon(Page page, double centerX, double centerY) {
        // 检查是否存在搜索图标
        ElementHandle searchIcon = page.querySelector("img[src*='data:image/svg+xml']");

        // 如果找到了搜索图标，直接返回
        if (searchIcon != null) {
            return searchIcon;
        }

        // 如果没有找到搜索图标，提示用户安装浏览器插件
        System.out.println("\n未检测到1688图搜插件的搜索图标！");
        System.out.println("请按照以下步骤安装1688图搜插件：");
        System.out.println("1. 打开Chrome扩展商店或Edge扩展商店");
        System.out.println("2. 搜索\"1688图搜\"或\"阿里巴巴图片搜索\"插件");
        System.out.println("3. 安装该插件并启用");
        System.out.println("4. 安装完成后刷新页面");
        System.out.println("\n插件安装完成后，请按Enter键继续...");

        try {
            // 使用System.in.read()代替Scanner，更可靠地等待用户输入
            System.in.read();
            // 清空输入缓冲区
            while (System.in.available() > 0) {
                System.in.read();
            }
        } catch (Exception e) {
            System.err.println("等待用户输入时出错: " + e.getMessage());
        }

        // 刷新页面
        System.out.println("正在刷新页面...");
        page.reload();

        // 等待页面加载
        page.waitForLoadState(LoadState.NETWORKIDLE);

        // 给插件足够的时间加载
        System.out.println("等待插件加载...");
        page.waitForTimeout(3000);

        // 再次悬停在图片上，以触发插件显示搜索图标
        System.out.println("再次悬停在图片上...");
        page.mouse().move(centerX, centerY);
        humanDelay();

        // 再次检查搜索图标
        System.out.println("再次检查搜索图标...");
        searchIcon = page.querySelector("img[src*='data:image/svg+xml']");

        // 如果仍然没有找到搜索图标，退出
        if (searchIcon == null) {
            System.err.println("仍然未检测到搜索图标，请确保插件已正确安装并启用。");
            return null;
        }

        System.out.println("已检测到搜索图标，继续执行...");
        return searchIcon;
    }

    /**
     * 保存商品图片到指定文件夹
     *
     * @param objectId 商品ID
     * @param imageUrl 图片URL
     * @param page Playwright页面对象，用于下载图片
     * @param folderPath 保存图片的文件夹路径，如果为null则使用默认路径
     * @return 保存的图片路径
     */
    private String saveProductImage(String objectId, String imageUrl, Page page, String folderPath) {
        // 确保objectId是正确的字符串格式，避免科学计数法
        String safeObjectId = objectId;

        try {
            // 检查是否是科学计数法表示
            if (objectId.contains("E") || objectId.contains("e") || objectId.contains(".")) {
                // 尝试使用BigDecimal处理科学计数法，更可靠
                java.math.BigDecimal bd = new java.math.BigDecimal(objectId);
                safeObjectId = bd.toPlainString();

                // 移除可能的小数点和小数部分
                if (safeObjectId.contains(".")) {
                    safeObjectId = safeObjectId.substring(0, safeObjectId.indexOf('.'));
                }

                System.out.println("将科学计数法 " + objectId + " 转换为标准格式: " + safeObjectId);
            }
        } catch (Exception e) {
            // 如果解析失败，保留原始ID
            System.out.println("无法将objectId转换为标准格式: " + objectId + ", 错误: " + e.getMessage());
        }

        // 如果folderPath为null，使用默认路径
        String savePath = (folderPath != null) ? folderPath : "images/alibaba";
        System.out.println("开始保存商品图片到文件夹: " + savePath);

        try {
            // 确保目标文件夹存在
            Path folder = Paths.get(savePath);
            if (!Files.exists(folder)) {
                Files.createDirectories(folder);
                System.out.println("创建商品图片保存目录: " + folder);
            }

            // 生成文件名，使用安全的objectId
            String fileName = safeObjectId + getFileExtension(imageUrl);
            Path imagePath = folder.resolve(fileName);

            // 使用Playwright下载图片
            byte[] imageBytes = page.request().fetch(imageUrl).body();

            // 保存图片
            if (imageBytes != null) {
                Files.write(imagePath, imageBytes);
                System.out.println("已保存商品图片: " + imagePath);
                return imagePath.toString();
            } else {
                System.out.println("无法下载图片: " + imageUrl);
            }
        } catch (Exception e) {
            System.err.println("保存图片时出错: " + e.getMessage());
            e.printStackTrace();
        }

        return null;
    }

    /**
     * 保存商品图片到默认文件夹
     *
     * @param objectId 商品ID
     * @param imageUrl 图片URL
     * @param page Playwright页面对象，用于下载图片
     * @return 保存的图片路径
     */
    private String saveProductImage(String objectId, String imageUrl, Page page) {
        return saveProductImage(objectId, imageUrl, page, "images/alibaba");
    }

    /**
     * 获取URL的文件扩展名
     *
     * @param url 图片URL
     * @return 文件扩展名（包括点号）
     */
    private String getFileExtension(String url) {
        try {
            // 移除URL参数
            String cleanUrl = url.split("\\?")[0];

            // 获取路径部分
            String path = new java.net.URL(cleanUrl).getPath();

            // 提取扩展名
            int lastDotPos = path.lastIndexOf('.');
            if (lastDotPos >= 0) {
                String extension = path.substring(lastDotPos);
                // 验证扩展名是否为常见图片格式
                if (extension.matches("\\.(jpg|jpeg|png|gif|bmp|webp|svg)")) {
                    return extension;
                }
            }
        } catch (Exception e) {
            System.out.println("获取文件扩展名时出错: " + url);
        }

        // 默认返回.jpg
        return ".jpg";
    }

    /**
     * 按图片搜索商品
     * 输入图片链接，输出拦截API请求提取的商品数据
     *
     * @param imageUrl 图片URL
     * @param folderPath 保存图片的文件夹路径，如果为null则使用默认路径
     * @return 提取的商品数据，包含商品图片保存路径
     */
    public Map<String, Object> searchProductsByImage(String imageUrl, String folderPath) {
        System.out.println("开始按图片搜索商品...");
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);

        // 创建一个容器来存储API响应结果
        AtomicReference<Object> apiResponseResult = new AtomicReference<>();

        // 最大重试次数
        final int MAX_RETRY = 5;  // 增加最大重试次数
        // 当前重试次数
        int retryCount = 0;
        // 基础重试延迟（毫秒）
        final int BASE_RETRY_DELAY = 2000;
        // 添加成功标志
        boolean success = false;

        while (retryCount < MAX_RETRY && !success) {
            try {
                // 计算当前重试的延迟时间（指数退避）
                long currentDelay = (long) (BASE_RETRY_DELAY * Math.pow(1.5, retryCount));

                // 如果不是第一次尝试，先等待
                if (retryCount > 0) {
                    System.out.println("第 " + (retryCount + 1) + " 次尝试，等待 " + (currentDelay / 1000) + " 秒...");
                    Thread.sleep(currentDelay);

                    // 在重试前尝试清理资源
                    try {
                        close();
                        System.gc();
                        System.out.println("资源清理完成");
                    } catch (Exception e) {
                        System.err.println("清理资源时出错: " + e.getMessage());
                    }
                }

                // 确保用户数据目录存在
                File userDataDirFile = new File(BASE_USER_DATA_DIR);
                if (!userDataDirFile.exists()) {
                    userDataDirFile.mkdirs();
                    System.out.println("创建用户数据目录: " + BASE_USER_DATA_DIR);
                } else {
                    System.out.println("使用现有用户数据目录: " + BASE_USER_DATA_DIR);
                }

                System.out.println("用户数据目录: " + BASE_USER_DATA_DIR);

                // 创建Playwright实例
                playwright = Playwright.create();

                // 用户数据目录已在前面创建

                // 创建浏览器上下文选项
                BrowserType.LaunchPersistentContextOptions persistentContextOptions = new BrowserType.LaunchPersistentContextOptions()
                    .setHeadless(false)
                    .setViewportSize(null)  // 使用默认视口大小
                    .setIgnoreHTTPSErrors(false)
                    .setChromiumSandbox(false)
                    // 启用扩展功能
                    .setIgnoreDefaultArgs(Arrays.asList(
                        "--disable-extensions",
                        "--disable-component-extensions-with-background-pages",
                        "--disable-default-apps"
                    ))
                    .setArgs(Arrays.asList(
                        "--start-maximized",
                        "--disable-features=InPrivate,PrivateBrowsing",
                        "--no-first-run",
                        "--no-default-browser-check",
                        "--disable-incognito",
                        "--disable-private-mode",
                        "--no-sandbox",
                        "--disable-web-security",
                        "--allow-running-insecure-content",
                        "--disable-site-isolation-trials",
                        "--enable-extensions"
                    ));

                // 启动持久化浏览器上下文
                context = playwright.chromium().launchPersistentContext(
                    Paths.get(BASE_USER_DATA_DIR),
                    persistentContextOptions
                );

                // 创建新的页面
                Page page = context.newPage();

                // 设置页面超时
                page.setDefaultTimeout(45000); // 增加默认超时时间到45秒

                // 添加人类操作延迟
                humanDelay();

                try {
                    // 导航到指定URL，使用domcontentloaded而不是load
                    System.out.println("正在打开URL: " + imageUrl);

                    // 使用Page.navigate的重载方法，直接传入选项
                    page.navigate(imageUrl, new Page.NavigateOptions()
                        .setWaitUntil(WaitUntilState.DOMCONTENTLOADED)
                        .setTimeout(45000));

                    // 添加人类操作延迟
                    humanDelay();

                    // 等待图片加载，使用更可靠的方式，允许超时但继续执行
                    try {
                        page.waitForSelector("img", new Page.WaitForSelectorOptions()
                            .setState(WaitForSelectorState.VISIBLE)
                            .setTimeout(5000)); // 增加超时时间
                        System.out.println("图片已加载");
                    } catch (Exception e) {
                        // 忽略等待图片元素的超时，尝试继续处理
                        System.out.println("等待图片元素超时，但继续处理: " + e.getMessage());
                    }

                    // 添加人类操作延迟
                    humanDelay();

                    // 获取图片元素和位置
                    ElementHandle mainImage = page.querySelector("img");
                    BoundingBox boundingBox = mainImage.boundingBox();

                    double x = boundingBox.x;
                    double y = boundingBox.y;
                    double width = boundingBox.width;
                    double height = boundingBox.height;

                    // 计算图片中心位置
                    double centerX = x + width / 2;
                    double centerY = y + height / 2;

                    // 添加人类操作延迟
                    humanDelay();

                    // 悬停在图片上
                    System.out.println("悬停在图片上");
                    page.mouse().move(centerX, centerY);

                    // 添加人类操作延迟
                    humanDelay();

                    // 检查搜索图标并处理
                    ElementHandle searchIcon = checkAndHandleSearchIcon(page, centerX, centerY);

                    // 如果没有找到搜索图标，退出
                    if (searchIcon == null) {
                        System.err.println("无法继续执行，退出程序。");
                        result.put("error", "未找到搜索图标，请确保已安装1688图搜插件");
                        return result;
                    }

                    BoundingBox iconBox = searchIcon.boundingBox();
                    if (iconBox == null) {
                        System.err.println("无法获取搜索图标的边界框，图标可能不可见或已被移除。");
                        result.put("error", "无法获取搜索图标的边界框，请确保1688图搜插件正常工作");
                        return result;
                    }
                    double iconX = iconBox.x + iconBox.width / 2;
                    double iconY = iconBox.y + iconBox.height / 2;

                    // 添加人类操作延迟
                    humanDelay();

                    // 创建一个单独的方法来处理点击操作
                    final double finalIconX = iconX;
                    final double finalIconY = iconY;

                    // 创建一个CompletableFuture来等待API响应
                    CompletableFuture<Object> apiResponseFuture = new CompletableFuture<>();

                    try {
                        // 在点击前设置网络请求监听
                        System.out.println("设置网络请求监听...");

                        // 监听网络请求
                        page.onResponse(response -> {
                            String url = response.url();
                            // 检查URL是否匹配1688图片搜索API
                            if (url.contains("h5api.m.1688.com/h5/mtop.1688.pc.plugin.imagesearch.plugin.search")) {
                                System.out.println("捕获到1688图片搜索API响应: " + url);

                                try {
                                    // 获取响应内容
                                    String responseBody = response.text();
                                    System.out.println("API响应内容长度: " + responseBody.length() + " 字节");

                                    // 解析JSON响应
                                    try {
                                        page.evaluate("json => { window.apiResponse = JSON.parse(json); }", responseBody);

                                        String imageSearchOfferResultViewService =
                                            "() => {\n" +
                                            "  try {\n" +
                                            "    return window.apiResponse.data.responseInfo.imageSearchOfferResultViewService;\n" +
                                            "  } catch (e) {\n" +
                                            "    return '直接获取data失败: ' + e.message;\n" +
                                            "  }\n" +
                                            "}";

                                        Object imageSearchOfferResult = page.evaluate(imageSearchOfferResultViewService);

                                        page.evaluate("json => { window.apiResponse = JSON.parse(json); }", imageSearchOfferResult.toString());

                                        System.out.println("响应内容解析成功");

                                    } catch (Exception e) {
                                        System.err.println("解析响应内容失败: " + e.getMessage());
                                        e.printStackTrace();

                                        // 设置一个空对象，以便后续代码可以继续执行
                                        page.evaluate("window.apiResponse = {};");
                                    }

                                    // 然后，使用单独的脚本提取商品数据
                                    String extractProductsScript = getExtractProductsScript();

                                    // 执行提取商品数据的脚本
                                    Object extractedResult = page.evaluate(extractProductsScript);

                                    // 打印结果
                                    System.out.println("商品数据提取结果：");
                                    System.out.println(extractedResult.toString());

                                    // 如果解析成功，下载商品图片
                                    if (!extractedResult.toString().contains("error")) {
                                        try {
                                            // 获取结果对象中的商品列表
                                            String getResultsScript =
                                                "() => {\n" +
                                                "  try {\n" +
                                                "    return window.apiResponse.data.offerList.slice(0, " + maxProductsToExtract + ");\n" +
                                                "  } catch (e) {\n" +
                                                "    return [];\n" +
                                                "  }\n" +
                                                "}";

                                            Object offerListObj = page.evaluate(getResultsScript);

                                            if (offerListObj instanceof List) {
                                                @SuppressWarnings("unchecked")
                                                List<Map<String, Object>> offerList = (List<Map<String, Object>>) offerListObj;

                                                System.out.println("开始下载商品图片...");

                                                // 遍历商品列表，下载图片
                                                for (int i = 0; i < offerList.size(); i++) {
                                                    Map<String, Object> offer = offerList.get(i);

                                                    // 提取商品ID并确保正确的格式
                                                    Object idObj = offer.get("id");
                                                    String objectId = "未知";
                                                    if (idObj != null) {
                                                        String idStr = String.valueOf(idObj);
                                                        // 处理可能的科学计数法表示
                                                        if (idStr.contains("E") || idStr.contains("e") || idStr.contains(".")) {
                                                            try {
                                                                // 使用BigDecimal处理科学计数法，更可靠
                                                                java.math.BigDecimal bd = new java.math.BigDecimal(idStr);
                                                                objectId = bd.toPlainString();

                                                                // 移除可能的小数点和小数部分
                                                                if (objectId.contains(".")) {
                                                                    objectId = objectId.substring(0, objectId.indexOf('.'));
                                                                }

                                                                System.out.println("将科学计数法ID " + idStr + " 转换为标准格式: " + objectId);
                                                            } catch (Exception e) {
                                                                // 如果解析失败，使用原始字符串
                                                                objectId = idStr;
                                                                System.out.println("无法将ID转换为标准格式: " + idStr + ", 错误: " + e.getMessage());
                                                            }
                                                        } else {
                                                            objectId = idStr;
                                                        }
                                                    }

                                                    // 提取图片URL
                                                    String productImageUrl = "未知";
                                                    if (offer.containsKey("image") && offer.get("image") instanceof Map) {
                                                        @SuppressWarnings("unchecked")
                                                        Map<String, Object> image = (Map<String, Object>) offer.get("image");
                                                        if (image.containsKey("imgUrl")) {
                                                            productImageUrl = String.valueOf(image.get("imgUrl"));
                                                        }
                                                    } else if (offer.containsKey("imageUrl")) {
                                                        productImageUrl = String.valueOf(offer.get("imageUrl"));
                                                    }

                                                    // 下载图片，直接使用objectId作为文件名
                                                    if (!"未知".equals(objectId) && !"未知".equals(productImageUrl)) {
                                                        String savedPath = saveProductImage(objectId, productImageUrl, page, folderPath);

                                                        // 更新结果对象中的本地图片路径
                                                        if (savedPath != null) {
                                                            final int index = i;
                                                            final String path = savedPath;

                                                            // 直接更新结果对象中的localImagePath字段
                                                            String updateLocalPathScript =
                                                                "params => {\n" +
                                                                "  try {\n" +
                                                                "    const index = params.index;\n" +
                                                                "    const path = params.path;\n" +
                                                                "    \n" +
                                                                "    // 获取当前页面上的结果对象\n" +
                                                                "    const data = window.apiResponse;\n" +
                                                                "    if (!data || !data.data || !data.data.offerList) return false;\n" +
                                                                "    \n" +
                                                                "    // 更新对应索引的商品对象，添加本地图片路径\n" +
                                                                "    const offer = data.data.offerList[index];\n" +
                                                                "    if (offer) {\n" +
                                                                "      if (!offer.localImagePath) {\n" +
                                                                "        offer.localImagePath = path;\n" +
                                                                "      }\n" +
                                                                "      return true;\n" +
                                                                "    }\n" +
                                                                "    return false;\n" +
                                                                "  } catch (e) {\n" +
                                                                "    console.error('更新本地图片路径时出错:', e);\n" +
                                                                "    return false;\n" +
                                                                "  }\n" +
                                                                "}";

                                                            // 创建参数对象
                                                            java.util.Map<String, Object> params = new java.util.HashMap<>();
                                                            params.put("index", index);
                                                            params.put("path", path);

                                                            // 更新本地图片路径
                                                            Object updated = page.evaluate(updateLocalPathScript, params);
                                                            if (Boolean.TRUE.equals(updated)) {
                                                                System.out.println("已更新商品 #" + (index + 1) + " 的本地图片路径: " + path);
                                                            }
                                                        }
                                                    }
                                                }

                                                // 获取更新后的结果对象
                                                Object updatedResult = page.evaluate("() => {\n" +
                                                    "  try {\n" +
                                                    "    const data = window.apiResponse;\n" +
                                                    "    if (!data || !data.data || !data.data.offerList) return { error: '无法获取更新后的结果' };\n" +
                                                    "    \n" +
                                                    "    // 提取前" + maxProductsToExtract + "个商品的数据，包含本地图片路径\n" +
                                                    "    const results = [];\n" +
                                                    "    for (let i = 0; i < Math.min(" + maxProductsToExtract + ", data.data.offerList.length); i++) {\n" +
                                                    "      const offer = data.data.offerList[i];\n" +
                                                    "      if (offer) {\n" +
                                                    "        results.push({\n" +
                                                    "          index: i + 1,\n" +
                                                    "          objectId: offer.id ? (function(id) {\n" +
                                                    "            // 确保ID是字符串格式\n" +
                                                    "            let idStr = typeof id === 'number' ? id.toString() : String(id);\n" +
                                                    "            // 处理科学计数法\n" +
                                                    "            if (idStr.includes('e') || idStr.includes('E') || idStr.includes('.')) {\n" +
                                                    "              try {\n" +
                                                    "                // 使用BigInt处理大整数（如果浏览器支持）\n" +
                                                    "                if (typeof BigInt !== 'undefined') {\n" +
                                                    "                  return BigInt(Math.floor(Number(idStr))).toString();\n" +
                                                    "                }\n" +
                                                    "                // 备选方案：移除科学计数法\n" +
                                                    "                let parts = idStr.split(/[eE]/);\n" +
                                                    "                if (parts.length === 2) {\n" +
                                                    "                  let mantissa = parts[0].replace('.', '');\n" +
                                                    "                  let exponent = parseInt(parts[1]);\n" +
                                                    "                  if (exponent > 0) {\n" +
                                                    "                    idStr = mantissa + '0'.repeat(exponent - (mantissa.length - parts[0].indexOf('.') + 1));\n" +
                                                    "                  }\n" +
                                                    "                }\n" +
                                                    "                // 移除小数部分\n" +
                                                    "                if (idStr.includes('.')) {\n" +
                                                    "                  idStr = idStr.substring(0, idStr.indexOf('.'));\n" +
                                                    "                }\n" +
                                                    "              } catch (e) {\n" +
                                                    "                console.error('处理ID时出错:', e);\n" +
                                                    "              }\n" +
                                                    "            }\n" +
                                                    "            return idStr;\n" +
                                                    "          })(offer.id) : '未知',\n" +
                                                    "          title: offer.information?.subject || offer.subject || offer.title || '未知',\n" +
                                                    "          price: offer.tradePrice?.offerPrice?.caigouPriceYuan || offer.price || '未知',\n" +
                                                    "          imageUrl: offer.image?.imgUrl || offer.imageUrl || '未知',\n" +
                                                    "          detailUrl: offer.information?.detailUrl || `https://detail.1688.com/offer/${offer.id}.html`,\n" +
                                                    "          tpYear: offer.tradeService?.tpYear || '未知',\n" +
                                                    "          companyName: offer.company?.name || '未知',\n" +
                                                    "          tpCreditUrl: offer.tradeService?.tpCreditUrl || '未知',\n" +
                                                    "          sales360Fuzzify: offer.tradeQuantity?.sales360Fuzzify || '未知',\n" +
                                                    "          quantityBegin: offer.tradeQuantity?.quantityBegin || '未知',\n" +
                                                    "          localImagePath: offer.localImagePath || ''\n" +
                                                    "        });\n" +
                                                    "      }\n" +
                                                    "    }\n" +
                                                    "    \n" +
                                                    "    return {\n" +
                                                    "      success: true,\n" +
                                                    "      totalCount: data.data.offerList.length,\n" +
                                                    "      results: results\n" +
                                                    "    };\n" +
                                                    "  } catch (e) {\n" +
                                                    "    return { error: '获取更新后的结果时出错: ' + e.message };\n" +
                                                    "  }\n" +
                                                    "}");

                                                // 设置API响应结果
                                                apiResponseResult.set(updatedResult);
                                                apiResponseFuture.complete(updatedResult);
                                            }
                                        } catch (Exception e) {
                                            System.err.println("下载商品图片时出错: " + e.getMessage());
                                            e.printStackTrace();
                                            apiResponseFuture.completeExceptionally(e);
                                        }
                                    } else {
                                        // 如果提取失败，完成future但带有错误信息
                                        apiResponseFuture.complete(extractedResult);
                                        apiResponseResult.set(extractedResult);
                                    }
                                } catch (Exception e) {
                                    System.err.println("处理API响应时出错: " + e.getMessage());
                                    e.printStackTrace();
                                    apiResponseFuture.completeExceptionally(e);
                                }
                            }
                        });

                        // 添加人类操作延迟
                        humanDelay();

                        // 点击搜索图标
                        System.out.println("点击搜索图标...");
                        page.mouse().click(finalIconX, finalIconY);

                        // 添加人类操作延迟
                        humanDelay();

                        // 等待页面加载完成
                        System.out.println("等待搜索结果页面加载...");
                        page.waitForLoadState(LoadState.NETWORKIDLE);

                        // 添加人类操作延迟
                        humanDelay();

                        // 给API请求足够的时间完成
                        System.out.println("等待API请求完成...");
                        page.waitForTimeout(5000);

                        // 等待API响应或超时
                        try {
                            // 最多等待30秒
                            Object apiResponse = apiResponseFuture.get(30, java.util.concurrent.TimeUnit.SECONDS);
                            if (apiResponse != null) {
                                result = (Map<String, Object>) apiResponse;
                                // 标记操作成功
                                success = true;
                            }
                        } catch (Exception e) {
                            System.err.println("等待API响应时出错: " + e.getMessage());
                            result.put("error", "等待API响应超时或出错: " + e.getMessage());
                            // 不在这里增加重试计数，让外层catch处理
                            throw e; // 重新抛出异常，让外层catch处理
                        }

                        // 如果成功获取到API响应，跳出循环
                        if (success) {
                            System.out.println("成功获取API响应，跳出重试循环");
                            break;
                        }

                    } catch (Exception e) {
                        System.err.println("点击搜索图标或等待页面加载时出错: " + e.getMessage());
                        e.printStackTrace();
                        result.put("error", "点击搜索图标或等待页面加载时出错: " + e.getMessage());
                    }

                } catch (Exception e) {
                    System.err.println("浏览器操作出错: " + e.getMessage());
                    e.printStackTrace();
                    result.put("error", "浏览器操作出错: " + e.getMessage());

                    // 关闭页面，避免资源泄漏
                    try {
                        if (page != null) {
                            page.close();
                        }
                    } catch (Exception pageCloseEx) {
                        System.err.println("关闭页面时出错: " + pageCloseEx.getMessage());
                    }

                    // 不在这里增加重试计数，让外层catch处理
                    throw e; // 重新抛出异常，让外层catch处理
                } // 结束内部try块
            } catch (Exception e) {
                // 处理所有异常
                boolean isTargetClosedError = e.getMessage() != null &&
                    (e.getMessage().contains("Target page, context or browser has been closed") ||
                     e.getMessage().contains("TargetClosedError"));

                System.err.println("搜索商品时出错: " + e.getMessage());
                e.printStackTrace();

                // 对于TargetClosedError，添加特殊处理
                if (isTargetClosedError) {
                    System.out.println("检测到TargetClosedError，尝试清理浏览器进程...");
                    try {
                        // 在Linux/Mac环境下尝试杀死可能存在的Chrome进程
                        if (!System.getProperty("os.name").toLowerCase().contains("win")) {
                            // 查找与用户数据目录相关的Chrome进程
                            String cmd = "ps aux | grep \"" + BASE_USER_DATA_DIR + "\" | grep -v grep | awk '{print $2}'";
                            Process process = Runtime.getRuntime().exec(new String[]{"bash", "-c", cmd});

                            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
                            String line;
                            while ((line = reader.readLine()) != null) {
                                if (!line.trim().isEmpty()) {
                                    try {
                                        int pid = Integer.parseInt(line.trim());
                                        System.out.println("尝试终止Chrome进程: " + pid);
                                        Runtime.getRuntime().exec("kill -9 " + pid);
                                    } catch (NumberFormatException nfe) {
                                        // 忽略非数字输出
                                    }
                                }
                            }

                            // 等待进程清理完成
                            Thread.sleep(2000);
                            System.out.println("浏览器进程清理完成");
                        } else {
                            System.out.println("Windows环境下不执行浏览器进程清理");
                        }
                    } catch (Exception cleanupEx) {
                        System.err.println("清理浏览器进程时出错: " + cleanupEx.getMessage());
                    }
                }

                result.put("error", "搜索商品时出错: " + e.getMessage());

                // 关闭资源，避免资源泄漏
                try {
                    if (context != null) {
                        context.close();
                    }
                    if (playwright != null) {
                        playwright.close();
                    }
                } catch (Exception closeEx) {
                    System.err.println("关闭资源时出错: " + closeEx.getMessage());
                }

                // 增加重试计数 - 只在这一个地方增加计数
                retryCount++;

                // 如果还有重试次数，打印信息
                if (retryCount < MAX_RETRY) {
                    long nextRetryDelay = (long) (BASE_RETRY_DELAY * Math.pow(1.5, retryCount));
                    System.out.println("将在 " + (nextRetryDelay / 1000) + " 秒后进行第 " + (retryCount + 1) + " 次重试...");
                    try {
                        Thread.sleep(nextRetryDelay);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                    // 不需要continue，循环会自动继续
                } else {
                    System.out.println("已达到最大重试次数 " + MAX_RETRY + "，停止重试");
                }
            }
        } // 结束while循环

        // 注意：不在finally块中关闭资源，而是在方法返回前关闭
        // 这样可以确保在完全处理完响应后再关闭资源

        // 如果没有设置结果，使用apiResponseResult中的值
        if (!result.containsKey("success") && !result.containsKey("error")) {
            Object apiResponse = apiResponseResult.get();
            if (apiResponse != null) {
                try {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> apiResponseMap = (Map<String, Object>) apiResponse;
                    result = apiResponseMap;
                } catch (ClassCastException e) {
                    result.put("error", "无法转换API响应结果");
                }
            } else {
                result.put("error", "未能获取API响应结果");
            }
        }

        // 在返回结果前关闭浏览器资源
        // 这样可以确保在完全处理完响应后再关闭资源
        try {
            close();
            System.out.println("AlibabaClient资源已关闭");
        } catch (Exception e) {
            System.err.println("关闭AlibabaClient资源时出错: " + e.getMessage());
        }

        return result;
    }

    /**
     * 按图片搜索商品（使用默认保存路径）
     * 输入图片链接，输出拦截API请求提取的商品数据
     *
     * @param imageUrl 图片URL
     * @return 提取的商品数据，包含商品图片保存路径
     */
    public Map<String, Object> searchProductsByImage(String imageUrl) {
        return searchProductsByImage(imageUrl, "images/alibaba");
    }

    /**
     * 初始化会话
     * 1. 检查会话是否已初始化
     * 2. 如果未初始化，创建会话文件目录并保存会话状态
     *
     * @return 操作是否成功
     */
    public boolean initSession() {
        // 如果会话已初始化，直接返回成功
        if (sessionInitialized) {
            System.out.println("会话已初始化，无需重复初始化");
            return true;
        }

        System.out.println("开始阿里巴巴会话管理...");
        Path sessionPath = Paths.get(SESSION_FILE_PATH);
        boolean sessionExists = Files.exists(sessionPath);

        try {
            // 确保会话目录存在
            Path sessionDir = sessionPath.getParent();
            if (!Files.exists(sessionDir)) {
                Files.createDirectories(sessionDir);
                System.out.println("创建会话目录: " + sessionDir);
            }

            // 如果会话文件存在，尝试加载
            if (sessionExists) {
                System.out.println("已找到会话文件: " + SESSION_FILE_PATH + ", 正在加载...");

                // 如果上下文已存在，先关闭
                if (context != null) {
                    try {
                        context.close();
                    } catch (Exception e) {
                        System.out.println("关闭现有上下文时出错: " + e.getMessage());
                    }
                }

                // 创建带会话状态的浏览器上下文
                if (playwright == null) {
                    playwright = Playwright.create();
                }

                // 创建浏览器上下文选项
                BrowserType.LaunchPersistentContextOptions persistentContextOptions = new BrowserType.LaunchPersistentContextOptions()
                    .setHeadless(false)
                    .setViewportSize(null)  // 使用默认视口大小
                    .setIgnoreHTTPSErrors(false)
                    .setChromiumSandbox(false)
                    .setIgnoreDefaultArgs(Arrays.asList(
                        "--disable-extensions",
                        "--disable-component-extensions-with-background-pages",
                        "--disable-default-apps"
                    ))
                    .setArgs(Arrays.asList(
                        "--start-maximized",
                        "--disable-features=InPrivate,PrivateBrowsing",
                        "--no-first-run",
                        "--no-default-browser-check",
                        "--disable-incognito",
                        "--disable-private-mode",
                        "--no-sandbox",
                        "--disable-web-security",
                        "--allow-running-insecure-content",
                        "--disable-site-isolation-trials",
                        "--enable-extensions"
                    ));

                // 确保用户数据目录存在
                File userDataDirFile = new File(BASE_USER_DATA_DIR);
                if (!userDataDirFile.exists()) {
                    userDataDirFile.mkdirs();
                    System.out.println("创建用户数据目录: " + BASE_USER_DATA_DIR);
                } else {
                    System.out.println("使用现有用户数据目录: " + BASE_USER_DATA_DIR);
                }

                System.out.println("用户数据目录: " + BASE_USER_DATA_DIR);

                // 启动持久化浏览器上下文
                context = playwright.chromium().launchPersistentContext(
                    Paths.get(BASE_USER_DATA_DIR),
                    persistentContextOptions
                );

                // 创建一个临时页面来加载会话状态
                try {
                    Page tempPage = context.newPage();

                    // 读取会话文件内容
                    String sessionContent = new String(Files.readAllBytes(sessionPath));

                    // 使用JavaScript设置会话状态
                    tempPage.evaluate("sessionData => {\n" +
                        "  try {\n" +
                        "    const data = JSON.parse(sessionData);\n" +
                        "    // 设置cookies\n" +
                        "    if (data.cookies) {\n" +
                        "      data.cookies.forEach(cookie => {\n" +
                        "        document.cookie = `${cookie.name}=${cookie.value}; path=${cookie.path}; domain=${cookie.domain}`;\n" +
                        "      });\n" +
                        "    }\n" +
                        "    return true;\n" +
                        "  } catch (e) {\n" +
                        "    return false;\n" +
                        "  }\n" +
                        "}", sessionContent);

                    // 关闭临时页面
                    tempPage.close();
                } catch (Exception e) {
                    System.out.println("加载会话状态时出错: " + e.getMessage());
                    // 继续执行，不中断流程
                }

                sessionInitialized = true;
                System.out.println("会话已成功加载");
                return true;
            } else {
                // 会话文件不存在，创建新会话
                System.out.println("会话文件不存在，创建新会话");

                // 创建Playwright实例
                if (playwright == null) {
                    playwright = Playwright.create();
                }

                // 确保用户数据目录存在
                File userDataDirFile = new File(BASE_USER_DATA_DIR);
                if (!userDataDirFile.exists()) {
                    userDataDirFile.mkdirs();
                    System.out.println("创建用户数据目录: " + BASE_USER_DATA_DIR);
                } else {
                    System.out.println("使用现有用户数据目录: " + BASE_USER_DATA_DIR);
                }

                // 创建浏览器上下文选项
                BrowserType.LaunchPersistentContextOptions persistentContextOptions = new BrowserType.LaunchPersistentContextOptions()
                    .setHeadless(false)
                    .setViewportSize(null)  // 使用默认视口大小
                    .setIgnoreHTTPSErrors(false)
                    .setChromiumSandbox(false)
                    .setIgnoreDefaultArgs(Arrays.asList(
                        "--disable-extensions",
                        "--disable-component-extensions-with-background-pages",
                        "--disable-default-apps"
                    ))
                    .setArgs(Arrays.asList(
                        "--start-maximized",
                        "--disable-features=InPrivate,PrivateBrowsing",
                        "--no-first-run",
                        "--no-default-browser-check",
                        "--disable-incognito",
                        "--disable-private-mode",
                        "--no-sandbox",
                        "--disable-web-security",
                        "--allow-running-insecure-content",
                        "--disable-site-isolation-trials",
                        "--enable-extensions"
                    ));

                // 启动持久化浏览器上下文
                context = playwright.chromium().launchPersistentContext(
                    Paths.get(BASE_USER_DATA_DIR),
                    persistentContextOptions
                );

                // 保存会话状态
                context.storageState(new BrowserContext.StorageStateOptions().setPath(sessionPath));
                System.out.println("会话状态已保存到: " + sessionPath);

                sessionInitialized = true;
                return true;
            }
        } catch (Exception e) {
            System.err.println("初始化会话时出错: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 打开商品详情页并获取商品信息
     *
     * @param productUrl 商品详情页URL
     * @return 包含商品属性和包装信息的Map
     */
    public Map<String, String> openProductDetailPage(String productUrl) {
        System.out.println("正在打开商品详情页: " + productUrl);
        Map<String, String> result = new HashMap<>();

        try {
            // 确保会话已初始化
            if (!sessionInitialized) {
                boolean initResult = initSession();
                if (!initResult) {
                    System.err.println("初始化会话失败，无法继续");
                    result.put("error", "初始化会话失败");
                    return result;
                }
            }

            // 创建新的页面
            Page page = context.newPage();
            page.setDefaultTimeout(60000); // 60秒

            // 添加人类操作延迟
            humanDelay();

            // 导航到商品详情页
            System.out.println("正在打开商品详情页: " + productUrl);
            page.navigate(productUrl);

            // 等待页面加载
            page.waitForLoadState(LoadState.DOMCONTENTLOADED);
            humanDelay();

            // 等待商品属性元素加载
            try {
                page.waitForSelector("div.od-pc-attribute", new Page.WaitForSelectorOptions()
                    .setState(WaitForSelectorState.VISIBLE)
                    .setTimeout(10000));

                // 获取商品属性信息
                String attributeInfo = page.evaluate("() => document.querySelector('div.od-pc-attribute').innerText").toString();
                result.put("productAttributes", attributeInfo);
                System.out.println("商品属性信息获取成功");
            } catch (Exception e) {
                System.out.println("获取商品属性信息时出错: " + e.getMessage());
                result.put("productAttributes", "未找到商品属性信息");
            }

            // 获取包装信息
            try {
                page.waitForSelector("div.od-pc-offer-cross", new Page.WaitForSelectorOptions()
                    .setState(WaitForSelectorState.VISIBLE)
                    .setTimeout(5000));

                String packagingInfo = page.evaluate("() => document.querySelector('div.od-pc-offer-cross').innerText").toString();
                result.put("packagingInfo", packagingInfo);
                System.out.println("包装信息获取成功");
            } catch (Exception e) {
                System.out.println("获取包装信息时出错: " + e.getMessage());
                result.put("packagingInfo", "未找到包装信息");
            }

            // 添加人类操作延迟
            humanDelay();

            // 关闭页面
            page.close();

        } catch (Exception e) {
            System.err.println("打开商品详情页时出错: " + e.getMessage());
            e.printStackTrace();
            result.put("error", "打开商品详情页时出错: " + e.getMessage());
        }

        return result;
    }



    // 删除未使用的方法

    /**
     * 关闭资源
     */
    @Override
    public void close() {
        try {
            // 添加日志，记录关闭开始
            System.out.println("开始关闭AlibabaClient资源...");

            // 关闭所有页面和上下文
            if (context != null) {
                try {
                    // 先关闭所有打开的页面
                    for (Page page : context.pages()) {
                        try {
                            page.close();
                            System.out.println("页面已关闭");
                        } catch (Exception e) {
                            System.out.println("关闭页面时出错: " + e.getMessage());
                        }
                    }

                    // 然后关闭上下文
                    context.close();
                    System.out.println("浏览器上下文已关闭");
                } catch (Exception e) {
                    System.out.println("关闭浏览器上下文时出错: " + e.getMessage());
                }
                context = null;
            }

            if (browser != null) {
                try {
                    browser.close();
                    System.out.println("浏览器已关闭");
                } catch (Exception e) {
                    System.out.println("关闭浏览器时出错: " + e.getMessage());
                }
                browser = null;
            }

            if (playwright != null) {
                try {
                    playwright.close();
                    System.out.println("Playwright已关闭");
                } catch (Exception e) {
                    System.out.println("关闭Playwright时出错: " + e.getMessage());
                }
                playwright = null;
            }

            // 添加强制垃圾回收提示
            System.gc();

            // 不再需要清理临时目录，因为使用固定目录

            System.out.println("AlibabaClient资源已完全关闭");
        } catch (Exception e) {
            System.err.println("关闭AlibabaClient时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
