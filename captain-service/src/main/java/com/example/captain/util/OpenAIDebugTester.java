package com.example.captain.util;

import com.openai.client.OpenAIClient;
import com.openai.client.okhttp.OpenAIOkHttpClient;
import com.openai.models.*;

import java.util.Arrays;

/**
 * OpenAI调试测试工具
 * 用于诊断OpenAI API调用问题
 */
public class OpenAIDebugTester {

    public static void main(String[] args) {
        System.out.println("🔍 OpenAI API调试测试");
        System.out.println("============================================================");
        
        // API密钥
        String apiKey = "********************************************************************************************************************************************************************";
        
        // 步骤1: 测试基本连接
        testBasicConnection(apiKey);
        
        // 步骤2: 测试简单聊天
        testSimpleChat(apiKey);
        
        // 步骤3: 测试Function Call
        testFunctionCall(apiKey);
        
        System.out.println("✅ 调试测试完成！");
    }
    
    private static void testBasicConnection(String apiKey) {
        System.out.println("\n🔗 步骤1: 测试基本连接");
        System.out.println("----------------------------------------");
        
        try {
            OpenAIClient client = OpenAIOkHttpClient.builder()
                    .apiKey(apiKey)
                    .build();
            
            System.out.println("✅ OpenAI客户端创建成功");
            System.out.println("API密钥格式: " + (apiKey.startsWith("sk-") ? "正确" : "错误"));
            System.out.println("API密钥长度: " + apiKey.length());
            
        } catch (Exception e) {
            System.out.println("❌ 客户端创建失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void testSimpleChat(String apiKey) {
        System.out.println("\n💬 步骤2: 测试简单聊天");
        System.out.println("----------------------------------------");
        
        try {
            OpenAIClient client = OpenAIOkHttpClient.builder()
                    .apiKey(apiKey)
                    .build();
            
            System.out.println("🔄 发送简单聊天请求...");
            
            ChatCompletionCreateParams request = ChatCompletionCreateParams.builder()
                    .model("gpt-3.5-turbo")
                    .messages(Arrays.asList(
                            ChatCompletionMessageParam.ofChatCompletionUserMessageParam(
                                    ChatCompletionUserMessageParam.builder()
                                            .content(ChatCompletionUserMessageParam.Content.ofTextContent("Hello, this is a test message."))
                                            .role(ChatCompletionUserMessageParam.Role.USER)
                                            .build()
                            )
                    ))
                    .maxTokens(50)
                    .build();
            
            ChatCompletion response = client.chat().completions().create(request);
            
            if (response.choices() != null && !response.choices().isEmpty()) {
                String content = response.choices().get(0).message().content().orElse("无内容");
                System.out.println("✅ 简单聊天成功");
                System.out.println("响应内容: " + content);
            } else {
                System.out.println("❌ 响应为空");
            }
            
        } catch (Exception e) {
            System.out.println("❌ 简单聊天失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void testFunctionCall(String apiKey) {
        System.out.println("\n🛠️ 步骤3: 测试Function Call");
        System.out.println("----------------------------------------");
        
        try {
            OpenAIClient client = OpenAIOkHttpClient.builder()
                    .apiKey(apiKey)
                    .build();
            
            System.out.println("🔄 发送Function Call请求...");
            
            // 创建简单的Function定义
            FunctionDefinition functionDef = FunctionDefinition.builder()
                    .name("extract_weight")
                    .description("从文本中提取重量信息")
                    .parameters(FunctionParameters.builder()
                            .putAdditionalProperty("type", com.openai.core.JsonValue.from("object"))
                            .putAdditionalProperty("properties", com.openai.core.JsonValue.from(
                                    java.util.Map.of(
                                            "weight", java.util.Map.of(
                                                    "type", "number",
                                                    "description", "重量数值"
                                            ),
                                            "unit", java.util.Map.of(
                                                    "type", "string", 
                                                    "description", "重量单位"
                                            )
                                    )
                            ))
                            .build())
                    .build();
            
            ChatCompletionCreateParams request = ChatCompletionCreateParams.builder()
                    .model("gpt-3.5-turbo")
                    .messages(Arrays.asList(
                            ChatCompletionMessageParam.ofChatCompletionUserMessageParam(
                                    ChatCompletionUserMessageParam.builder()
                                            .content(ChatCompletionUserMessageParam.Content.ofTextContent("这个产品重量是500克"))
                                            .role(ChatCompletionUserMessageParam.Role.USER)
                                            .build()
                            )
                    ))
                    .tools(Arrays.asList(
                            ChatCompletionTool.builder()
                                    .type(ChatCompletionTool.Type.FUNCTION)
                                    .function(functionDef)
                                    .build()
                    ))
                    .toolChoice(ChatCompletionToolChoiceOption.ofChatCompletionNamedToolChoice(
                            ChatCompletionNamedToolChoice.builder()
                                    .type(ChatCompletionNamedToolChoice.Type.FUNCTION)
                                    .function(ChatCompletionNamedToolChoice.Function.builder()
                                            .name("extract_weight")
                                            .build())
                                    .build()
                    ))
                    .maxTokens(100)
                    .build();
            
            ChatCompletion response = client.chat().completions().create(request);
            
            if (response.choices() != null && !response.choices().isEmpty()) {
                ChatCompletionMessage message = response.choices().get(0).message();
                
                if (message.toolCalls().isPresent() && !message.toolCalls().get().isEmpty()) {
                    ChatCompletionMessageToolCall toolCall = message.toolCalls().get().get(0);
                    System.out.println("✅ Function Call成功");
                    System.out.println("函数名: " + toolCall.function().name());
                    System.out.println("参数: " + toolCall.function().arguments());
                } else {
                    System.out.println("❌ 没有Function Call响应");
                    System.out.println("消息内容: " + message.content().orElse("无内容"));
                }
            } else {
                System.out.println("❌ Function Call响应为空");
            }
            
        } catch (Exception e) {
            System.out.println("❌ Function Call失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
