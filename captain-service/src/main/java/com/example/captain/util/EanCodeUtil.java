package com.example.captain.util;

// 移除Spring依赖，使工具类更加独立

/**
 * EAN编码工具类
 * 用于生成和校验EAN-13条形码
 *
 * EAN-13码结构：
 * - 国家代码：前2-3位，标识产品注册国家
 * - 厂商代码：后续4-5位，由各国编码机构分配
 * - 产品代码：厂商代码后的5位，标识具体商品
 * - 校验码：最后1位，用于校验整个编码的正确性
 */
public class EanCodeUtil {

    /**
     * 生成EAN-13编码
     *
     * @param countryCode 国家代码（2-3位）
     * @param manufacturerCode 厂商代码（4-5位）
     * @param productCode 产品代码（5位）
     * @return 完整的EAN-13编码，如果输入无效则返回null
     */
    public static String generateEAN13(String countryCode, String manufacturerCode, String productCode) {
        if (!validateInputCodes(countryCode, manufacturerCode, productCode)) {
            System.err.println("EAN编码输入参数无效: countryCode=" + countryCode +
                             ", manufacturerCode=" + manufacturerCode +
                             ", productCode=" + productCode);
            return null;
        }

        // 组合前12位
        String ean12 = countryCode + manufacturerCode + productCode;

        // 确保总长度为12位
        if (ean12.length() != 12) {
            System.err.println("EAN-12编码长度不正确: " + ean12 + ", 期望长度12位");
            return null;
        }

        return generateEAN13FromEAN12(ean12);
    }

    /**
     * 从12位EAN码生成完整的EAN-13编码（包含校验码）
     *
     * @param ean12 12位EAN码
     * @return 完整的EAN-13编码，如果输入无效则返回null
     */
    public static String generateEAN13FromEAN12(String ean12) {
        if (!isValidEAN12(ean12)) {
            System.err.println("无效的EAN-12编码: " + ean12);
            return null;
        }

        int checkDigit = calculateCheckDigit(ean12);
        return ean12 + checkDigit;
    }

    /**
     * 校验EAN-13编码是否正确
     *
     * @param ean13 EAN-13编码
     * @return 是否有效
     */
    public static boolean validateEAN13(String ean13) {
        if (!isValidEAN13Format(ean13)) {
            return false;
        }

        String ean12 = ean13.substring(0, 12);
        String generatedEAN13 = generateEAN13FromEAN12(ean12);
        return ean13.equals(generatedEAN13);
    }

    /**
     * 计算EAN-13的校验码
     *
     * @param ean12 前12位EAN码
     * @return 校验码（0-9）
     */
    private static int calculateCheckDigit(String ean12) {
        int sum = 0;

        for (int i = 0; i < 12; i++) {
            int digit = Character.getNumericValue(ean12.charAt(i));
            int position = 12 - i; // 从右向左的位置为1-12

            // 奇数位乘3，偶数位乘1（从右向左计算）
            sum += (position % 2 == 1) ? digit * 3 : digit * 1;
        }

        return (10 - (sum % 10)) % 10;
    }

    /**
     * 格式化数字为指定长度的字符串（前面补0）
     *
     * @param number 数字
     * @param length 目标长度
     * @return 格式化后的字符串
     */
    public static String formatNumber(int number, int length) {
        String str = String.valueOf(number);
        while (str.length() < length) {
            str = "0" + str;
        }
        return str;
    }

    /**
     * 批量生成EAN-13编码
     *
     * @param countryCode 国家代码
     * @param manufacturerCode 厂商代码
     * @param startProductCode 起始产品代码
     * @param count 生成数量
     * @return EAN-13编码数组
     */
    public static String[] generateBatchEAN13(String countryCode, String manufacturerCode,
                                            int startProductCode, int count) {
        if (!validateInputCodes(countryCode, manufacturerCode, null)) {
            System.err.println("批量生成EAN编码输入参数无效: countryCode=" + countryCode +
                             ", manufacturerCode=" + manufacturerCode);
            return new String[0];
        }

        String[] eanCodes = new String[count];

        for (int i = 0; i < count; i++) {
            String productCode = formatNumber(startProductCode + i, 5);
            String ean13 = generateEAN13(countryCode, manufacturerCode, productCode);
            eanCodes[i] = ean13;
        }

        return eanCodes;
    }

    /**
     * 验证输入的编码参数是否有效
     */
    private static boolean validateInputCodes(String countryCode, String manufacturerCode, String productCode) {
        // 验证国家代码（2-3位数字）
        if (!hasText(countryCode) || !countryCode.matches("\\d{2,3}")) {
            return false;
        }

        // 验证厂商代码（4-5位数字）
        if (!hasText(manufacturerCode) || !manufacturerCode.matches("\\d{4,5}")) {
            return false;
        }

        // 如果提供了产品代码，验证其格式（5位数字）
        if (productCode != null && (!hasText(productCode) || !productCode.matches("\\d{5}"))) {
            return false;
        }

        // 验证总长度：国家代码 + 厂商代码 + 产品代码 = 12位
        int totalLength = countryCode.length() + manufacturerCode.length() +
                         (productCode != null ? productCode.length() : 5);
        if (totalLength != 12) {
            System.err.println("EAN编码总长度不正确: " + totalLength + ", 期望12位。" +
                             "国家代码(" + countryCode.length() + "位) + " +
                             "厂商代码(" + manufacturerCode.length() + "位) + " +
                             "产品代码(5位) = " + totalLength + "位");
            return false;
        }

        return true;
    }

    /**
     * 验证字符串是否有内容
     */
    private static boolean hasText(String str) {
        return str != null && !str.trim().isEmpty();
    }

    /**
     * 验证EAN-12格式是否正确
     */
    private static boolean isValidEAN12(String ean12) {
        return hasText(ean12) && ean12.matches("\\d{12}");
    }

    /**
     * 验证EAN-13格式是否正确
     */
    private static boolean isValidEAN13Format(String ean13) {
        return hasText(ean13) && ean13.matches("\\d{13}");
    }

    /**
     * 从EAN-13编码中提取各个部分
     *
     * @param ean13 EAN-13编码
     * @return EAN编码信息对象
     */
    public static EanCodeInfo parseEAN13(String ean13) {
        if (!validateEAN13(ean13)) {
            return null;
        }

        // 根据国家代码长度来解析（这里简化处理，假设国家代码为3位）
        String countryCode = ean13.substring(0, 3);
        String manufacturerCode = ean13.substring(3, 8);
        String productCode = ean13.substring(8, 12);
        String checkDigit = ean13.substring(12, 13);

        return EanCodeInfo.builder()
                .countryCode(countryCode)
                .manufacturerCode(manufacturerCode)
                .productCode(productCode)
                .checkDigit(checkDigit)
                .fullCode(ean13)
                .build();
    }

    /**
     * EAN编码信息类
     */
    public static class EanCodeInfo {
        private String countryCode;
        private String manufacturerCode;
        private String productCode;
        private String checkDigit;
        private String fullCode;

        public static EanCodeInfoBuilder builder() {
            return new EanCodeInfoBuilder();
        }

        // Getters
        public String getCountryCode() { return countryCode; }
        public String getManufacturerCode() { return manufacturerCode; }
        public String getProductCode() { return productCode; }
        public String getCheckDigit() { return checkDigit; }
        public String getFullCode() { return fullCode; }

        // Builder pattern
        public static class EanCodeInfoBuilder {
            private String countryCode;
            private String manufacturerCode;
            private String productCode;
            private String checkDigit;
            private String fullCode;

            public EanCodeInfoBuilder countryCode(String countryCode) {
                this.countryCode = countryCode;
                return this;
            }

            public EanCodeInfoBuilder manufacturerCode(String manufacturerCode) {
                this.manufacturerCode = manufacturerCode;
                return this;
            }

            public EanCodeInfoBuilder productCode(String productCode) {
                this.productCode = productCode;
                return this;
            }

            public EanCodeInfoBuilder checkDigit(String checkDigit) {
                this.checkDigit = checkDigit;
                return this;
            }

            public EanCodeInfoBuilder fullCode(String fullCode) {
                this.fullCode = fullCode;
                return this;
            }

            public EanCodeInfo build() {
                EanCodeInfo info = new EanCodeInfo();
                info.countryCode = this.countryCode;
                info.manufacturerCode = this.manufacturerCode;
                info.productCode = this.productCode;
                info.checkDigit = this.checkDigit;
                info.fullCode = this.fullCode;
                return info;
            }
        }

        @Override
        public String toString() {
            return String.format("EanCodeInfo{countryCode='%s', manufacturerCode='%s', productCode='%s', checkDigit='%s', fullCode='%s'}",
                    countryCode, manufacturerCode, productCode, checkDigit, fullCode);
        }
    }
}
