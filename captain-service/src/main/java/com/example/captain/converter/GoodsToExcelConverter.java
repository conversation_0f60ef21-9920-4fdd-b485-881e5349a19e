package com.example.captain.converter;

import com.example.captain.dto.AmazonProductExcelData;
import com.example.captain.entity.Goods;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Goods数据到Excel数据的转换器
 * 将数据库中的Goods实体转换为Excel导出格式
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class GoodsToExcelConverter {

    /**
     * 将Goods实体转换为AmazonProductExcelData
     * @param goods 商品实体
     * @return Excel数据对象
     */
    public AmazonProductExcelData convertToExcelData(Goods goods) {
        if (goods == null) {
            return null;
        }

        AmazonProductExcelData.AmazonProductExcelDataBuilder builder = AmazonProductExcelData.builder();

        // 基本产品信息
        builder.feedProductType(goods.getFeedProductType())
               .itemSku(goods.getItemSku())
               .brandName(goods.getBrandName())
               .externalProductId(goods.getExternalProductId())
               .externalProductIdType(goods.getExternalProductIdType())
               .itemName(goods.getItemName())
               .manufacturer(goods.getManufacturer())
               .recommendedBrowseNodes(goods.getRecommendedBrowseNodes())
               .maxOrderQuantity(goods.getMaxOrderQuantity())
               .countryOfOrigin(goods.getCountryOfOrigin());

        // 价格和库存
        builder.standardPrice(goods.getStandardPrice())
               .quantity(goods.getQuantity())
               .currency(goods.getCurrency())
               .listPrice(goods.getListPrice())
               .listPriceWithTax(goods.getListPriceWithTax());

        // 图片信息
        builder.mainImageUrl(goods.getMainImageUrl())
               .swatchImageUrl(goods.getSwatchImageUrl());

        // 收集其他图片URL
        List<String> otherImageUrls = collectOtherImageUrls(goods);
        if (!otherImageUrls.isEmpty()) {
            builder.otherImageUrls(otherImageUrls);
        }

        // 变体信息
        builder.parentChild(goods.getParentChild())
               .parentSku(goods.getParentSku())
               .relationshipType(goods.getRelationshipType())
               .variationTheme(goods.getVariationTheme());

        // 基本描述信息
        builder.updateDelete(goods.getUpdateDelete())
               .productDescription(goods.getProductDescription())
               .partNumber(goods.getPartNumber());

        // 产品特性
        List<String> bulletPoints = collectBulletPoints(goods);
        if (!bulletPoints.isEmpty()) {
            builder.bulletPoints(bulletPoints);
        }

        // 产品属性
        builder.colorName(goods.getColorName())
               .colorMap(goods.getColorMap())
               .sizeName(goods.getSizeName())
               .departmentName(goods.getDepartmentName());

        // 尺寸和重量
        builder.websiteShippingWeight(goods.getWebsiteShippingWeight())
               .websiteShippingWeightUnitOfMeasure(goods.getWebsiteShippingWeightUnitOfMeasure())
               .sizeMap(goods.getSizeMap())
               .unitCount(goods.getUnitCount())
               .unitCountType(goods.getUnitCountType())
               .depthFrontToBack(goods.getDepthFrontToBack())
               .depthFrontToBackUnitOfMeasure(goods.getDepthFrontToBackUnitOfMeasure())
               .depthWidthSideToSide(goods.getDepthWidthSideToSide())
               .depthWidthSideToSideUnitOfMeasure(goods.getDepthWidthSideToSideUnitOfMeasure())
               .depthHeightFloorToTop(goods.getDepthHeightFloorToTop())
               .depthHeightFloorToTopUnitOfMeasure(goods.getDepthHeightFloorToTopUnitOfMeasure());

        // 履行和价格
        builder.fulfillmentCenterId(goods.getFulfillmentCenterId())
               .conditionType(goods.getConditionType());

        // 合规性信息
        builder.warrantyDescription(goods.getWarrantyDescription())
               .areBatteriesIncluded(goods.getAreBatteriesIncluded())
               .batteriesRequired(goods.getBatteriesRequired())
               .supplierDeclaredDgHzRegulation1(goods.getSupplierDeclaredDgHzRegulation1());

        return builder.build();
    }

    /**
     * 批量转换商品列表
     * @param goodsList 商品列表
     * @return Excel数据列表
     */
    public List<AmazonProductExcelData> convertToExcelDataList(List<Goods> goodsList) {
        if (goodsList == null || goodsList.isEmpty()) {
            return new ArrayList<>();
        }

        return goodsList.stream()
                .map(this::convertToExcelData)
                .filter(data -> data != null)
                .collect(Collectors.toList());
    }

    /**
     * 收集其他图片URL
     * @param goods 商品实体
     * @return 其他图片URL列表
     */
    private List<String> collectOtherImageUrls(Goods goods) {
        List<String> otherImageUrls = new ArrayList<>();

        // 收集8个其他图片URL字段
        addIfNotEmpty(otherImageUrls, goods.getOtherImageUrl1());
        addIfNotEmpty(otherImageUrls, goods.getOtherImageUrl2());
        addIfNotEmpty(otherImageUrls, goods.getOtherImageUrl3());
        addIfNotEmpty(otherImageUrls, goods.getOtherImageUrl4());
        addIfNotEmpty(otherImageUrls, goods.getOtherImageUrl5());
        addIfNotEmpty(otherImageUrls, goods.getOtherImageUrl6());
        addIfNotEmpty(otherImageUrls, goods.getOtherImageUrl7());
        addIfNotEmpty(otherImageUrls, goods.getOtherImageUrl8());

        return otherImageUrls;
    }

    /**
     * 收集产品特性
     * @param goods 商品实体
     * @return 产品特性列表
     */
    private List<String> collectBulletPoints(Goods goods) {
        List<String> bulletPoints = new ArrayList<>();

        // 收集5个产品特性字段
        addIfNotEmpty(bulletPoints, goods.getBulletPoint1());
        addIfNotEmpty(bulletPoints, goods.getBulletPoint2());
        addIfNotEmpty(bulletPoints, goods.getBulletPoint3());
        addIfNotEmpty(bulletPoints, goods.getBulletPoint4());
        addIfNotEmpty(bulletPoints, goods.getBulletPoint5());

        return bulletPoints;
    }

    /**
     * 如果字符串不为空则添加到列表中
     * @param list 目标列表
     * @param value 要添加的值
     */
    private void addIfNotEmpty(List<String> list, String value) {
        if (StringUtils.hasText(value)) {
            list.add(value);
        }
    }
}
