spring:
  application:
    name: worker-service
  # 设置文件编码
  http:
    encoding:
      charset: UTF-8
      enabled: true
      force: true
  # 禁用Web服务器
  main:
    web-application-type: none

# 应用配置
app:
  gateway:
    url: http://10.6.5.198:8080
  websocket:
    url: ws://10.6.5.198:8080/ws/notification
  auth:
    username: admin
    password: Test@123
  topic: shop-001

# 日志配置
logging:
  level:
    root: INFO
    com.example.worker: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} %highlight(%-5level) [%thread] %yellow(%logger{39}) : %msg%n"
  file:
    name: logs/worker-service.log
  charset:
    console: UTF-8
    file: UTF-8
