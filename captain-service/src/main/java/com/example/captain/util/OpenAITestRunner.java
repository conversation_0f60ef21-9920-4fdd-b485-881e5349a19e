package com.example.captain.util;

import com.example.captain.model.ProductDimensionsAndWeight;
import com.example.captain.service.OpenAIProductAnalysisService;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * OpenAI测试运行器
 * 用于快速测试OpenAI Function Call功能
 *
 * 使用方法：
 * 1. 在application.yml中配置您的OpenAI API密钥
 * 2. 运行此类的main方法
 */
public class OpenAITestRunner {

    public static void main(String[] args) {
        System.out.println("🚀 OpenAI商品信息提取测试");
        System.out.println("============================================================");

        // 初始化服务
        ObjectMapper objectMapper = new ObjectMapper();
        // 注意：请在application.yml中配置真实的API密钥
        String apiKey = "********************************************************************************************************************************************************************"; // 请替换为真实的API密钥
        String model = "gpt-4o"; // 使用更智能的模型
        OpenAIProductAnalysisService service = new OpenAIProductAnalysisService(apiKey, objectMapper, model);

        // 测试案例1: 蓝牙耳机
        testBluetoothHeadphones(service);

        // 测试案例2: 笔记本支架
        testLaptopStand(service);

        // 测试案例3: 无线充电器
        testWirelessCharger(service);

        // 测试案例4: 破壁机
        testSmoothieBlender(service);

        System.out.println("✅ 所有测试完成！");
    }

    private static void testBluetoothHeadphones(OpenAIProductAnalysisService service) {
        System.out.println("\n🎧 测试案例1: 蓝牙耳机");
        System.out.println("----------------------------------------");

        String title = "Wireless Bluetooth Headphones - Over Ear Headphones with Microphone, 40H Playtime, Deep Bass, Weight: 0.5kg";
        String feature = "【40H Playtime】: 40 hours wireless mode. Weight: 500g. Dimensions: 20cm x 15cm x 8cm. 【Premium Sound】: Advanced 40mm drivers.";
        String description = "Premium wireless Bluetooth headphones. Package dimensions: 20 x 15 x 8 centimeters. Shipping weight: 500 grams. Comfortable over-ear design.";

        runTest(service, title, feature, description);
    }

    private static void testLaptopStand(OpenAIProductAnalysisService service) {
        System.out.println("\n💻 测试案例2: 笔记本支架");
        System.out.println("----------------------------------------");

        String title = "Adjustable Laptop Stand for Desk, Aluminum Laptop Riser, Ergonomic Computer Stand";
        String feature = "【Ergonomic Design】: 6 adjustable levels. Weight: 1.2kg. Size: 28cm(L) x 22cm(W) x 6cm(H). 【Sturdy】: Premium aluminum alloy.";
        String description = "High-quality aluminum laptop stand. Product dimensions: 28 x 22 x 6 cm. Net weight: 1200g. Supports 10-17.3 inch laptops.";

        runTest(service, title, feature, description);
    }

    private static void testWirelessCharger(OpenAIProductAnalysisService service) {
        System.out.println("\n🔋 测试案例3: 无线充电器");
        System.out.println("----------------------------------------");

        String title = "Wireless Charger, 15W Fast Wireless Charging Pad Compatible with iPhone, Samsung";
        String feature = "【15W Fast Charging】: Up to 15W fast charging. 【Compact】: 8mm thick. Dimensions: 10cm diameter x 0.8cm height. Weight: 150g.";
        String description = "Compact wireless charging pad. Specifications: Diameter 10cm, Height 0.8cm, Weight 150g. LED indicator included.";

        runTest(service, title, feature, description);
    }

    private static void testSmoothieBlender(OpenAIProductAnalysisService service) {
        System.out.println("\n🥤 测试案例4: 破壁机");
        System.out.println("----------------------------------------");

        String title = "Smoothie Maker Blender with Soundproof Cover Silent 1800W 2.5/4.5L Multifunctional Soy Milk Blender,Grey-4.5L";

        String feature = "【1800W High Power Blender】This blender has a powerful 1800W motor that can easily make smoothies, desserts, juices and milkshakes. The upgraded smoothie blender is equipped with food-grade sharp blades for easy chopping of ice, fruits, vegetables and nuts; the high-performance kitchen blender will allow your family to start a wonderful cooking journey every day! " +
                "【Adjustable】This professional commercial mixer has multi-speed adjustable control function, allowing you to make food according to your preference, we specially added a timer to this kitchen mixer so that you can make and enjoy more food. " +
                "【Blender with Soundproof Cover】The smoothie blender has a shielded and silent soundproof cover. Through the design of the all-inclusive soundproof cover, it can block the sound and achieve low-noise and high-volume mixing, so you can stir quietly at home. " +
                "【Suitable for Scenarios】This blender is perfect for any kitchen. It's super easy to use and can help you make any delicious smoothie you can imagine. Whether you want to make a healthy fruit smoothie for breakfast or a creamy milkshake for a friend, you can create delicious drinks with this blender. " +
                "【Our Service】Your needs are our pursuit. We will continue to improve our professional quality and provide you with better services. If you have any questions about the product, please feel free to contact us, we will provide 24 hours feedback and solve it for you as soon as possible.";

        String description = "Specification: Product name: Smoothie blender, Voltage: 100V-240V, Rated power: 1800W, Cup volume: 2.5L, 4.5L, Size: 235*220*560MM. " +
                "Packing list: Smoothie blender*1, User Manual*1. " +
                "Product information Technical Details: Brand ZXFDYF-TEGCUF, Model Number Wall Breaking Machine, Colour Grey, Package Dimensions 61 x 33 x 31 cm; 10 kg, " +
                "Capacity 2500 Millilitres, 4500 Millilitres, Voltage 240 Volts, Number of Speeds 1, Special Features Safety Lock, Item Weight 10 kg, " +
                "Additional Information ASIN B0D4494MV6, Date First Available 14 May 2024.";

        runTest(service, title, feature, description);
    }

    private static void runTest(OpenAIProductAnalysisService service, String title, String feature, String description) {
        // 打印输入
        System.out.println("📝 输入:");
        System.out.println("Title: " + truncate(title, 80));
        System.out.println("Feature: " + truncate(feature, 80));
        System.out.println("Description: " + truncate(description, 80));

        System.out.println("\n🔄 调用OpenAI API...");

        try {
            // 执行提取
            ProductDimensionsAndWeight result = service.extractDimensionsAndWeight(title, feature, description);

            // 打印结果
            System.out.println("\n📊 提取结果:");
            if (result != null && result.hasValidData()) {
                printResult(result);
                System.out.println("✅ 提取成功");
            } else {
                System.out.println("❌ 未提取到有效数据");
            }
        } catch (Exception e) {
            System.out.println("❌ 提取失败: " + e.getMessage());
        }

        System.out.println();
    }

    private static void printResult(ProductDimensionsAndWeight result) {
        // 重量
        if (result.getShippingWeight() != null) {
            System.out.println("🏋️ 重量: " + result.getShippingWeight() + " " +
                (result.getShippingWeightUnitOfMeasure() != null ? result.getShippingWeightUnitOfMeasure() : ""));
        }

        // 尺寸
        boolean hasDimensions = false;
        StringBuilder dimensions = new StringBuilder("📏 尺寸: ");

        if (result.getItemDepthFrontToBack() != null) {
            dimensions.append("长").append(result.getItemDepthFrontToBack());
            if (result.getItemDepthUnit() != null) dimensions.append(result.getItemDepthUnit());
            hasDimensions = true;
        }

        if (result.getItemWidthSideToSide() != null) {
            if (hasDimensions) dimensions.append(" × ");
            dimensions.append("宽").append(result.getItemWidthSideToSide());
            if (result.getItemWidthUnit() != null) dimensions.append(result.getItemWidthUnit());
            hasDimensions = true;
        }

        if (result.getItemHeightFloorToTop() != null) {
            if (hasDimensions) dimensions.append(" × ");
            dimensions.append("高").append(result.getItemHeightFloorToTop());
            if (result.getItemHeightUnitOfMeasure() != null) dimensions.append(result.getItemHeightUnitOfMeasure());
            hasDimensions = true;
        }

        if (hasDimensions) {
            System.out.println(dimensions.toString());
        }
    }

    private static String truncate(String text, int maxLength) {
        if (text == null) return "null";
        if (text.length() <= maxLength) return text;
        return text.substring(0, maxLength - 3) + "...";
    }
}
