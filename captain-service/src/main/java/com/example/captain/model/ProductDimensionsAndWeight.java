package com.example.captain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 商品重量和尺寸信息模型
 * 用于存储从OpenAI提取的商品重量和包装尺寸数据
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductDimensionsAndWeight {

    /**
     * 运输重量
     */
    private BigDecimal shippingWeight;

    /**
     * 运输重量单位
     */
    private String shippingWeightUnitOfMeasure;

    /**
     * 商品深度(前后)
     */
    private BigDecimal itemDepthFrontToBack;

    /**
     * 深度单位
     */
    private String itemDepthUnit;

    /**
     * 商品宽度(左右)
     */
    private BigDecimal itemWidthSideToSide;

    /**
     * 宽度单位
     */
    private String itemWidthUnit;

    /**
     * 商品高度(底部到顶部)
     */
    private BigDecimal itemHeightFloorToTop;

    /**
     * 高度单位
     */
    private String itemHeightUnitOfMeasure;

    /**
     * 检查数据是否有效（至少有一个重量或尺寸信息）
     * @return true如果有有效数据，false如果所有字段都为空
     */
    public boolean hasValidData() {
        return (shippingWeight != null && shippingWeight.compareTo(BigDecimal.ZERO) > 0) ||
               (itemDepthFrontToBack != null && itemDepthFrontToBack.compareTo(BigDecimal.ZERO) > 0) ||
               (itemWidthSideToSide != null && itemWidthSideToSide.compareTo(BigDecimal.ZERO) > 0) ||
               (itemHeightFloorToTop != null && itemHeightFloorToTop.compareTo(BigDecimal.ZERO) > 0);
    }
}
