package com.example.worker.client;

import java.util.Map;

/**
 * AlibabaClient 图片搜索测试类
 * 用于测试阿里巴巴客户端的按图搜索商品功能
 */
public class AlibabaImageSearchTest {

    /**
     * 主方法，用于测试 AlibabaClient 的按图搜索商品功能
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        System.out.println("开始测试 AlibabaClient 按图搜索商品功能...");

        // 解析命令行参数，获取要提取的商品数量
        int maxProductsToExtract = 5; // 默认值
        String imageUrl = "https://m.media-amazon.com/images/I/61RjwGw-dkL.__AC_SX300_SY300_QL70_ML2_.jpg"; // 默认图片URL
        
        // 处理命令行参数
        if (args.length > 0) {
            // 第一个参数是图片URL
            imageUrl = args[0];
            System.out.println("使用图片URL: " + imageUrl);
            
            // 第二个参数是提取商品数量
            if (args.length > 1) {
                try {
                    maxProductsToExtract = Integer.parseInt(args[1]);
                    System.out.println("设置提取商品数量为: " + maxProductsToExtract);
                } catch (NumberFormatException e) {
                    System.err.println("无效的商品数量参数，使用默认值5");
                }
            }
        }

        // 创建 AlibabaClient 实例，设置最大提取商品数量
        try (AlibabaClient client = new AlibabaClient(maxProductsToExtract)) {
            System.out.println("使用图片URL: " + imageUrl);
            System.out.println("将提取最多 " + client.getMaxProductsToExtract() + " 个商品");

            // 调用按图搜索商品方法
            Map<String, Object> result = client.searchProductsByImage(imageUrl);
            
            // 打印搜索结果
            System.out.println("\n搜索结果:");
            if (result.containsKey("error")) {
                System.err.println("搜索出错: " + result.get("error"));
            } else if (result.containsKey("success") && Boolean.TRUE.equals(result.get("success"))) {
                System.out.println("搜索成功!");
                
                // 打印总商品数
                if (result.containsKey("totalCount")) {
                    System.out.println("总商品数: " + result.get("totalCount"));
                }
                
                // 打印提取的商品数据
                if (result.containsKey("results") && result.get("results") instanceof java.util.List) {
                    @SuppressWarnings("unchecked")
                    java.util.List<Map<String, Object>> products = (java.util.List<Map<String, Object>>) result.get("results");
                    
                    System.out.println("提取的商品数: " + products.size());
                    System.out.println("\n商品详情:");
                    
                    for (Map<String, Object> product : products) {
                        System.out.println("----------------------------------------");
                        System.out.println("商品 #" + product.get("index"));
                        System.out.println("ID: " + product.get("objectId"));
                        System.out.println("标题: " + product.get("title"));
                        System.out.println("价格: " + product.get("price"));
                        System.out.println("图片URL: " + product.get("imageUrl"));
                        System.out.println("详情URL: " + product.get("detailUrl"));
                        System.out.println("本地图片路径: " + product.get("localImagePath"));
                    }
                    System.out.println("----------------------------------------");
                } else {
                    System.out.println("未找到商品数据");
                }
            } else {
                System.out.println("搜索未成功，返回结果: " + result);
            }

            System.out.println("\n测试完成");
        } catch (Exception e) {
            System.err.println("测试过程中出错: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
